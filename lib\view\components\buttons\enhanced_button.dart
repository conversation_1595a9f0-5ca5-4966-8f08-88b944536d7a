import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:viserpay/core/utils/dimensions.dart';
import 'package:viserpay/core/utils/my_color.dart';
import 'package:viserpay/core/utils/style.dart';

enum ButtonVariant { primary, secondary, outline, text, danger, success }
enum ButtonSize { small, medium, large }

class EnhancedButton extends StatefulWidget {
  final String text;
  final VoidCallback? onPressed;
  final ButtonVariant variant;
  final ButtonSize size;
  final bool isLoading;
  final bool isDisabled;
  final IconData? icon;
  final bool iconRight;
  final double? width;
  final Color? customColor;
  final bool hasHapticFeedback;

  const EnhancedButton({
    super.key,
    required this.text,
    this.onPressed,
    this.variant = ButtonVariant.primary,
    this.size = ButtonSize.medium,
    this.isLoading = false,
    this.isDisabled = false,
    this.icon,
    this.iconRight = false,
    this.width,
    this.customColor,
    this.hasHapticFeedback = true,
  });

  @override
  State<EnhancedButton> createState() => _EnhancedButtonState();
}

class _EnhancedButtonState extends State<EnhancedButton>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _scaleAnimation;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 150),
      vsync: this,
    );
    _scaleAnimation = Tween<double>(
      begin: 1.0,
      end: 0.95,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  void _handleTapDown(TapDownDetails details) {
    if (!widget.isDisabled && !widget.isLoading) {
      _animationController.forward();
    }
  }

  void _handleTapUp(TapUpDetails details) {
    _animationController.reverse();
  }

  void _handleTapCancel() {
    _animationController.reverse();
  }

  void _handleTap() {
    if (widget.hasHapticFeedback) {
      HapticFeedback.lightImpact();
    }
    widget.onPressed?.call();
  }

  @override
  Widget build(BuildContext context) {
    final buttonStyle = _getButtonStyle();
    final textStyle = _getTextStyle();
    final isInteractive = !widget.isDisabled && !widget.isLoading;

    return AnimatedBuilder(
      animation: _scaleAnimation,
      builder: (context, child) {
        return Transform.scale(
          scale: _scaleAnimation.value,
          child: GestureDetector(
            onTapDown: _handleTapDown,
            onTapUp: _handleTapUp,
            onTapCancel: _handleTapCancel,
            onTap: isInteractive ? _handleTap : null,
            child: AnimatedContainer(
              duration: const Duration(milliseconds: 200),
              width: widget.width,
              padding: _getPadding(),
              decoration: buttonStyle,
              child: Row(
                mainAxisSize: widget.width != null ? MainAxisSize.max : MainAxisSize.min,
                mainAxisAlignment: MainAxisAlignment.center,
                children: _buildButtonContent(textStyle),
              ),
            ),
          ),
        );
      },
    );
  }

  List<Widget> _buildButtonContent(TextStyle textStyle) {
    final List<Widget> children = [];

    if (widget.isLoading) {
      children.add(
        SizedBox(
          width: _getIconSize(),
          height: _getIconSize(),
          child: CircularProgressIndicator(
            strokeWidth: 2,
            valueColor: AlwaysStoppedAnimation<Color>(textStyle.color!),
          ),
        ),
      );
      children.add(const SizedBox(width: Dimensions.space8));
      children.add(
        Text(
          'Loading...',
          style: textStyle,
        ),
      );
    } else {
      if (widget.icon != null && !widget.iconRight) {
        children.add(
          Icon(
            widget.icon,
            size: _getIconSize(),
            color: textStyle.color,
          ),
        );
        children.add(const SizedBox(width: Dimensions.space8));
      }

      children.add(
        Text(
          widget.text,
          style: textStyle,
          textAlign: TextAlign.center,
        ),
      );

      if (widget.icon != null && widget.iconRight) {
        children.add(const SizedBox(width: Dimensions.space8));
        children.add(
          Icon(
            widget.icon,
            size: _getIconSize(),
            color: textStyle.color,
          ),
        );
      }
    }

    return children;
  }

  BoxDecoration _getButtonStyle() {
    final isInteractive = !widget.isDisabled && !widget.isLoading;
    final opacity = isInteractive ? 1.0 : 0.6;

    switch (widget.variant) {
      case ButtonVariant.primary:
        return BoxDecoration(
          gradient: MyColor.primaryGradient,
          borderRadius: BorderRadius.circular(_getBorderRadius()),
          boxShadow: isInteractive ? [
            BoxShadow(
              offset: const Offset(0, 4),
              blurRadius: 12,
              color: MyColor.primaryColor.withOpacity(0.3),
            ),
          ] : null,
        ).copyWith(
          gradient: MyColor.primaryGradient.transform(
            Matrix4.identity()..scale(opacity),
          ),
        );

      case ButtonVariant.secondary:
        return BoxDecoration(
          color: MyColor.brandWhite.withOpacity(opacity),
          borderRadius: BorderRadius.circular(_getBorderRadius()),
          border: Border.all(
            color: MyColor.primaryColor.withOpacity(opacity),
            width: 2,
          ),
          boxShadow: isInteractive ? [
            BoxShadow(
              offset: const Offset(0, 2),
              blurRadius: 8,
              color: MyColor.brandBlack.withOpacity(0.1),
            ),
          ] : null,
        );

      case ButtonVariant.outline:
        return BoxDecoration(
          color: Colors.transparent,
          borderRadius: BorderRadius.circular(_getBorderRadius()),
          border: Border.all(
            color: MyColor.borderColor.withOpacity(opacity),
            width: 1,
          ),
        );

      case ButtonVariant.text:
        return BoxDecoration(
          color: Colors.transparent,
          borderRadius: BorderRadius.circular(_getBorderRadius()),
        );

      case ButtonVariant.danger:
        return BoxDecoration(
          color: MyColor.colorRed.withOpacity(opacity),
          borderRadius: BorderRadius.circular(_getBorderRadius()),
          boxShadow: isInteractive ? [
            BoxShadow(
              offset: const Offset(0, 4),
              blurRadius: 12,
              color: MyColor.colorRed.withOpacity(0.3),
            ),
          ] : null,
        );

      case ButtonVariant.success:
        return BoxDecoration(
          color: MyColor.colorGreen.withOpacity(opacity),
          borderRadius: BorderRadius.circular(_getBorderRadius()),
          boxShadow: isInteractive ? [
            BoxShadow(
              offset: const Offset(0, 4),
              blurRadius: 12,
              color: MyColor.colorGreen.withOpacity(0.3),
            ),
          ] : null,
        );
    }
  }

  TextStyle _getTextStyle() {
    final baseStyle = _getBaseTextStyle();
    final isInteractive = !widget.isDisabled && !widget.isLoading;

    Color textColor;
    switch (widget.variant) {
      case ButtonVariant.primary:
      case ButtonVariant.danger:
      case ButtonVariant.success:
        textColor = MyColor.brandWhite;
        break;
      case ButtonVariant.secondary:
        textColor = MyColor.primaryColor;
        break;
      case ButtonVariant.outline:
      case ButtonVariant.text:
        textColor = MyColor.primaryTextColor;
        break;
    }

    return baseStyle.copyWith(
      color: textColor.withOpacity(isInteractive ? 1.0 : 0.6),
    );
  }

  TextStyle _getBaseTextStyle() {
    switch (widget.size) {
      case ButtonSize.small:
        return mediumSmall;
      case ButtonSize.medium:
        return mediumDefault;
      case ButtonSize.large:
        return mediumLarge;
    }
  }

  EdgeInsets _getPadding() {
    switch (widget.size) {
      case ButtonSize.small:
        return const EdgeInsets.symmetric(
          horizontal: Dimensions.space12,
          vertical: Dimensions.space8,
        );
      case ButtonSize.medium:
        return const EdgeInsets.symmetric(
          horizontal: Dimensions.space20,
          vertical: Dimensions.space12,
        );
      case ButtonSize.large:
        return const EdgeInsets.symmetric(
          horizontal: Dimensions.space25,
          vertical: Dimensions.space16,
        );
    }
  }

  double _getBorderRadius() {
    switch (widget.size) {
      case ButtonSize.small:
        return Dimensions.buttonRadius;
      case ButtonSize.medium:
        return Dimensions.mediumRadius;
      case ButtonSize.large:
        return Dimensions.largeRadius;
    }
  }

  double _getIconSize() {
    switch (widget.size) {
      case ButtonSize.small:
        return 16;
      case ButtonSize.medium:
        return 18;
      case ButtonSize.large:
        return 20;
    }
  }
}
