class CartResponseModel {
  String? status;
  Message? message;
  CartData? data;

  CartResponseModel({
    this.status,
    this.message,
    this.data,
  });

  factory CartResponseModel.fromJson(Map<String, dynamic> json) => CartResponseModel(
        status: json["status"],
        message: json["message"] == null ? null : Message.fromJson(json["message"]),
        data: json["data"] == null ? null : CartData.fromJson(json["data"]),
      );

  Map<String, dynamic> toJson() => {
        "status": status,
        "message": message?.toJson(),
        "data": data?.toJson(),
      };
}

class Message {
  List<String>? success;
  List<String>? error;

  Message({
    this.success,
    this.error,
  });

  factory Message.fromJson(Map<String, dynamic> json) => Message(
        success: json["success"] == null ? [] : List<String>.from(json["success"]!.map((x) => x)),
        error: json["error"] == null ? [] : List<String>.from(json["error"]!.map((x) => x)),
      );

  Map<String, dynamic> toJson() => {
        "success": success == null ? [] : List<dynamic>.from(success!.map((x) => x)),
        "error": error == null ? [] : List<dynamic>.from(error!.map((x) => x)),
      };
}

class CartData {
  List<CartItem>? items;
  CartSummary? summary;

  CartData({
    this.items,
    this.summary,
  });

  factory CartData.fromJson(Map<String, dynamic> json) => CartData(
        items: json["items"] == null ? [] : List<CartItem>.from(json["items"]!.map((x) => CartItem.fromJson(x))),
        summary: json["summary"] == null ? null : CartSummary.fromJson(json["summary"]),
      );

  Map<String, dynamic> toJson() => {
        "items": items == null ? [] : List<dynamic>.from(items!.map((x) => x.toJson())),
        "summary": summary?.toJson(),
      };
}

class CartItem {
  int? id;
  int? userId;
  int? productId;
  int? variantId;
  int? quantity;
  String? price;
  String? totalPrice;
  Product? product;
  ProductVariant? variant;
  String? addedAt;

  CartItem({
    this.id,
    this.userId,
    this.productId,
    this.variantId,
    this.quantity,
    this.price,
    this.totalPrice,
    this.product,
    this.variant,
    this.addedAt,
  });

  factory CartItem.fromJson(Map<String, dynamic> json) => CartItem(
        id: json["id"],
        userId: json["user_id"],
        productId: json["product_id"],
        variantId: json["variant_id"],
        quantity: json["quantity"],
        price: json["price"],
        totalPrice: json["total_price"],
        product: json["product"] == null ? null : Product.fromJson(json["product"]),
        variant: json["variant"] == null ? null : ProductVariant.fromJson(json["variant"]),
        addedAt: json["added_at"],
      );

  Map<String, dynamic> toJson() => {
        "id": id,
        "user_id": userId,
        "product_id": productId,
        "variant_id": variantId,
        "quantity": quantity,
        "price": price,
        "total_price": totalPrice,
        "product": product?.toJson(),
        "variant": variant?.toJson(),
        "added_at": addedAt,
      };
}

class Product {
  int? id;
  String? name;
  String? featuredImage;
  String? price;
  int? quantity;
  String? status;
  Store? store;

  Product({
    this.id,
    this.name,
    this.featuredImage,
    this.price,
    this.quantity,
    this.status,
    this.store,
  });

  factory Product.fromJson(Map<String, dynamic> json) => Product(
        id: json["id"],
        name: json["name"],
        featuredImage: json["featured_image"],
        price: json["price"],
        quantity: json["quantity"],
        status: json["status"],
        store: json["store"] == null ? null : Store.fromJson(json["store"]),
      );

  Map<String, dynamic> toJson() => {
        "id": id,
        "name": name,
        "featured_image": featuredImage,
        "price": price,
        "quantity": quantity,
        "status": status,
        "store": store?.toJson(),
      };
}

class ProductVariant {
  int? id;
  String? name;
  String? value;
  String? price;
  int? quantity;
  String? image;

  ProductVariant({
    this.id,
    this.name,
    this.value,
    this.price,
    this.quantity,
    this.image,
  });

  factory ProductVariant.fromJson(Map<String, dynamic> json) => ProductVariant(
        id: json["id"],
        name: json["name"],
        value: json["value"],
        price: json["price"],
        quantity: json["quantity"],
        image: json["image"],
      );

  Map<String, dynamic> toJson() => {
        "id": id,
        "name": name,
        "value": value,
        "price": price,
        "quantity": quantity,
        "image": image,
      };
}

class Store {
  int? id;
  String? name;
  String? logo;
  String? address;
  String? phone;
  bool? isVerified;

  Store({
    this.id,
    this.name,
    this.logo,
    this.address,
    this.phone,
    this.isVerified,
  });

  factory Store.fromJson(Map<String, dynamic> json) => Store(
        id: json["id"],
        name: json["name"],
        logo: json["logo"],
        address: json["address"],
        phone: json["phone"],
        isVerified: json["is_verified"] == 1,
      );

  Map<String, dynamic> toJson() => {
        "id": id,
        "name": name,
        "logo": logo,
        "address": address,
        "phone": phone,
        "is_verified": isVerified == true ? 1 : 0,
      };
}

class CartSummary {
  int? totalItems;
  String? subtotal;
  String? tax;
  String? shipping;
  String? discount;
  String? total;
  List<String>? appliedCoupons;

  CartSummary({
    this.totalItems,
    this.subtotal,
    this.tax,
    this.shipping,
    this.discount,
    this.total,
    this.appliedCoupons,
  });

  factory CartSummary.fromJson(Map<String, dynamic> json) => CartSummary(
        totalItems: json["total_items"],
        subtotal: json["subtotal"],
        tax: json["tax"],
        shipping: json["shipping"],
        discount: json["discount"],
        total: json["total"],
        appliedCoupons: json["applied_coupons"] == null ? [] : List<String>.from(json["applied_coupons"]!.map((x) => x)),
      );

  Map<String, dynamic> toJson() => {
        "total_items": totalItems,
        "subtotal": subtotal,
        "tax": tax,
        "shipping": shipping,
        "discount": discount,
        "total": total,
        "applied_coupons": appliedCoupons == null ? [] : List<dynamic>.from(appliedCoupons!.map((x) => x)),
      };
}

// Add to cart request model
class AddToCartRequest {
  int productId;
  int? variantId;
  int quantity;

  AddToCartRequest({
    required this.productId,
    this.variantId,
    required this.quantity,
  });

  Map<String, dynamic> toJson() => {
        "product_id": productId,
        if (variantId != null) "variant_id": variantId,
        "quantity": quantity,
      };
}

// Update cart item request model
class UpdateCartItemRequest {
  int cartItemId;
  int quantity;

  UpdateCartItemRequest({
    required this.cartItemId,
    required this.quantity,
  });

  Map<String, dynamic> toJson() => {
        "cart_item_id": cartItemId,
        "quantity": quantity,
      };
}

// Apply coupon request model
class ApplyCouponRequest {
  String couponCode;

  ApplyCouponRequest({
    required this.couponCode,
  });

  Map<String, dynamic> toJson() => {
        "coupon_code": couponCode,
      };
}
