# KojaPay Flutter App - Android Studio Setup Guide

## 🚀 **Complete Setup Instructions for Android Studio**

Follow this comprehensive guide to set up and run the KojaPay Flutter app in Android Studio with all the enhanced insights features.

---

## 📋 **Prerequisites**

### **1. System Requirements**
- **Operating System**: Windows 10/11, macOS 10.14+, or Ubuntu 18.04+
- **RAM**: Minimum 8GB (16GB recommended)
- **Storage**: At least 10GB free space
- **Internet**: Stable connection for downloads

### **2. Required Software**
- **Android Studio**: Latest version (Flamingo or newer)
- **Flutter SDK**: Latest stable version (3.10.0+)
- **Dart SDK**: Included with Flutter
- **Git**: For version control

---

## 🛠️ **Step 1: Install Flutter SDK**

### **Windows:**
```bash
# Download Flutter SDK from https://flutter.dev/docs/get-started/install/windows
# Extract to C:\flutter
# Add C:\flutter\bin to your PATH environment variable

# Verify installation
flutter doctor
```

### **macOS:**
```bash
# Using Homebrew (recommended)
brew install flutter

# Or download from https://flutter.dev/docs/get-started/install/macos
# Extract and add to PATH

# Verify installation
flutter doctor
```

### **Linux:**
```bash
# Download Flutter SDK
wget https://storage.googleapis.com/flutter_infra_release/releases/stable/linux/flutter_linux_3.13.0-stable.tar.xz

# Extract
tar xf flutter_linux_3.13.0-stable.tar.xz

# Add to PATH
export PATH="$PATH:`pwd`/flutter/bin"

# Verify installation
flutter doctor
```

---

## 🔧 **Step 2: Install Android Studio**

### **Download and Install:**
1. Go to [Android Studio Download Page](https://developer.android.com/studio)
2. Download the latest version for your OS
3. Run the installer and follow setup wizard
4. Install Android SDK, Android SDK Platform-Tools, and Android SDK Build-Tools

### **Configure Android Studio:**
1. Open Android Studio
2. Go to **File > Settings** (Windows/Linux) or **Android Studio > Preferences** (macOS)
3. Navigate to **Plugins**
4. Install **Flutter** and **Dart** plugins
5. Restart Android Studio

---

## 📱 **Step 3: Set Up Android Emulator**

### **Create Virtual Device:**
1. Open Android Studio
2. Go to **Tools > AVD Manager**
3. Click **Create Virtual Device**
4. Select **Phone** category
5. Choose **Pixel 7** or **Pixel 6** (recommended)
6. Select **API Level 33** (Android 13) or **API Level 34** (Android 14)
7. Click **Next** and **Finish**

### **Start Emulator:**
1. In AVD Manager, click the **Play** button next to your virtual device
2. Wait for the emulator to boot up completely

---

## 📂 **Step 4: Clone and Setup KojaPay Project**

### **Clone Repository:**
```bash
# Clone the project (replace with actual repository URL)
git clone https://github.com/your-username/kojapay-flutter.git
cd kojapay-flutter
```

### **Install Dependencies:**
```bash
# Get Flutter packages
flutter pub get

# Clean and rebuild
flutter clean
flutter pub get
```

---

## ⚙️ **Step 5: Configure Project Settings**

### **1. Update pubspec.yaml Dependencies:**
```yaml
dependencies:
  flutter:
    sdk: flutter
  get: ^4.6.6
  http: ^1.1.0
  shared_preferences: ^2.2.2
  flutter_secure_storage: ^9.0.0
  image_picker: ^1.0.4
  permission_handler: ^11.0.1
  url_launcher: ^6.2.1
  intl: ^0.18.1
  cached_network_image: ^3.3.0
  flutter_svg: ^2.0.9
  pin_code_fields: ^8.0.1
  local_auth: ^2.1.7
  device_info_plus: ^9.1.1
  package_info_plus: ^4.2.0
  connectivity_plus: ^5.0.1
  path_provider: ^2.1.1
  sqflite: ^2.3.0
  google_maps_flutter: ^2.5.0
  location: ^5.0.3
  qr_code_scanner: ^1.0.1
  qr_flutter: ^4.1.0
  file_picker: ^6.1.1
  open_file: ^3.3.2
  share_plus: ^7.2.1
  fl_chart: ^0.65.0
  lottie: ^2.7.0

dev_dependencies:
  flutter_test:
    sdk: flutter
  flutter_lints: ^3.0.1
  build_runner: ^2.4.7
```

### **2. Configure Android Permissions:**
Update `android/app/src/main/AndroidManifest.xml`:
```xml
<manifest xmlns:android="http://schemas.android.com/apk/res/android">
    <!-- Internet Permission -->
    <uses-permission android:name="android.permission.INTERNET" />
    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
    
    <!-- Location Permissions -->
    <uses-permission android:name="android.permission.ACCESS_FINE_LOCATION" />
    <uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION" />
    
    <!-- Camera Permission -->
    <uses-permission android:name="android.permission.CAMERA" />
    
    <!-- Storage Permissions -->
    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
    
    <!-- Biometric Permission -->
    <uses-permission android:name="android.permission.USE_FINGERPRINT" />
    <uses-permission android:name="android.permission.USE_BIOMETRIC" />
    
    <!-- Phone Permission -->
    <uses-permission android:name="android.permission.READ_PHONE_STATE" />

    <application
        android:label="KojaPay"
        android:name="${applicationName}"
        android:icon="@mipmap/ic_launcher">
        
        <!-- Main Activity -->
        <activity
            android:name=".MainActivity"
            android:exported="true"
            android:launchMode="singleTop"
            android:theme="@style/LaunchTheme"
            android:configChanges="orientation|keyboardHidden|keyboard|screenSize|smallestScreenSize|locale|layoutDirection|fontScale|screenLayout|density|uiMode"
            android:hardwareAccelerated="true"
            android:windowSoftInputMode="adjustResize">
            
            <meta-data
              android:name="io.flutter.embedding.android.NormalTheme"
              android:resource="@style/NormalTheme" />
              
            <intent-filter android:autoVerify="true">
                <action android:name="android.intent.action.MAIN"/>
                <category android:name="android.intent.category.LAUNCHER"/>
            </intent-filter>
        </activity>
        
        <!-- Google Maps API Key -->
        <meta-data android:name="com.google.android.geo.API_KEY"
                   android:value="YOUR_GOOGLE_MAPS_API_KEY"/>
                   
        <meta-data
            android:name="flutterEmbedding"
            android:value="2" />
    </application>
</manifest>
```

### **3. Configure iOS Settings (if needed):**
Update `ios/Runner/Info.plist`:
```xml
<key>NSLocationWhenInUseUsageDescription</key>
<string>This app needs location access to find nearby agents and stores.</string>
<key>NSCameraUsageDescription</key>
<string>This app needs camera access to scan QR codes for payments.</string>
<key>NSPhotoLibraryUsageDescription</key>
<string>This app needs photo library access to upload documents.</string>
<key>NSFaceIDUsageDescription</key>
<string>This app uses Face ID for secure authentication.</string>
```

---

## 🎯 **Step 6: Configure API Endpoints**

### **Update Base URL:**
Edit `lib/core/utils/url_container.dart`:
```dart
class UrlContainer {
  // Development Environment
  static const String baseUrl = "https://api-dev.kojapay.com/api/v1/";
  
  // Production Environment (uncomment for production)
  // static const String baseUrl = "https://api.kojapay.com/api/v1/";
  
  // Local Development (uncomment for local testing)
  // static const String baseUrl = "http://********:8000/api/v1/"; // Android Emulator
  // static const String baseUrl = "http://localhost:8000/api/v1/"; // iOS Simulator
}
```

### **Configure Environment Variables:**
Create `lib/core/config/app_config.dart`:
```dart
class AppConfig {
  static const String appName = "KojaPay";
  static const String appVersion = "1.0.0";
  static const bool isProduction = false;
  
  // API Configuration
  static const String apiBaseUrl = "https://api-dev.kojapay.com/api/v1/";
  static const int apiTimeout = 30000; // 30 seconds
  
  // BankOne API Configuration
  static const String bankOneApiKey = "YOUR_BANKONE_API_KEY";
  static const String bankOneSecretKey = "YOUR_BANKONE_SECRET_KEY";
  static const bool bankOneProduction = false;
  
  // Google Maps Configuration
  static const String googleMapsApiKey = "YOUR_GOOGLE_MAPS_API_KEY";
  
  // Paystack Configuration
  static const String paystackPublicKey = "YOUR_PAYSTACK_PUBLIC_KEY";
  static const String paystackSecretKey = "YOUR_PAYSTACK_SECRET_KEY";
}
```

---

## 🏃‍♂️ **Step 7: Run the Application**

### **Method 1: Using Android Studio**
1. Open Android Studio
2. Click **File > Open** and select the KojaPay project folder
3. Wait for project to sync and index
4. Ensure your emulator is running or device is connected
5. Click the **Run** button (green play icon) or press **Shift + F10**

### **Method 2: Using Command Line**
```bash
# Check connected devices
flutter devices

# Run on emulator/device
flutter run

# Run in debug mode with hot reload
flutter run --debug

# Run in release mode
flutter run --release
```

### **Method 3: Using VS Code (Alternative)**
1. Install Flutter and Dart extensions
2. Open project in VS Code
3. Press **F5** or **Ctrl + F5** to run

---

## 🔍 **Step 8: Verify Installation**

### **Check Flutter Doctor:**
```bash
flutter doctor -v
```

### **Expected Output:**
```
[✓] Flutter (Channel stable, 3.13.0, on Microsoft Windows)
[✓] Windows Version (Installed version of Windows is 10 or higher)
[✓] Android toolchain - develop for Android devices (Android SDK version 34.0.0)
[✓] Chrome - develop for the web
[✓] Visual Studio - develop for Windows (Visual Studio Community 2022)
[✓] Android Studio (version 2023.1)
[✓] VS Code (version 1.84.0)
[✓] Connected device (2 available)
[✓] Network resources
```

---

## 🐛 **Troubleshooting Common Issues**

### **1. Flutter Doctor Issues:**
```bash
# Accept Android licenses
flutter doctor --android-licenses

# Update Flutter
flutter upgrade

# Clear cache
flutter clean
flutter pub get
```

### **2. Gradle Build Issues:**
```bash
# Clean and rebuild
cd android
./gradlew clean
cd ..
flutter clean
flutter pub get
flutter run
```

### **3. Emulator Issues:**
- Ensure virtualization is enabled in BIOS
- Allocate sufficient RAM to emulator (4GB+)
- Use x86_64 images for better performance
- Enable hardware acceleration

### **4. Plugin Issues:**
```bash
# Reinstall dependencies
flutter pub deps
flutter pub get
flutter pub upgrade
```

---

## 📊 **Step 9: Test Insights Features**

### **Navigate to Insights:**
1. Launch the app
2. Complete login/registration
3. Go to **Home Screen**
4. Tap on **Insights** or **Analytics** button
5. Verify all tabs work: Overview, Spending, Savings, Income, Goals

### **Test Responsive Design:**
1. Rotate device/emulator to test landscape mode
2. Test on different screen sizes
3. Verify charts and graphs render correctly
4. Test pull-to-refresh functionality

---

## 🎨 **Step 10: Customize and Extend**

### **Add Custom Colors:**
Edit `lib/core/utils/my_color.dart` to match your brand colors.

### **Modify Insights:**
- Add new chart types in `chart_widget.dart`
- Create custom insight cards
- Implement real-time data updates
- Add export functionality

### **Backend Integration:**
- Update API endpoints in repositories
- Implement proper error handling
- Add offline data caching
- Set up push notifications

---

## 🚀 **Production Deployment**

### **Build Release APK:**
```bash
# Build APK
flutter build apk --release

# Build App Bundle (recommended for Play Store)
flutter build appbundle --release
```

### **Build for iOS:**
```bash
# Build iOS app
flutter build ios --release
```

---

## 📞 **Support and Resources**

### **Documentation:**
- [Flutter Documentation](https://flutter.dev/docs)
- [Dart Documentation](https://dart.dev/guides)
- [Android Studio Guide](https://developer.android.com/studio/intro)

### **Community:**
- [Flutter Community](https://flutter.dev/community)
- [Stack Overflow](https://stackoverflow.com/questions/tagged/flutter)
- [GitHub Issues](https://github.com/flutter/flutter/issues)

---

## ✅ **Final Checklist**

- [ ] Flutter SDK installed and configured
- [ ] Android Studio with Flutter/Dart plugins
- [ ] Android emulator created and running
- [ ] Project dependencies installed (`flutter pub get`)
- [ ] API endpoints configured
- [ ] Permissions added to AndroidManifest.xml
- [ ] App runs successfully on emulator/device
- [ ] Insights features working correctly
- [ ] Charts and graphs rendering properly
- [ ] Responsive design tested

**🎉 Congratulations! Your KojaPay Flutter app with enhanced insights is now ready for development and testing!**
