{"name": "webmozart/assert", "description": "Assertions to validate method input/output with nice error messages.", "license": "MIT", "keywords": ["assert", "check", "validate"], "authors": [{"name": "<PERSON>", "email": "b<PERSON><PERSON><PERSON>@gmail.com"}], "require": {"php": "^7.2 || ^8.0", "ext-ctype": "*"}, "require-dev": {"phpunit/phpunit": "^8.5.13"}, "conflict": {"phpstan/phpstan": "<0.12.20", "vimeo/psalm": "<4.6.1 || 4.6.2"}, "autoload": {"psr-4": {"Webmozart\\Assert\\": "src/"}}, "autoload-dev": {"psr-4": {"Webmozart\\Assert\\Tests\\": "tests/", "Webmozart\\Assert\\Bin\\": "bin/src"}}, "extra": {"branch-alias": {"dev-master": "1.10-dev"}}}