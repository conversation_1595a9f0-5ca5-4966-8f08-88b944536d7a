<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\User;
use App\Models\Transaction;
use App\Models\SavingsTransaction;
use App\Models\InvestmentTransaction;
use App\Models\FixedDeposit;
use App\Models\TargetSaving;
use App\Models\FlexSaving;
use App\Models\UserInvestment;
use App\Models\UserLoan;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Auth;
use Carbon\Carbon;

class InsightsController extends Controller
{
    public function __construct()
    {
        $this->middleware('auth:api');
    }

    /**
     * Get comprehensive financial overview
     */
    public function overview(): JsonResponse
    {
        $user = Auth::user();
        
        $overview = [
            'financial_health_score' => $this->calculateFinancialHealthScore($user),
            'net_worth' => $this->calculateNetWorth($user),
            'monthly_cash_flow' => $this->calculateMonthlyCashFlow($user),
            'savings_rate' => $this->calculateSavingsRate($user),
            'investment_allocation' => $this->getInvestmentAllocation($user),
            'debt_to_income_ratio' => $this->calculateDebtToIncomeRatio($user),
            'emergency_fund_months' => $this->calculateEmergencyFundMonths($user),
            'financial_goals_progress' => $this->getFinancialGoalsProgress($user)
        ];

        return response()->json([
            'status' => 'success',
            'data' => $overview
        ]);
    }

    /**
     * Get spending analytics
     */
    public function spendingAnalytics(Request $request): JsonResponse
    {
        $user = Auth::user();
        $period = $request->get('period', 30); // days
        
        $startDate = Carbon::now()->subDays($period);
        
        $transactions = Transaction::where('user_id', $user->id)
                                 ->where('trx_type', '-')
                                 ->where('created_at', '>=', $startDate)
                                 ->get();

        $analytics = [
            'total_spending' => $transactions->sum('amount'),
            'transaction_count' => $transactions->count(),
            'average_transaction' => $transactions->avg('amount'),
            'daily_average' => $transactions->sum('amount') / max(1, $period),
            'spending_by_category' => $this->getSpendingByCategory($transactions),
            'spending_trend' => $this->getSpendingTrend($user, $period),
            'top_merchants' => $this->getTopMerchants($transactions),
            'spending_patterns' => $this->getSpendingPatterns($transactions),
            'budget_analysis' => $this->getBudgetAnalysis($user, $transactions)
        ];

        return response()->json([
            'status' => 'success',
            'data' => $analytics
        ]);
    }

    /**
     * Get savings analytics
     */
    public function savingsAnalytics(Request $request): JsonResponse
    {
        $user = Auth::user();
        $period = $request->get('period', 30);
        
        $analytics = [
            'total_savings' => $user->savings_balance,
            'savings_growth' => $this->getSavingsGrowth($user, $period),
            'interest_earned' => $this->getTotalInterestEarned($user, $period),
            'savings_by_product' => [
                'fixed_deposits' => FixedDeposit::forUser($user->id)->sum('amount'),
                'target_savings' => TargetSaving::forUser($user->id)->sum('saved_amount'),
                'flex_savings' => FlexSaving::forUser($user->id)->sum('balance'),
                'group_savings' => $this->getGroupSavingsTotal($user)
            ],
            'savings_performance' => $this->getSavingsPerformance($user),
            'goal_completion_rate' => $this->getGoalCompletionRate($user),
            'auto_save_efficiency' => $this->getAutoSaveEfficiency($user),
            'projected_savings' => $this->getProjectedSavings($user)
        ];

        return response()->json([
            'status' => 'success',
            'data' => $analytics
        ]);
    }

    /**
     * Get income analytics
     */
    public function incomeAnalytics(Request $request): JsonResponse
    {
        $user = Auth::user();
        $period = $request->get('period', 30);
        
        $startDate = Carbon::now()->subDays($period);
        
        $incomeTransactions = Transaction::where('user_id', $user->id)
                                       ->where('trx_type', '+')
                                       ->where('created_at', '>=', $startDate)
                                       ->get();

        $analytics = [
            'total_income' => $incomeTransactions->sum('amount'),
            'income_sources' => $this->getIncomeSources($incomeTransactions),
            'income_trend' => $this->getIncomeTrend($user, $period),
            'income_stability' => $this->calculateIncomeStability($user),
            'passive_income' => $this->getPassiveIncome($user, $period),
            'income_vs_expenses' => $this->getIncomeVsExpenses($user, $period),
            'monthly_income_projection' => $this->getMonthlyIncomeProjection($user)
        ];

        return response()->json([
            'status' => 'success',
            'data' => $analytics
        ]);
    }

    /**
     * Get financial goals analytics
     */
    public function goalsAnalytics(): JsonResponse
    {
        $user = Auth::user();
        
        $targetSavings = TargetSaving::forUser($user->id)->get();
        
        $analytics = [
            'active_goals' => $targetSavings->where('status', 'active')->count(),
            'completed_goals' => $targetSavings->where('status', 'completed')->count(),
            'total_goal_amount' => $targetSavings->sum('target_amount'),
            'total_saved_towards_goals' => $targetSavings->sum('saved_amount'),
            'overall_progress' => $this->calculateOverallGoalProgress($targetSavings),
            'goals_on_track' => $this->getGoalsOnTrack($targetSavings),
            'goals_behind_schedule' => $this->getGoalsBehindSchedule($targetSavings),
            'average_goal_completion_time' => $this->getAverageGoalCompletionTime($user),
            'goal_recommendations' => $this->getGoalRecommendations($user)
        ];

        return response()->json([
            'status' => 'success',
            'data' => $analytics
        ]);
    }

    /**
     * Calculate financial health score
     */
    public function financialHealthScore(): JsonResponse
    {
        $user = Auth::user();
        
        $score = $this->calculateFinancialHealthScore($user);
        $breakdown = $this->getFinancialHealthBreakdown($user);
        $recommendations = $this->getFinancialHealthRecommendations($user, $score);

        return response()->json([
            'status' => 'success',
            'data' => [
                'score' => $score,
                'grade' => $this->getScoreGrade($score),
                'breakdown' => $breakdown,
                'recommendations' => $recommendations,
                'improvement_tips' => $this->getImprovementTips($score)
            ]
        ]);
    }

    // Private helper methods
    private function calculateFinancialHealthScore($user): int
    {
        $score = 0;
        
        // Savings rate (30 points)
        $savingsRate = $this->calculateSavingsRate($user);
        $score += min(30, ($savingsRate / 20) * 30); // 20% savings rate = full points
        
        // Emergency fund (25 points)
        $emergencyMonths = $this->calculateEmergencyFundMonths($user);
        $score += min(25, ($emergencyMonths / 6) * 25); // 6 months = full points
        
        // Debt to income ratio (20 points)
        $debtRatio = $this->calculateDebtToIncomeRatio($user);
        $score += max(0, 20 - ($debtRatio / 5)); // Lower debt = higher score
        
        // Investment diversification (15 points)
        $diversification = $this->calculateInvestmentDiversification($user);
        $score += $diversification * 15;
        
        // Credit score (10 points)
        $creditScore = $user->credit_score ?? 500;
        $score += min(10, (($creditScore - 300) / 550) * 10);
        
        return min(100, max(0, round($score)));
    }

    private function calculateNetWorth($user): float
    {
        $assets = $user->balance + $user->savings_balance + $user->investment_balance;
        $liabilities = $user->loan_balance ?? 0;
        
        return $assets - $liabilities;
    }

    private function calculateMonthlyCashFlow($user): array
    {
        $monthlyIncome = $this->getMonthlyIncome($user);
        $monthlyExpenses = $this->getMonthlyExpenses($user);
        
        return [
            'income' => $monthlyIncome,
            'expenses' => $monthlyExpenses,
            'net_cash_flow' => $monthlyIncome - $monthlyExpenses,
            'cash_flow_ratio' => $monthlyIncome > 0 ? ($monthlyIncome - $monthlyExpenses) / $monthlyIncome : 0
        ];
    }

    private function calculateSavingsRate($user): float
    {
        $monthlyIncome = $this->getMonthlyIncome($user);
        $monthlySavings = $this->getMonthlySavings($user);
        
        return $monthlyIncome > 0 ? ($monthlySavings / $monthlyIncome) * 100 : 0;
    }

    private function getInvestmentAllocation($user): array
    {
        $investments = UserInvestment::forUser($user->id)->with('product')->get();
        $totalValue = $investments->sum('current_value');
        
        if ($totalValue <= 0) {
            return [];
        }
        
        $allocation = [];
        foreach ($investments->groupBy('product.type') as $type => $typeInvestments) {
            $typeValue = $typeInvestments->sum('current_value');
            $allocation[$type] = [
                'value' => $typeValue,
                'percentage' => ($typeValue / $totalValue) * 100
            ];
        }
        
        return $allocation;
    }

    private function calculateDebtToIncomeRatio($user): float
    {
        $monthlyIncome = $this->getMonthlyIncome($user);
        $monthlyDebtPayments = $this->getMonthlyDebtPayments($user);
        
        return $monthlyIncome > 0 ? ($monthlyDebtPayments / $monthlyIncome) * 100 : 0;
    }

    private function calculateEmergencyFundMonths($user): float
    {
        $emergencyFund = $user->balance; // Liquid savings
        $monthlyExpenses = $this->getMonthlyExpenses($user);
        
        return $monthlyExpenses > 0 ? $emergencyFund / $monthlyExpenses : 0;
    }

    private function getFinancialGoalsProgress($user): array
    {
        $targetSavings = TargetSaving::forUser($user->id)->active()->get();
        
        return [
            'total_goals' => $targetSavings->count(),
            'average_progress' => $targetSavings->avg('progress_percentage'),
            'goals_on_track' => $targetSavings->where('progress_percentage', '>=', 80)->count(),
            'total_target_amount' => $targetSavings->sum('target_amount'),
            'total_saved' => $targetSavings->sum('saved_amount')
        ];
    }

    private function getScoreGrade($score): string
    {
        if ($score >= 90) return 'Excellent';
        if ($score >= 80) return 'Very Good';
        if ($score >= 70) return 'Good';
        if ($score >= 60) return 'Fair';
        return 'Needs Improvement';
    }

    // Additional helper methods would be implemented here...
    private function getMonthlyIncome($user): float
    {
        return $user->monthly_income ?? 0;
    }

    private function getMonthlyExpenses($user): float
    {
        $lastMonth = Transaction::where('user_id', $user->id)
                               ->where('trx_type', '-')
                               ->where('created_at', '>=', Carbon::now()->subMonth())
                               ->sum('amount');
        return $lastMonth;
    }

    private function getMonthlySavings($user): float
    {
        $lastMonth = SavingsTransaction::where('user_id', $user->id)
                                     ->where('type', 'deposit')
                                     ->where('created_at', '>=', Carbon::now()->subMonth())
                                     ->sum('amount');
        return $lastMonth;
    }

    private function getMonthlyDebtPayments($user): float
    {
        $activeLoans = UserLoan::where('user_id', $user->id)
                              ->whereIn('status', ['disbursed', 'repaying'])
                              ->get();
        return $activeLoans->sum('monthly_payment');
    }

    private function calculateInvestmentDiversification($user): float
    {
        $investments = UserInvestment::forUser($user->id)->with('product')->get();
        $uniqueTypes = $investments->pluck('product.type')->unique()->count();
        
        // Score based on number of investment types (max 5 types)
        return min(1, $uniqueTypes / 5);
    }
}
