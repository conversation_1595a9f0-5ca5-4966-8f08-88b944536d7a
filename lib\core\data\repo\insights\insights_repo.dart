import 'dart:convert';
import 'package:viserpay/core/utils/method.dart';
import 'package:viserpay/core/utils/url_container.dart';
import 'package:viserpay/data/model/global/response_model/response_model.dart';
import 'package:viserpay/data/services/api_service.dart';

class InsightsRepo {
  ApiClient apiClient;
  
  InsightsRepo({required this.apiClient});

  // Get comprehensive insights
  Future<ResponseModel> getInsights({
    String period = '30_days',
    bool includeComparison = true,
    bool includePredictions = true,
  }) async {
    String url = '${UrlContainer.baseUrl}${UrlContainer.insightsEndPoint}?period=$period';
    if (includeComparison) url += '&include_comparison=true';
    if (includePredictions) url += '&include_predictions=true';
    
    ResponseModel responseModel = await apiClient.request(url, Method.getMethod, null, passHeader: true);
    return responseModel;
  }

  // Get financial overview
  Future<ResponseModel> getFinancialOverview({
    String period = '30_days',
  }) async {
    String url = '${UrlContainer.baseUrl}${UrlContainer.financialOverviewEndPoint}?period=$period';
    ResponseModel responseModel = await apiClient.request(url, Method.getMethod, null, passHeader: true);
    return responseModel;
  }

  // Get spending insights
  Future<ResponseModel> getSpendingInsights({
    String period = '30_days',
    String? category,
  }) async {
    String url = '${UrlContainer.baseUrl}${UrlContainer.spendingInsightsEndPoint}?period=$period';
    if (category != null && category.isNotEmpty) url += '&category=$category';
    
    ResponseModel responseModel = await apiClient.request(url, Method.getMethod, null, passHeader: true);
    return responseModel;
  }

  // Get savings insights
  Future<ResponseModel> getSavingsInsights({
    String period = '30_days',
  }) async {
    String url = '${UrlContainer.baseUrl}${UrlContainer.savingsInsightsEndPoint}?period=$period';
    ResponseModel responseModel = await apiClient.request(url, Method.getMethod, null, passHeader: true);
    return responseModel;
  }

  // Get income insights
  Future<ResponseModel> getIncomeInsights({
    String period = '30_days',
  }) async {
    String url = '${UrlContainer.baseUrl}${UrlContainer.incomeInsightsEndPoint}?period=$period';
    ResponseModel responseModel = await apiClient.request(url, Method.getMethod, null, passHeader: true);
    return responseModel;
  }

  // Get goals progress
  Future<ResponseModel> getGoalsProgress() async {
    String url = '${UrlContainer.baseUrl}${UrlContainer.goalsProgressEndPoint}';
    ResponseModel responseModel = await apiClient.request(url, Method.getMethod, null, passHeader: true);
    return responseModel;
  }

  // Get personalized tips
  Future<ResponseModel> getPersonalizedTips({
    String? category,
    String priority = 'all',
  }) async {
    String url = '${UrlContainer.baseUrl}${UrlContainer.personalizedTipsEndPoint}?priority=$priority';
    if (category != null && category.isNotEmpty) url += '&category=$category';
    
    ResponseModel responseModel = await apiClient.request(url, Method.getMethod, null, passHeader: true);
    return responseModel;
  }

  // Get recommendations
  Future<ResponseModel> getRecommendations({
    String type = 'all',
  }) async {
    String url = '${UrlContainer.baseUrl}${UrlContainer.recommendationsEndPoint}?type=$type';
    ResponseModel responseModel = await apiClient.request(url, Method.getMethod, null, passHeader: true);
    return responseModel;
  }

  // Get credit score
  Future<ResponseModel> getCreditScore() async {
    String url = '${UrlContainer.baseUrl}${UrlContainer.creditScoreEndPoint}';
    ResponseModel responseModel = await apiClient.request(url, Method.getMethod, null, passHeader: true);
    return responseModel;
  }

  // Get chart data
  Future<ResponseModel> getChartData({
    String type = 'all',
    String period = '30_days',
  }) async {
    String url = '${UrlContainer.baseUrl}${UrlContainer.chartDataEndPoint}?type=$type&period=$period';
    ResponseModel responseModel = await apiClient.request(url, Method.getMethod, null, passHeader: true);
    return responseModel;
  }

  // Get comparison data
  Future<ResponseModel> getComparisonData({
    String period = '30_days',
  }) async {
    String url = '${UrlContainer.baseUrl}${UrlContainer.comparisonDataEndPoint}?period=$period';
    ResponseModel responseModel = await apiClient.request(url, Method.getMethod, null, passHeader: true);
    return responseModel;
  }

  // Get spending by category
  Future<ResponseModel> getSpendingByCategory({
    String period = '30_days',
  }) async {
    String url = '${UrlContainer.baseUrl}${UrlContainer.spendingByCategoryEndPoint}?period=$period';
    ResponseModel responseModel = await apiClient.request(url, Method.getMethod, null, passHeader: true);
    return responseModel;
  }

  // Get monthly trends
  Future<ResponseModel> getMonthlyTrends({
    String type = 'spending',
    int months = 12,
  }) async {
    String url = '${UrlContainer.baseUrl}${UrlContainer.monthlyTrendsEndPoint}?type=$type&months=$months';
    ResponseModel responseModel = await apiClient.request(url, Method.getMethod, null, passHeader: true);
    return responseModel;
  }

  // Get budget analysis
  Future<ResponseModel> getBudgetAnalysis({
    String period = '30_days',
  }) async {
    String url = '${UrlContainer.baseUrl}${UrlContainer.budgetAnalysisEndPoint}?period=$period';
    ResponseModel responseModel = await apiClient.request(url, Method.getMethod, null, passHeader: true);
    return responseModel;
  }

  // Get investment insights
  Future<ResponseModel> getInvestmentInsights({
    String period = '30_days',
  }) async {
    String url = '${UrlContainer.baseUrl}${UrlContainer.investmentInsightsEndPoint}?period=$period';
    ResponseModel responseModel = await apiClient.request(url, Method.getMethod, null, passHeader: true);
    return responseModel;
  }

  // Get cash flow analysis
  Future<ResponseModel> getCashFlowAnalysis({
    String period = '30_days',
  }) async {
    String url = '${UrlContainer.baseUrl}${UrlContainer.cashFlowAnalysisEndPoint}?period=$period';
    ResponseModel responseModel = await apiClient.request(url, Method.getMethod, null, passHeader: true);
    return responseModel;
  }

  // Get financial health score
  Future<ResponseModel> getFinancialHealthScore() async {
    String url = '${UrlContainer.baseUrl}${UrlContainer.financialHealthScoreEndPoint}';
    ResponseModel responseModel = await apiClient.request(url, Method.getMethod, null, passHeader: true);
    return responseModel;
  }

  // Get expense predictions
  Future<ResponseModel> getExpensePredictions({
    String period = '30_days',
  }) async {
    String url = '${UrlContainer.baseUrl}${UrlContainer.expensePredictionsEndPoint}?period=$period';
    ResponseModel responseModel = await apiClient.request(url, Method.getMethod, null, passHeader: true);
    return responseModel;
  }

  // Get savings recommendations
  Future<ResponseModel> getSavingsRecommendations() async {
    String url = '${UrlContainer.baseUrl}${UrlContainer.savingsRecommendationsEndPoint}';
    ResponseModel responseModel = await apiClient.request(url, Method.getMethod, null, passHeader: true);
    return responseModel;
  }

  // Get debt analysis
  Future<ResponseModel> getDebtAnalysis() async {
    String url = '${UrlContainer.baseUrl}${UrlContainer.debtAnalysisEndPoint}';
    ResponseModel responseModel = await apiClient.request(url, Method.getMethod, null, passHeader: true);
    return responseModel;
  }

  // Get merchant insights
  Future<ResponseModel> getMerchantInsights({
    String period = '30_days',
  }) async {
    String url = '${UrlContainer.baseUrl}${UrlContainer.merchantInsightsEndPoint}?period=$period';
    ResponseModel responseModel = await apiClient.request(url, Method.getMethod, null, passHeader: true);
    return responseModel;
  }

  // Get transaction patterns
  Future<ResponseModel> getTransactionPatterns({
    String period = '30_days',
  }) async {
    String url = '${UrlContainer.baseUrl}${UrlContainer.transactionPatternsEndPoint}?period=$period';
    ResponseModel responseModel = await apiClient.request(url, Method.getMethod, null, passHeader: true);
    return responseModel;
  }

  // Get financial milestones
  Future<ResponseModel> getFinancialMilestones() async {
    String url = '${UrlContainer.baseUrl}${UrlContainer.financialMilestonesEndPoint}';
    ResponseModel responseModel = await apiClient.request(url, Method.getMethod, null, passHeader: true);
    return responseModel;
  }

  // Export insights report
  Future<ResponseModel> exportInsightsReport({
    String format = 'pdf',
    String period = '30_days',
  }) async {
    String url = '${UrlContainer.baseUrl}${UrlContainer.exportInsightsEndPoint}';
    Map<String, String> params = {
      'format': format,
      'period': period,
    };
    ResponseModel responseModel = await apiClient.request(url, Method.postMethod, params, passHeader: true);
    return responseModel;
  }

  // Set insights preferences
  Future<ResponseModel> setInsightsPreferences({
    required Map<String, dynamic> preferences,
  }) async {
    String url = '${UrlContainer.baseUrl}${UrlContainer.insightsPreferencesEndPoint}';
    ResponseModel responseModel = await apiClient.request(url, Method.postMethod, preferences, passHeader: true);
    return responseModel;
  }

  // Get insights preferences
  Future<ResponseModel> getInsightsPreferences() async {
    String url = '${UrlContainer.baseUrl}${UrlContainer.insightsPreferencesEndPoint}';
    ResponseModel responseModel = await apiClient.request(url, Method.getMethod, null, passHeader: true);
    return responseModel;
  }
}
