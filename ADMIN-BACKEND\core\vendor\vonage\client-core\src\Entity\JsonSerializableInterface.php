<?php

/**
 * Vonage Client Library for PHP
 *
 * @copyright Copyright (c) 2016-2020 Vonage, Inc. (http://vonage.com)
 * @license https://github.com/Vonage/vonage-php-sdk-core/blob/master/LICENSE.txt Apache License 2.0
 */

declare(strict_types=1);

namespace Vonage\Entity;

use JsonSerializable;

/**
 * Identifies the Entity as using JsonSerializable to prepare request data.
 */
interface JsonSerializableInterface extends JsonSerializable
{
}
