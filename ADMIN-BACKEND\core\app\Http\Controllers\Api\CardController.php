<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\UserCard;
use App\Models\CardRequest;
use App\Models\CardSecuritySetting;
use App\Models\AdminRate;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Validator;
use Carbon\Carbon;

class CardController extends Controller
{
    public function __construct()
    {
        $this->middleware('auth:api');
    }

    /**
     * Get user's cards
     */
    public function getUserCards(): JsonResponse
    {
        $user = Auth::user();
        
        $cards = UserCard::forUser($user->id)
                        ->with(['securitySettings', 'transactions' => function($q) {
                            $q->orderBy('created_at', 'desc')->limit(5);
                        }])
                        ->get();

        return response()->json([
            'status' => 'success',
            'data' => [
                'cards' => $cards,
                'card_fees' => AdminRate::getCardFees()
            ]
        ]);
    }

    /**
     * Request new card
     */
    public function requestCard(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'card_type' => 'required|in:physical,virtual',
            'account_type' => 'required|in:personal,business',
            'card_name' => 'required|string|max:255',
            'delivery_address' => 'required_if:card_type,physical|string',
            'delivery_state' => 'required_if:card_type,physical|string',
            'delivery_city' => 'required_if:card_type,physical|string',
            'phone_number' => 'required|string|max:20',
            'spending_limit' => 'nullable|numeric|min:1000'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'status' => 'error',
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], 422);
        }

        $user = Auth::user();
        
        // Check if user has completed KYC
        if ($user->kv != 1) {
            return response()->json([
                'status' => 'error',
                'message' => 'KYC verification required to request a card'
            ], 400);
        }

        // Get card fees
        $cardFees = AdminRate::getCardFees();
        $cardFee = $request->card_type === 'physical' ? 
                  $cardFees['physical_card_fee'] : 
                  $cardFees['virtual_card_fee'];

        // Check if user has sufficient balance for card fee
        if ($user->balance < $cardFee) {
            return response()->json([
                'status' => 'error',
                'message' => 'Insufficient balance for card fee'
            ], 400);
        }

        // Create card request
        $cardRequest = CardRequest::create([
            'user_id' => $user->id,
            'card_type' => $request->card_type,
            'account_type' => $request->account_type,
            'card_name' => $request->card_name,
            'delivery_address' => $request->delivery_address,
            'delivery_state' => $request->delivery_state,
            'delivery_city' => $request->delivery_city,
            'phone_number' => $request->phone_number,
            'spending_limit' => $request->spending_limit,
            'status' => 'pending'
        ]);

        // For virtual cards, auto-approve and create immediately
        if ($request->card_type === 'virtual') {
            $this->processVirtualCard($cardRequest, $cardFee);
        } else {
            // Deduct card fee for physical cards (will be refunded if rejected)
            $user->balance -= $cardFee;
            $user->save();
        }

        return response()->json([
            'status' => 'success',
            'message' => 'Card request submitted successfully',
            'data' => [
                'request' => $cardRequest,
                'card_fee' => $cardFee,
                'processing_time' => $request->card_type === 'virtual' ? 'Instant' : '3-5 business days'
            ]
        ]);
    }

    /**
     * Create PIN for card
     */
    public function createPin(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'card_id' => 'required|exists:user_cards,id',
            'pin' => 'required|string|size:4|regex:/^[0-9]{4}$/'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'status' => 'error',
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], 422);
        }

        $user = Auth::user();
        $card = UserCard::where('id', $request->card_id)
                       ->where('user_id', $user->id)
                       ->first();

        if (!$card) {
            return response()->json([
                'status' => 'error',
                'message' => 'Card not found'
            ], 404);
        }

        $result = $card->createPin($request->pin);

        return response()->json([
            'status' => $result['success'] ? 'success' : 'error',
            'message' => $result['message']
        ], $result['success'] ? 200 : 400);
    }

    /**
     * Change PIN for card
     */
    public function changePin(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'card_id' => 'required|exists:user_cards,id',
            'old_pin' => 'required|string|size:4',
            'new_pin' => 'required|string|size:4|regex:/^[0-9]{4}$/'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'status' => 'error',
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], 422);
        }

        $user = Auth::user();
        $card = UserCard::where('id', $request->card_id)
                       ->where('user_id', $user->id)
                       ->first();

        if (!$card) {
            return response()->json([
                'status' => 'error',
                'message' => 'Card not found'
            ], 404);
        }

        $result = $card->changePin($request->old_pin, $request->new_pin);

        return response()->json([
            'status' => $result['success'] ? 'success' : 'error',
            'message' => $result['message']
        ], $result['success'] ? 200 : 400);
    }

    /**
     * Reset PIN for card
     */
    public function resetPin(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'card_id' => 'required|exists:user_cards,id',
            'new_pin' => 'required|string|size:4|regex:/^[0-9]{4}$/'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'status' => 'error',
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], 422);
        }

        $user = Auth::user();
        $card = UserCard::where('id', $request->card_id)
                       ->where('user_id', $user->id)
                       ->first();

        if (!$card) {
            return response()->json([
                'status' => 'error',
                'message' => 'Card not found'
            ], 404);
        }

        $result = $card->resetPin($request->new_pin);

        return response()->json([
            'status' => $result['success'] ? 'success' : 'error',
            'message' => $result['message']
        ], $result['success'] ? 200 : 400);
    }

    /**
     * Unlock PIN for card
     */
    public function unlockPin(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'card_id' => 'required|exists:user_cards,id'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'status' => 'error',
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], 422);
        }

        $user = Auth::user();
        $card = UserCard::where('id', $request->card_id)
                       ->where('user_id', $user->id)
                       ->first();

        if (!$card) {
            return response()->json([
                'status' => 'error',
                'message' => 'Card not found'
            ], 404);
        }

        $result = $card->unlockPin();

        return response()->json([
            'status' => $result['success'] ? 'success' : 'error',
            'message' => $result['message']
        ], $result['success'] ? 200 : 400);
    }

    /**
     * Freeze card
     */
    public function freezeCard(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'card_id' => 'required|exists:user_cards,id',
            'reason' => 'nullable|string|max:255'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'status' => 'error',
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], 422);
        }

        $user = Auth::user();
        $card = UserCard::where('id', $request->card_id)
                       ->where('user_id', $user->id)
                       ->first();

        if (!$card) {
            return response()->json([
                'status' => 'error',
                'message' => 'Card not found'
            ], 404);
        }

        $result = $card->freeze($request->reason ?? 'User requested');

        return response()->json([
            'status' => $result['success'] ? 'success' : 'error',
            'message' => $result['message']
        ], $result['success'] ? 200 : 400);
    }

    /**
     * Unfreeze card
     */
    public function unfreezeCard(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'card_id' => 'required|exists:user_cards,id'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'status' => 'error',
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], 422);
        }

        $user = Auth::user();
        $card = UserCard::where('id', $request->card_id)
                       ->where('user_id', $user->id)
                       ->first();

        if (!$card) {
            return response()->json([
                'status' => 'error',
                'message' => 'Card not found'
            ], 404);
        }

        $result = $card->unfreeze();

        return response()->json([
            'status' => $result['success'] ? 'success' : 'error',
            'message' => $result['message']
        ], $result['success'] ? 200 : 400);
    }

    /**
     * Update card limits
     */
    public function updateLimits(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'card_id' => 'required|exists:user_cards,id',
            'daily_limit' => 'nullable|numeric|min:1000|max:1000000',
            'monthly_limit' => 'nullable|numeric|min:10000|max:10000000',
            'single_transaction_limit' => 'nullable|numeric|min:100|max:500000'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'status' => 'error',
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], 422);
        }

        $user = Auth::user();
        $card = UserCard::where('id', $request->card_id)
                       ->where('user_id', $user->id)
                       ->first();

        if (!$card) {
            return response()->json([
                'status' => 'error',
                'message' => 'Card not found'
            ], 404);
        }

        $limits = array_filter([
            'daily_limit' => $request->daily_limit,
            'monthly_limit' => $request->monthly_limit,
            'single_transaction_limit' => $request->single_transaction_limit
        ]);

        $result = $card->updateLimits($limits);

        return response()->json([
            'status' => $result['success'] ? 'success' : 'error',
            'message' => $result['message'],
            'data' => $card->fresh()
        ], $result['success'] ? 200 : 400);
    }

    /**
     * Get card analytics
     */
    public function getCardAnalytics(Request $request): JsonResponse
    {
        $user = Auth::user();
        $cardId = $request->get('card_id');
        $days = $request->get('days', 30);

        if ($cardId) {
            $card = UserCard::where('id', $cardId)
                           ->where('user_id', $user->id)
                           ->first();

            if (!$card) {
                return response()->json([
                    'status' => 'error',
                    'message' => 'Card not found'
                ], 404);
            }

            $analytics = $card->getUsageAnalytics($days);
            
            return response()->json([
                'status' => 'success',
                'data' => [
                    'card' => $card,
                    'analytics' => $analytics
                ]
            ]);
        }

        // Get analytics for all user cards
        $cards = UserCard::forUser($user->id)->get();
        $allAnalytics = [];

        foreach ($cards as $card) {
            $allAnalytics[] = [
                'card' => $card,
                'analytics' => $card->getUsageAnalytics($days)
            ];
        }

        return response()->json([
            'status' => 'success',
            'data' => $allAnalytics
        ]);
    }

    private function processVirtualCard(CardRequest $cardRequest, float $cardFee): void
    {
        // Auto-approve virtual card request
        $cardRequest->status = 'approved';
        $cardRequest->processed_at = Carbon::now();
        $cardRequest->save();

        // Create the virtual card
        $card = UserCard::create([
            'user_id' => $cardRequest->user_id,
            'card_type' => 'virtual',
            'account_type' => $cardRequest->account_type,
            'card_name' => $cardRequest->card_name,
            'card_number' => UserCard::generateCardNumber(),
            'masked_card_number' => '**** **** **** ' . substr(UserCard::generateCardNumber(), -4),
            'expiry_date' => UserCard::generateExpiryDate(),
            'cvv' => UserCard::generateCVV(),
            'daily_limit' => $cardRequest->spending_limit ?? 100000,
            'monthly_limit' => ($cardRequest->spending_limit ?? 100000) * 10,
            'single_transaction_limit' => ($cardRequest->spending_limit ?? 100000) / 2,
            'status' => 'active',
            'card_fee' => $cardFee
        ]);

        // Create default security settings
        CardSecuritySetting::create([
            'card_id' => $card->id,
            'online_transactions_enabled' => true,
            'international_transactions_enabled' => false,
            'atm_withdrawals_enabled' => false,
            'contactless_enabled' => true,
            'pos_transactions_enabled' => true,
            'web_transactions_enabled' => true
        ]);

        // Deduct card fee
        $user = $cardRequest->user;
        $user->balance -= $cardFee;
        $user->save();
    }
}
