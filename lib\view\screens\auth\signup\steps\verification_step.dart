import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';
import 'package:pin_code_fields/pin_code_fields.dart';
import 'package:viserpay/core/utils/dimensions.dart';
import 'package:viserpay/core/utils/my_color.dart';
import 'package:viserpay/core/utils/style.dart';
import 'package:viserpay/view/screens/auth/signup/signup_controller.dart';

class VerificationStep extends StatelessWidget {
  final SignupController controller;

  const VerificationStep({
    super.key,
    required this.controller,
  });

  @override
  Widget build(BuildContext context) {
    return Form(
      key: controller.otpFormKey,
      child: Column(
        children: [
          // Phone number display
          Container(
            padding: const EdgeInsets.all(Dimensions.space20),
            decoration: BoxDecoration(
              color: MyColor.primaryColor.withOpacity(0.05),
              borderRadius: BorderRadius.circular(Dimensions.largeRadius),
              border: Border.all(
                color: MyColor.primaryColor.withOpacity(0.2),
                width: 1,
              ),
            ),
            child: Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(Dimensions.space10),
                  decoration: BoxDecoration(
                    color: MyColor.primaryColor.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(Dimensions.mediumRadius),
                  ),
                  child: Icon(
                    Icons.sms_outlined,
                    color: MyColor.primaryColor,
                    size: 24,
                  ),
                ),
                const SizedBox(width: Dimensions.space15),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Verification Code Sent',
                        style: mediumDefault.copyWith(
                          color: MyColor.primaryColor,
                        ),
                      ),
                      const SizedBox(height: Dimensions.space5),
                      Text(
                        'We sent a 6-digit code to',
                        style: regularSmall.copyWith(
                          color: MyColor.contentTextColor,
                        ),
                      ),
                      Text(
                        '+234 ${controller.phoneController.text}',
                        style: semiBoldDefault.copyWith(
                          color: MyColor.primaryTextColor,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
          
          const SizedBox(height: Dimensions.space40),
          
          // OTP Input
          Text(
            'Enter Verification Code',
            style: semiBoldLarge.copyWith(
              color: MyColor.primaryTextColor,
            ),
          ),
          
          const SizedBox(height: Dimensions.space20),
          
          PinCodeTextField(
            appContext: context,
            length: 6,
            controller: controller.otpController,
            keyboardType: TextInputType.number,
            inputFormatters: [FilteringTextInputFormatter.digitsOnly],
            pinTheme: PinTheme(
              shape: PinCodeFieldShape.box,
              borderRadius: BorderRadius.circular(Dimensions.mediumRadius),
              fieldHeight: 56,
              fieldWidth: 48,
              activeFillColor: MyColor.brandWhite,
              inactiveFillColor: MyColor.brandWhite,
              selectedFillColor: MyColor.primaryColor.withOpacity(0.1),
              activeColor: MyColor.primaryColor,
              inactiveColor: MyColor.borderColor,
              selectedColor: MyColor.primaryColor,
              borderWidth: 2,
            ),
            enableActiveFill: true,
            cursorColor: MyColor.primaryColor,
            animationType: AnimationType.fade,
            animationDuration: const Duration(milliseconds: 300),
            onChanged: (value) {
              // Auto-validate as user types
            },
            onCompleted: (value) {
              // Auto-submit when complete
              if (value.length == 6) {
                controller.nextStep();
              }
            },
            validator: (value) {
              if (value == null || value.isEmpty) {
                return 'Please enter the verification code';
              }
              if (value.length != 6) {
                return 'Please enter all 6 digits';
              }
              return null;
            },
          ),
          
          const SizedBox(height: Dimensions.space30),
          
          // Resend OTP section
          Obx(() {
            if (controller.canResendOtp.value) {
              return Column(
                children: [
                  Text(
                    "Didn't receive the code?",
                    style: regularDefault.copyWith(
                      color: MyColor.contentTextColor,
                    ),
                  ),
                  const SizedBox(height: Dimensions.space10),
                  GestureDetector(
                    onTap: controller.resendOtp,
                    child: Container(
                      padding: const EdgeInsets.symmetric(
                        horizontal: Dimensions.space20,
                        vertical: Dimensions.space10,
                      ),
                      decoration: BoxDecoration(
                        color: MyColor.primaryColor.withOpacity(0.1),
                        borderRadius: BorderRadius.circular(Dimensions.mediumRadius),
                        border: Border.all(
                          color: MyColor.primaryColor.withOpacity(0.3),
                          width: 1,
                        ),
                      ),
                      child: Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Icon(
                            Icons.refresh,
                            color: MyColor.primaryColor,
                            size: 18,
                          ),
                          const SizedBox(width: Dimensions.space8),
                          Text(
                            'Resend Code',
                            style: mediumDefault.copyWith(
                              color: MyColor.primaryColor,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                ],
              );
            } else {
              return Column(
                children: [
                  Text(
                    "Didn't receive the code?",
                    style: regularDefault.copyWith(
                      color: MyColor.contentTextColor,
                    ),
                  ),
                  const SizedBox(height: Dimensions.space10),
                  Container(
                    padding: const EdgeInsets.symmetric(
                      horizontal: Dimensions.space20,
                      vertical: Dimensions.space10,
                    ),
                    decoration: BoxDecoration(
                      color: MyColor.borderColor.withOpacity(0.3),
                      borderRadius: BorderRadius.circular(Dimensions.mediumRadius),
                    ),
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Icon(
                          Icons.timer_outlined,
                          color: MyColor.contentTextColor,
                          size: 18,
                        ),
                        const SizedBox(width: Dimensions.space8),
                        Text(
                          'Resend in ${controller.otpCountdown.value}s',
                          style: regularDefault.copyWith(
                            color: MyColor.contentTextColor,
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              );
            }
          }),
          
          const SizedBox(height: Dimensions.space30),
          
          // Help section
          Container(
            padding: const EdgeInsets.all(Dimensions.space15),
            decoration: BoxDecoration(
              color: MyColor.brandYellow.withOpacity(0.1),
              borderRadius: BorderRadius.circular(Dimensions.mediumRadius),
              border: Border.all(
                color: MyColor.brandYellow.withOpacity(0.3),
                width: 1,
              ),
            ),
            child: Column(
              children: [
                Row(
                  children: [
                    Icon(
                      Icons.help_outline,
                      color: MyColor.brandYellow,
                      size: 20,
                    ),
                    const SizedBox(width: Dimensions.space10),
                    Text(
                      'Having trouble?',
                      style: mediumDefault.copyWith(
                        color: MyColor.brandYellow,
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: Dimensions.space10),
                Text(
                  '• Check your SMS messages\n• Ensure you have network coverage\n• The code expires in 10 minutes\n• Contact support if issues persist',
                  style: regularSmall.copyWith(
                    color: MyColor.contentTextColor,
                    height: 1.5,
                  ),
                ),
                const SizedBox(height: Dimensions.space10),
                GestureDetector(
                  onTap: () {
                    // Open support/help
                  },
                  child: Text(
                    'Contact Support',
                    style: mediumSmall.copyWith(
                      color: MyColor.primaryColor,
                      decoration: TextDecoration.underline,
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
