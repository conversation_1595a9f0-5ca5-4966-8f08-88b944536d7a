import 'package:flutter/material.dart';
import 'package:viserpay/core/utils/dimensions.dart';
import 'package:viserpay/core/utils/my_color.dart';
import 'package:viserpay/core/utils/style.dart';

class PeriodSelector extends StatelessWidget {
  final String selectedPeriod;
  final Function(String) onPeriodChanged;

  const PeriodSelector({
    super.key,
    required this.selectedPeriod,
    required this.onPeriodChanged,
  });

  @override
  Widget build(BuildContext context) {
    final periods = [
      {'value': '7_days', 'label': '7D'},
      {'value': '30_days', 'label': '30D'},
      {'value': '90_days', 'label': '3M'},
      {'value': '1_year', 'label': '1Y'},
    ];

    return Container(
      padding: const EdgeInsets.all(4),
      decoration: BoxDecoration(
        color: MyColor.colorWhite,
        borderRadius: BorderRadius.circular(Dimensions.mediumRadius),
        boxShadow: [
          BoxShadow(
            color: MyColor.colorGrey.withOpacity(0.1),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: periods.map((period) {
          bool isSelected = selectedPeriod == period['value'];
          return GestureDetector(
            onTap: () => onPeriodChanged(period['value']!),
            child: AnimatedContainer(
              duration: const Duration(milliseconds: 200),
              padding: const EdgeInsets.symmetric(
                horizontal: Dimensions.space15,
                vertical: Dimensions.space8,
              ),
              decoration: BoxDecoration(
                color: isSelected ? MyColor.primaryColor : Colors.transparent,
                borderRadius: BorderRadius.circular(Dimensions.smallRadius),
              ),
              child: Text(
                period['label']!,
                style: semiBoldDefault.copyWith(
                  color: isSelected ? MyColor.colorWhite : MyColor.colorGrey,
                  fontSize: 12,
                ),
              ),
            ),
          );
        }).toList(),
      ),
    );
  }
}
