import 'package:flutter/material.dart';
import 'package:viserpay/core/utils/dimensions.dart';
import 'package:viserpay/core/utils/my_color.dart';
import 'package:viserpay/core/utils/style.dart';
import 'package:viserpay/core/utils/nigerian_currency_utils.dart';
import 'package:viserpay/data/model/insights/insights_model.dart';

class SavingsInsightsCard extends StatelessWidget {
  final SavingsInsights? savingsInsights;

  const SavingsInsightsCard({
    super.key,
    this.savingsInsights,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(Dimensions.space20),
      decoration: BoxDecoration(
        color: MyColor.colorWhite,
        borderRadius: BorderRadius.circular(Dimensions.cardRadius),
        boxShadow: [
          BoxShadow(
            color: MyColor.colorGrey.withOpacity(0.1),
            blurRadius: 15,
            offset: const Offset(0, 5),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(Dimensions.space10),
                decoration: BoxDecoration(
                  color: MyColor.colorGreen.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(Dimensions.smallRadius),
                ),
                child: const Icon(
                  Icons.savings,
                  color: MyColor.colorGreen,
                  size: 24,
                ),
              ),
              const SizedBox(width: Dimensions.space15),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Savings Insights',
                      style: semiBoldLarge.copyWith(
                        color: MyColor.colorBlack,
                        fontSize: 18,
                      ),
                    ),
                    Text(
                      'Your savings progress and goals',
                      style: regularDefault.copyWith(
                        color: MyColor.colorGrey,
                        fontSize: 12,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),

          const SizedBox(height: Dimensions.space25),

          // Total Savings
          _buildTotalSavings(),

          const SizedBox(height: Dimensions.space20),

          // Savings Metrics
          Row(
            children: [
              Expanded(
                child: _buildMetricCard(
                  label: 'Monthly Savings',
                  value: savingsInsights?.monthlySavings ?? '0',
                  icon: Icons.calendar_month,
                  color: MyColor.colorBlue,
                ),
              ),
              const SizedBox(width: Dimensions.space10),
              Expanded(
                child: _buildMetricCard(
                  label: 'Interest Earned',
                  value: savingsInsights?.interestEarned ?? '0',
                  icon: Icons.trending_up,
                  color: MyColor.colorOrange,
                ),
              ),
            ],
          ),

          const SizedBox(height: Dimensions.space20),

          // Savings Rate
          _buildSavingsRate(),

          const SizedBox(height: Dimensions.space20),

          // Goals Progress
          if (savingsInsights?.goals != null && savingsInsights!.goals!.isNotEmpty)
            _buildGoalsProgress(),
        ],
      ),
    );
  }

  Widget _buildTotalSavings() {
    String totalSavings = savingsInsights?.totalSavings ?? '0';
    String savingsGoal = savingsInsights?.savingsGoal ?? '0';
    double progress = double.tryParse(savingsInsights?.goalProgress ?? '0') ?? 0;

    return Container(
      padding: const EdgeInsets.all(Dimensions.space15),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            MyColor.colorGreen.withOpacity(0.1),
            MyColor.colorGreen.withOpacity(0.05),
          ],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(Dimensions.smallRadius),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'Total Savings',
                style: regularDefault.copyWith(
                  color: MyColor.colorGrey,
                  fontSize: 14,
                ),
              ),
              Text(
                '${progress.toStringAsFixed(1)}% of goal',
                style: semiBoldDefault.copyWith(
                  color: MyColor.colorGreen,
                  fontSize: 12,
                ),
              ),
            ],
          ),
          const SizedBox(height: Dimensions.space8),
          Text(
            NigerianCurrencyUtils.formatNaira(totalSavings),
            style: boldExtraLarge.copyWith(
              color: MyColor.colorBlack,
              fontSize: 24,
            ),
          ),
          const SizedBox(height: Dimensions.space8),
          Text(
            'Goal: ${NigerianCurrencyUtils.formatNaira(savingsGoal)}',
            style: regularDefault.copyWith(
              color: MyColor.colorGrey,
              fontSize: 12,
            ),
          ),
          const SizedBox(height: Dimensions.space10),
          LinearProgressIndicator(
            value: progress / 100,
            backgroundColor: MyColor.colorGrey.withOpacity(0.2),
            valueColor: const AlwaysStoppedAnimation<Color>(MyColor.colorGreen),
            minHeight: 6,
          ),
        ],
      ),
    );
  }

  Widget _buildMetricCard({
    required String label,
    required String value,
    required IconData icon,
    required Color color,
  }) {
    return Container(
      padding: const EdgeInsets.all(Dimensions.space15),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(Dimensions.smallRadius),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Icon(
            icon,
            color: color,
            size: 20,
          ),
          const SizedBox(height: Dimensions.space10),
          Text(
            label,
            style: regularSmall.copyWith(
              color: MyColor.colorGrey,
              fontSize: 11,
            ),
          ),
          const SizedBox(height: Dimensions.space5),
          Text(
            NigerianCurrencyUtils.formatNaira(value, useShortForm: true),
            style: semiBoldDefault.copyWith(
              color: MyColor.colorBlack,
              fontSize: 14,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSavingsRate() {
    double savingsRate = double.tryParse(savingsInsights?.savingsRate ?? '0') ?? 0;
    Color rateColor;
    String rateDescription;

    if (savingsRate >= 20) {
      rateColor = MyColor.colorGreen;
      rateDescription = 'Excellent';
    } else if (savingsRate >= 10) {
      rateColor = MyColor.colorOrange;
      rateDescription = 'Good';
    } else if (savingsRate >= 5) {
      rateColor = MyColor.colorYellow;
      rateDescription = 'Fair';
    } else {
      rateColor = MyColor.colorRed;
      rateDescription = 'Needs Improvement';
    }

    return Container(
      padding: const EdgeInsets.all(Dimensions.space15),
      decoration: BoxDecoration(
        color: rateColor.withOpacity(0.1),
        borderRadius: BorderRadius.circular(Dimensions.smallRadius),
      ),
      child: Row(
        children: [
          Container(
            width: 50,
            height: 50,
            decoration: BoxDecoration(
              color: rateColor.withOpacity(0.2),
              shape: BoxShape.circle,
            ),
            child: Center(
              child: Text(
                '${savingsRate.toInt()}%',
                style: boldDefault.copyWith(
                  color: rateColor,
                  fontSize: 14,
                ),
              ),
            ),
          ),
          const SizedBox(width: Dimensions.space15),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Savings Rate',
                  style: semiBoldDefault.copyWith(
                    color: MyColor.colorBlack,
                    fontSize: 14,
                  ),
                ),
                const SizedBox(height: Dimensions.space2),
                Text(
                  rateDescription,
                  style: regularDefault.copyWith(
                    color: rateColor,
                    fontSize: 12,
                  ),
                ),
                const SizedBox(height: Dimensions.space5),
                Text(
                  'Recommended: 20% or higher',
                  style: regularSmall.copyWith(
                    color: MyColor.colorGrey,
                    fontSize: 10,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildGoalsProgress() {
    List<SavingsGoal> goals = savingsInsights!.goals!.take(3).toList();

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Savings Goals',
          style: semiBoldDefault.copyWith(
            color: MyColor.colorBlack,
            fontSize: 16,
          ),
        ),
        const SizedBox(height: Dimensions.space15),
        ...goals.map((goal) => _buildGoalItem(goal)).toList(),
      ],
    );
  }

  Widget _buildGoalItem(SavingsGoal goal) {
    double currentAmount = double.tryParse(goal.currentAmount ?? '0') ?? 0;
    double targetAmount = double.tryParse(goal.targetAmount ?? '0') ?? 0;
    double progress = targetAmount > 0 ? (currentAmount / targetAmount) : 0;

    return Container(
      margin: const EdgeInsets.only(bottom: Dimensions.space10),
      padding: const EdgeInsets.all(Dimensions.space15),
      decoration: BoxDecoration(
        color: MyColor.colorGrey.withOpacity(0.05),
        borderRadius: BorderRadius.circular(Dimensions.smallRadius),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Expanded(
                child: Text(
                  goal.name ?? 'Savings Goal',
                  style: semiBoldDefault.copyWith(
                    color: MyColor.colorBlack,
                    fontSize: 14,
                  ),
                ),
              ),
              Text(
                '${(progress * 100).toStringAsFixed(1)}%',
                style: semiBoldDefault.copyWith(
                  color: MyColor.colorGreen,
                  fontSize: 12,
                ),
              ),
            ],
          ),
          const SizedBox(height: Dimensions.space8),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                NigerianCurrencyUtils.formatNaira(goal.currentAmount ?? '0', useShortForm: true),
                style: regularDefault.copyWith(
                  color: MyColor.colorGrey,
                  fontSize: 12,
                ),
              ),
              Text(
                NigerianCurrencyUtils.formatNaira(goal.targetAmount ?? '0', useShortForm: true),
                style: regularDefault.copyWith(
                  color: MyColor.colorGrey,
                  fontSize: 12,
                ),
              ),
            ],
          ),
          const SizedBox(height: Dimensions.space8),
          LinearProgressIndicator(
            value: progress.clamp(0.0, 1.0),
            backgroundColor: MyColor.colorGrey.withOpacity(0.2),
            valueColor: const AlwaysStoppedAnimation<Color>(MyColor.colorGreen),
            minHeight: 4,
          ),
          if (goal.deadline != null) ...[
            const SizedBox(height: Dimensions.space8),
            Row(
              children: [
                const Icon(
                  Icons.schedule,
                  color: MyColor.colorGrey,
                  size: 12,
                ),
                const SizedBox(width: Dimensions.space5),
                Text(
                  'Target: ${goal.deadline}',
                  style: regularSmall.copyWith(
                    color: MyColor.colorGrey,
                    fontSize: 10,
                  ),
                ),
              ],
            ),
          ],
        ],
      ),
    );
  }
}
