<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\LoanProduct;
use App\Models\UserLoan;
use App\Models\LoanRepayment;
use App\Models\UserCreditScore;
use App\Models\AdminRate;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Validator;
use Carbon\Carbon;

class LoanController extends Controller
{
    public function __construct()
    {
        $this->middleware('auth:api');
    }

    /**
     * Get available loan products
     */
    public function getProducts(): JsonResponse
    {
        $user = Auth::user();
        $products = LoanProduct::active()->get();
        
        // Update rates from admin configuration
        $loanRates = AdminRate::getLoanRates();
        
        foreach ($products as $product) {
            $rateKey = $product->type . '_loan';
            if (isset($loanRates[$rateKey])) {
                $product->interest_rate = $loanRates[$rateKey];
            }
        }

        return response()->json([
            'status' => 'success',
            'data' => [
                'products' => $products,
                'user_credit_score' => $user->credit_score ?? 500,
                'active_loans_count' => $user->loans()->whereIn('status', ['approved', 'disbursed', 'repaying'])->count()
            ]
        ]);
    }

    /**
     * Check loan eligibility
     */
    public function checkEligibility(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'product_id' => 'required|exists:loan_products,id',
            'amount' => 'required|numeric|min:1000',
            'duration_months' => 'required|integer|min:1|max:60'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'status' => 'error',
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], 422);
        }

        $user = Auth::user();
        $product = LoanProduct::findOrFail($request->product_id);
        
        $eligibility = $product->checkEligibility(
            $user->id, 
            $request->amount, 
            $request->duration_months
        );

        return response()->json([
            'status' => 'success',
            'data' => $eligibility
        ]);
    }

    /**
     * Apply for loan
     */
    public function applyForLoan(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'product_id' => 'required|exists:loan_products,id',
            'amount' => 'required|numeric|min:1000',
            'duration_months' => 'required|integer|min:1|max:60',
            'purpose' => 'required|string|max:500',
            'employment_status' => 'required|string',
            'monthly_income' => 'required|numeric|min:0',
            'documents' => 'nullable|array'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'status' => 'error',
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], 422);
        }

        $user = Auth::user();
        $product = LoanProduct::findOrFail($request->product_id);
        
        // Check eligibility first
        $eligibility = $product->checkEligibility(
            $user->id, 
            $request->amount, 
            $request->duration_months
        );

        if (!$eligibility['eligible']) {
            return response()->json([
                'status' => 'error',
                'message' => 'Loan application not eligible',
                'reasons' => $eligibility['reasons']
            ], 400);
        }

        // Create loan application
        $loan = UserLoan::create([
            'user_id' => $user->id,
            'product_id' => $request->product_id,
            'amount' => $request->amount,
            'duration_months' => $request->duration_months,
            'interest_rate' => $eligibility['recommended_rate'],
            'monthly_payment' => $eligibility['monthly_payment'],
            'total_repayment' => $eligibility['total_repayment'],
            'processing_fee' => $eligibility['processing_fee'],
            'purpose' => $request->purpose,
            'employment_status' => $request->employment_status,
            'monthly_income' => $request->monthly_income,
            'application_date' => Carbon::now()->toDateString(),
            'status' => 'pending',
            'documents' => $request->documents ?? []
        ]);

        return response()->json([
            'status' => 'success',
            'message' => 'Loan application submitted successfully',
            'data' => [
                'loan' => $loan,
                'reference' => $loan->reference,
                'processing_time' => '1-3 business days'
            ]
        ]);
    }

    /**
     * Get user's loans
     */
    public function getUserLoans(): JsonResponse
    {
        $user = Auth::user();
        
        $loans = UserLoan::where('user_id', $user->id)
                        ->with(['product', 'repayments'])
                        ->orderBy('created_at', 'desc')
                        ->get();

        $summary = [
            'total_loans' => $loans->count(),
            'active_loans' => $loans->whereIn('status', ['disbursed', 'repaying'])->count(),
            'completed_loans' => $loans->where('status', 'completed')->count(),
            'total_borrowed' => $loans->whereIn('status', ['disbursed', 'repaying', 'completed'])->sum('amount'),
            'total_repaid' => $loans->sum('amount_paid'),
            'outstanding_balance' => $loans->whereIn('status', ['disbursed', 'repaying'])->sum('outstanding_balance')
        ];

        return response()->json([
            'status' => 'success',
            'data' => [
                'loans' => $loans,
                'summary' => $summary
            ]
        ]);
    }

    /**
     * Get repayment schedule for a loan
     */
    public function getRepaymentSchedule($loanId): JsonResponse
    {
        $user = Auth::user();
        
        $loan = UserLoan::where('id', $loanId)
                       ->where('user_id', $user->id)
                       ->with(['product', 'repayments'])
                       ->first();

        if (!$loan) {
            return response()->json([
                'status' => 'error',
                'message' => 'Loan not found'
            ], 404);
        }

        $schedule = $loan->generateRepaymentSchedule();

        return response()->json([
            'status' => 'success',
            'data' => [
                'loan' => $loan,
                'schedule' => $schedule,
                'next_payment' => $loan->getNextPayment(),
                'overdue_payments' => $loan->getOverduePayments()
            ]
        ]);
    }

    /**
     * Get loan analytics
     */
    public function getLoanAnalytics(): JsonResponse
    {
        $user = Auth::user();
        
        $loans = UserLoan::where('user_id', $user->id)->get();
        $repayments = LoanRepayment::whereIn('loan_id', $loans->pluck('id'))->get();

        $analytics = [
            'credit_score' => $user->credit_score ?? 500,
            'credit_utilization' => $this->calculateCreditUtilization($user),
            'payment_history' => [
                'on_time_payments' => $repayments->where('status', 'paid')->where('paid_on_time', true)->count(),
                'late_payments' => $repayments->where('status', 'paid')->where('paid_on_time', false)->count(),
                'missed_payments' => $repayments->where('status', 'overdue')->count(),
                'payment_score' => $this->calculatePaymentScore($repayments)
            ],
            'loan_history' => [
                'total_loans' => $loans->count(),
                'successful_completions' => $loans->where('status', 'completed')->count(),
                'defaults' => $loans->where('status', 'defaulted')->count(),
                'average_loan_amount' => $loans->avg('amount')
            ],
            'eligibility_factors' => [
                'income_verification' => !empty($user->monthly_income),
                'employment_verification' => !empty($user->occupation),
                'bank_statement' => $user->kv == 1,
                'credit_history_length' => $loans->count() > 0 ? $loans->min('created_at')->diffInMonths(now()) : 0
            ]
        ];

        return response()->json([
            'status' => 'success',
            'data' => $analytics
        ]);
    }

    private function calculateCreditUtilization($user): float
    {
        $activeLoans = $user->loans()->whereIn('status', ['disbursed', 'repaying'])->get();
        $totalCreditUsed = $activeLoans->sum('outstanding_balance');
        
        // Assume max credit limit based on income (simplified)
        $maxCreditLimit = ($user->monthly_income ?? 50000) * 6; // 6 months income
        
        return $maxCreditLimit > 0 ? ($totalCreditUsed / $maxCreditLimit) * 100 : 0;
    }

    private function calculatePaymentScore($repayments): float
    {
        if ($repayments->count() == 0) return 100;
        
        $onTimePayments = $repayments->where('status', 'paid')->where('paid_on_time', true)->count();
        $totalPayments = $repayments->where('status', 'paid')->count();
        
        return $totalPayments > 0 ? ($onTimePayments / $totalPayments) * 100 : 100;
    }
}
