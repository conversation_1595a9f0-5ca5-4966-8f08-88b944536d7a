<?php

namespace App\Models;

use App\Traits\Searchable;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasOne;
use Illuminate\Support\Facades\Hash;
use Carbon\Carbon;

class UserCard extends Model
{
    use Searchable;

    protected $fillable = [
        'user_id',
        'card_type',
        'account_type',
        'card_name',
        'card_number',
        'masked_card_number',
        'expiry_date',
        'cvv',
        'pin_hash',
        'pin_set',
        'pin_locked',
        'failed_attempts',
        'daily_limit',
        'monthly_limit',
        'single_transaction_limit',
        'status',
        'delivery_address',
        'delivery_status',
        'tracking_number',
        'card_fee',
        'is_frozen',
        'frozen_at'
    ];

    protected $casts = [
        'daily_limit' => 'decimal:8',
        'monthly_limit' => 'decimal:8',
        'single_transaction_limit' => 'decimal:8',
        'card_fee' => 'decimal:8',
        'pin_set' => 'boolean',
        'pin_locked' => 'boolean',
        'is_frozen' => 'boolean',
        'frozen_at' => 'datetime'
    ];

    protected $hidden = [
        'card_number',
        'cvv',
        'pin_hash'
    ];

    protected $appends = [
        'is_active',
        'is_expired',
        'days_until_expiry',
        'can_transact',
        'monthly_spending',
        'daily_spending',
        'available_daily_limit',
        'available_monthly_limit'
    ];

    // Relationships
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    public function securitySettings(): HasOne
    {
        return $this->hasOne(CardSecuritySetting::class, 'card_id');
    }

    public function transactions(): HasMany
    {
        return $this->hasMany(CardTransaction::class, 'card_id');
    }

    public function pinHistory(): HasMany
    {
        return $this->hasMany(CardPinHistory::class, 'card_id');
    }

    public function deliveryTracking(): HasMany
    {
        return $this->hasMany(CardDeliveryTracking::class, 'card_id');
    }

    // Accessors
    public function getIsActiveAttribute(): bool
    {
        return $this->status === 'active' && !$this->is_expired && !$this->is_frozen;
    }

    public function getIsExpiredAttribute(): bool
    {
        if (!$this->expiry_date) return false;
        
        $expiryDate = Carbon::createFromFormat('m/Y', $this->expiry_date)->endOfMonth();
        return Carbon::now()->gt($expiryDate);
    }

    public function getDaysUntilExpiryAttribute(): ?int
    {
        if (!$this->expiry_date) return null;
        
        $expiryDate = Carbon::createFromFormat('m/Y', $this->expiry_date)->endOfMonth();
        return Carbon::now()->diffInDays($expiryDate, false);
    }

    public function getCanTransactAttribute(): bool
    {
        return $this->is_active && $this->pin_set && !$this->pin_locked;
    }

    public function getMonthlySpendingAttribute(): float
    {
        return $this->transactions()
                   ->where('type', 'purchase')
                   ->where('status', 'successful')
                   ->whereMonth('created_at', Carbon::now()->month)
                   ->whereYear('created_at', Carbon::now()->year)
                   ->sum('amount');
    }

    public function getDailySpendingAttribute(): float
    {
        return $this->transactions()
                   ->where('type', 'purchase')
                   ->where('status', 'successful')
                   ->whereDate('created_at', Carbon::today())
                   ->sum('amount');
    }

    public function getAvailableDailyLimitAttribute(): float
    {
        return max(0, $this->daily_limit - $this->daily_spending);
    }

    public function getAvailableMonthlyLimitAttribute(): float
    {
        return max(0, $this->monthly_limit - $this->monthly_spending);
    }

    // Scopes
    public function scopeActive($query)
    {
        return $query->where('status', 'active');
    }

    public function scopePending($query)
    {
        return $query->where('status', 'pending');
    }

    public function scopeBlocked($query)
    {
        return $query->where('status', 'blocked');
    }

    public function scopeExpired($query)
    {
        return $query->where('status', 'expired');
    }

    public function scopeForUser($query, $userId)
    {
        return $query->where('user_id', $userId);
    }

    public function scopePhysical($query)
    {
        return $query->where('card_type', 'physical');
    }

    public function scopeVirtual($query)
    {
        return $query->where('card_type', 'virtual');
    }

    public function scopeFrozen($query)
    {
        return $query->where('is_frozen', true);
    }

    public function scopeUnfrozen($query)
    {
        return $query->where('is_frozen', false);
    }

    public function scopeNearExpiry($query, $days = 30)
    {
        $cutoffDate = Carbon::now()->addDays($days)->format('m/Y');
        return $query->where('expiry_date', '<=', $cutoffDate);
    }

    // PIN Management Methods
    public function createPin(string $pin): array
    {
        if ($this->pin_set) {
            return ['success' => false, 'message' => 'PIN already set'];
        }

        if (!$this->validatePin($pin)) {
            return ['success' => false, 'message' => 'Invalid PIN format'];
        }

        $this->pin_hash = Hash::make($pin);
        $this->pin_set = true;
        $this->pin_locked = false;
        $this->failed_attempts = 0;
        $this->save();

        $this->logPinAction('created');

        return ['success' => true, 'message' => 'PIN created successfully'];
    }

    public function changePin(string $oldPin, string $newPin): array
    {
        if (!$this->pin_set) {
            return ['success' => false, 'message' => 'No PIN set'];
        }

        if ($this->pin_locked) {
            return ['success' => false, 'message' => 'PIN is locked'];
        }

        if (!Hash::check($oldPin, $this->pin_hash)) {
            $this->incrementFailedAttempts();
            return ['success' => false, 'message' => 'Incorrect current PIN'];
        }

        if (!$this->validatePin($newPin)) {
            return ['success' => false, 'message' => 'Invalid new PIN format'];
        }

        $this->pin_hash = Hash::make($newPin);
        $this->failed_attempts = 0;
        $this->save();

        $this->logPinAction('changed');

        return ['success' => true, 'message' => 'PIN changed successfully'];
    }

    public function resetPin(string $newPin): array
    {
        if (!$this->validatePin($newPin)) {
            return ['success' => false, 'message' => 'Invalid PIN format'];
        }

        $this->pin_hash = Hash::make($newPin);
        $this->pin_locked = false;
        $this->failed_attempts = 0;
        $this->save();

        $this->logPinAction('reset');

        return ['success' => true, 'message' => 'PIN reset successfully'];
    }

    public function unlockPin(): array
    {
        if (!$this->pin_locked) {
            return ['success' => false, 'message' => 'PIN is not locked'];
        }

        $this->pin_locked = false;
        $this->failed_attempts = 0;
        $this->save();

        $this->logPinAction('unlocked');

        return ['success' => true, 'message' => 'PIN unlocked successfully'];
    }

    public function verifyPin(string $pin): bool
    {
        if (!$this->pin_set || $this->pin_locked) {
            return false;
        }

        if (Hash::check($pin, $this->pin_hash)) {
            $this->failed_attempts = 0;
            $this->save();
            return true;
        }

        $this->incrementFailedAttempts();
        return false;
    }

    private function incrementFailedAttempts(): void
    {
        $this->failed_attempts++;
        
        if ($this->failed_attempts >= 3) {
            $this->pin_locked = true;
            $this->logPinAction('locked', 'Too many failed attempts');
        }
        
        $this->save();
    }

    private function validatePin(string $pin): bool
    {
        return preg_match('/^\d{4}$/', $pin);
    }

    private function logPinAction(string $action, string $reason = null): void
    {
        CardPinHistory::create([
            'card_id' => $this->id,
            'user_id' => $this->user_id,
            'action' => $action,
            'ip_address' => request()->ip(),
            'user_agent' => request()->userAgent(),
            'reason' => $reason
        ]);
    }

    // Card Management Methods
    public function freeze(string $reason = 'User requested'): array
    {
        if ($this->is_frozen) {
            return ['success' => false, 'message' => 'Card is already frozen'];
        }

        $this->is_frozen = true;
        $this->frozen_at = Carbon::now();
        $this->save();

        return ['success' => true, 'message' => 'Card frozen successfully'];
    }

    public function unfreeze(): array
    {
        if (!$this->is_frozen) {
            return ['success' => false, 'message' => 'Card is not frozen'];
        }

        $this->is_frozen = false;
        $this->frozen_at = null;
        $this->save();

        return ['success' => true, 'message' => 'Card unfrozen successfully'];
    }

    public function updateLimits(array $limits): array
    {
        $validLimits = ['daily_limit', 'monthly_limit', 'single_transaction_limit'];
        
        foreach ($limits as $key => $value) {
            if (in_array($key, $validLimits) && is_numeric($value) && $value >= 0) {
                $this->$key = $value;
            }
        }
        
        $this->save();

        return ['success' => true, 'message' => 'Limits updated successfully'];
    }

    public function canTransact(float $amount): array
    {
        if (!$this->can_transact) {
            return ['can_transact' => false, 'reason' => 'Card cannot transact'];
        }

        if ($amount > $this->single_transaction_limit) {
            return ['can_transact' => false, 'reason' => 'Amount exceeds single transaction limit'];
        }

        if ($amount > $this->available_daily_limit) {
            return ['can_transact' => false, 'reason' => 'Amount exceeds daily limit'];
        }

        if ($amount > $this->available_monthly_limit) {
            return ['can_transact' => false, 'reason' => 'Amount exceeds monthly limit'];
        }

        if ($this->user->balance < $amount) {
            return ['can_transact' => false, 'reason' => 'Insufficient balance'];
        }

        return ['can_transact' => true];
    }

    public function getUsageAnalytics($days = 30): array
    {
        $transactions = $this->transactions()
                            ->where('created_at', '>=', Carbon::now()->subDays($days))
                            ->where('status', 'successful')
                            ->get();

        return [
            'total_transactions' => $transactions->count(),
            'total_amount' => $transactions->sum('amount'),
            'average_transaction' => $transactions->avg('amount'),
            'by_type' => [
                'purchases' => $transactions->where('type', 'purchase')->sum('amount'),
                'withdrawals' => $transactions->where('type', 'withdrawal')->sum('amount'),
                'refunds' => $transactions->where('type', 'refund')->sum('amount')
            ],
            'by_channel' => [
                'pos' => $transactions->where('channel', 'pos')->sum('amount'),
                'atm' => $transactions->where('channel', 'atm')->sum('amount'),
                'online' => $transactions->where('channel', 'online')->sum('amount'),
                'contactless' => $transactions->where('channel', 'contactless')->sum('amount')
            ],
            'daily_average' => $transactions->sum('amount') / max(1, $days)
        ];
    }

    public static function generateCardNumber(): string
    {
        // Generate a 16-digit card number (simplified)
        $prefix = '5399'; // KojaPay prefix
        $middle = str_pad(mt_rand(0, 999999999999), 12, '0', STR_PAD_LEFT);
        return $prefix . substr($middle, 0, 12);
    }

    public static function generateCVV(): string
    {
        return str_pad(mt_rand(0, 999), 3, '0', STR_PAD_LEFT);
    }

    public static function generateExpiryDate(): string
    {
        return Carbon::now()->addYears(3)->format('m/Y');
    }
}
