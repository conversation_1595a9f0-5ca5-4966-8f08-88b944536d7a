class OrderResponseModel {
  String? status;
  Message? message;
  OrderData? data;

  OrderResponseModel({
    this.status,
    this.message,
    this.data,
  });

  factory OrderResponseModel.fromJson(Map<String, dynamic> json) => OrderResponseModel(
        status: json["status"],
        message: json["message"] == null ? null : Message.fromJson(json["message"]),
        data: json["data"] == null ? null : OrderData.fromJson(json["data"]),
      );

  Map<String, dynamic> toJson() => {
        "status": status,
        "message": message?.toJson(),
        "data": data?.toJson(),
      };
}

class Message {
  List<String>? success;
  List<String>? error;

  Message({
    this.success,
    this.error,
  });

  factory Message.fromJson(Map<String, dynamic> json) => Message(
        success: json["success"] == null ? [] : List<String>.from(json["success"]!.map((x) => x)),
        error: json["error"] == null ? [] : List<String>.from(json["error"]!.map((x) => x)),
      );

  Map<String, dynamic> toJson() => {
        "success": success == null ? [] : List<dynamic>.from(success!.map((x) => x)),
        "error": error == null ? [] : List<dynamic>.from(error!.map((x) => x)),
      };
}

class OrderData {
  List<Order>? orders;
  Order? order;
  Pagination? pagination;

  OrderData({
    this.orders,
    this.order,
    this.pagination,
  });

  factory OrderData.fromJson(Map<String, dynamic> json) => OrderData(
        orders: json["orders"] == null ? [] : List<Order>.from(json["orders"]!.map((x) => Order.fromJson(x))),
        order: json["order"] == null ? null : Order.fromJson(json["order"]),
        pagination: json["pagination"] == null ? null : Pagination.fromJson(json["pagination"]),
      );

  Map<String, dynamic> toJson() => {
        "orders": orders == null ? [] : List<dynamic>.from(orders!.map((x) => x.toJson())),
        "order": order?.toJson(),
        "pagination": pagination?.toJson(),
      };
}

class Order {
  int? id;
  String? orderNumber;
  int? userId;
  String? status;
  String? paymentStatus;
  String? paymentMethod;
  String? subtotal;
  String? tax;
  String? shipping;
  String? discount;
  String? total;
  String? currency;
  ShippingAddress? shippingAddress;
  BillingAddress? billingAddress;
  List<OrderItem>? items;
  List<OrderStatusHistory>? statusHistory;
  String? notes;
  String? trackingNumber;
  String? estimatedDelivery;
  bool? isEscrow;
  String? escrowStatus;
  String? createdAt;
  String? updatedAt;

  Order({
    this.id,
    this.orderNumber,
    this.userId,
    this.status,
    this.paymentStatus,
    this.paymentMethod,
    this.subtotal,
    this.tax,
    this.shipping,
    this.discount,
    this.total,
    this.currency,
    this.shippingAddress,
    this.billingAddress,
    this.items,
    this.statusHistory,
    this.notes,
    this.trackingNumber,
    this.estimatedDelivery,
    this.isEscrow,
    this.escrowStatus,
    this.createdAt,
    this.updatedAt,
  });

  factory Order.fromJson(Map<String, dynamic> json) => Order(
        id: json["id"],
        orderNumber: json["order_number"],
        userId: json["user_id"],
        status: json["status"],
        paymentStatus: json["payment_status"],
        paymentMethod: json["payment_method"],
        subtotal: json["subtotal"],
        tax: json["tax"],
        shipping: json["shipping"],
        discount: json["discount"],
        total: json["total"],
        currency: json["currency"],
        shippingAddress: json["shipping_address"] == null ? null : ShippingAddress.fromJson(json["shipping_address"]),
        billingAddress: json["billing_address"] == null ? null : BillingAddress.fromJson(json["billing_address"]),
        items: json["items"] == null ? [] : List<OrderItem>.from(json["items"]!.map((x) => OrderItem.fromJson(x))),
        statusHistory: json["status_history"] == null ? [] : List<OrderStatusHistory>.from(json["status_history"]!.map((x) => OrderStatusHistory.fromJson(x))),
        notes: json["notes"],
        trackingNumber: json["tracking_number"],
        estimatedDelivery: json["estimated_delivery"],
        isEscrow: json["is_escrow"] == 1,
        escrowStatus: json["escrow_status"],
        createdAt: json["created_at"],
        updatedAt: json["updated_at"],
      );

  Map<String, dynamic> toJson() => {
        "id": id,
        "order_number": orderNumber,
        "user_id": userId,
        "status": status,
        "payment_status": paymentStatus,
        "payment_method": paymentMethod,
        "subtotal": subtotal,
        "tax": tax,
        "shipping": shipping,
        "discount": discount,
        "total": total,
        "currency": currency,
        "shipping_address": shippingAddress?.toJson(),
        "billing_address": billingAddress?.toJson(),
        "items": items == null ? [] : List<dynamic>.from(items!.map((x) => x.toJson())),
        "status_history": statusHistory == null ? [] : List<dynamic>.from(statusHistory!.map((x) => x.toJson())),
        "notes": notes,
        "tracking_number": trackingNumber,
        "estimated_delivery": estimatedDelivery,
        "is_escrow": isEscrow == true ? 1 : 0,
        "escrow_status": escrowStatus,
        "created_at": createdAt,
        "updated_at": updatedAt,
      };
}

class OrderItem {
  int? id;
  int? orderId;
  int? productId;
  int? variantId;
  String? productName;
  String? variantName;
  String? price;
  int? quantity;
  String? total;
  String? productImage;
  int? storeId;
  String? storeName;

  OrderItem({
    this.id,
    this.orderId,
    this.productId,
    this.variantId,
    this.productName,
    this.variantName,
    this.price,
    this.quantity,
    this.total,
    this.productImage,
    this.storeId,
    this.storeName,
  });

  factory OrderItem.fromJson(Map<String, dynamic> json) => OrderItem(
        id: json["id"],
        orderId: json["order_id"],
        productId: json["product_id"],
        variantId: json["variant_id"],
        productName: json["product_name"],
        variantName: json["variant_name"],
        price: json["price"],
        quantity: json["quantity"],
        total: json["total"],
        productImage: json["product_image"],
        storeId: json["store_id"],
        storeName: json["store_name"],
      );

  Map<String, dynamic> toJson() => {
        "id": id,
        "order_id": orderId,
        "product_id": productId,
        "variant_id": variantId,
        "product_name": productName,
        "variant_name": variantName,
        "price": price,
        "quantity": quantity,
        "total": total,
        "product_image": productImage,
        "store_id": storeId,
        "store_name": storeName,
      };
}

class ShippingAddress {
  String? firstName;
  String? lastName;
  String? company;
  String? address1;
  String? address2;
  String? city;
  String? state;
  String? postalCode;
  String? country;
  String? phone;

  ShippingAddress({
    this.firstName,
    this.lastName,
    this.company,
    this.address1,
    this.address2,
    this.city,
    this.state,
    this.postalCode,
    this.country,
    this.phone,
  });

  factory ShippingAddress.fromJson(Map<String, dynamic> json) => ShippingAddress(
        firstName: json["first_name"],
        lastName: json["last_name"],
        company: json["company"],
        address1: json["address1"],
        address2: json["address2"],
        city: json["city"],
        state: json["state"],
        postalCode: json["postal_code"],
        country: json["country"],
        phone: json["phone"],
      );

  Map<String, dynamic> toJson() => {
        "first_name": firstName,
        "last_name": lastName,
        "company": company,
        "address1": address1,
        "address2": address2,
        "city": city,
        "state": state,
        "postal_code": postalCode,
        "country": country,
        "phone": phone,
      };
}

class BillingAddress {
  String? firstName;
  String? lastName;
  String? company;
  String? address1;
  String? address2;
  String? city;
  String? state;
  String? postalCode;
  String? country;
  String? phone;

  BillingAddress({
    this.firstName,
    this.lastName,
    this.company,
    this.address1,
    this.address2,
    this.city,
    this.state,
    this.postalCode,
    this.country,
    this.phone,
  });

  factory BillingAddress.fromJson(Map<String, dynamic> json) => BillingAddress(
        firstName: json["first_name"],
        lastName: json["last_name"],
        company: json["company"],
        address1: json["address1"],
        address2: json["address2"],
        city: json["city"],
        state: json["state"],
        postalCode: json["postal_code"],
        country: json["country"],
        phone: json["phone"],
      );

  Map<String, dynamic> toJson() => {
        "first_name": firstName,
        "last_name": lastName,
        "company": company,
        "address1": address1,
        "address2": address2,
        "city": city,
        "state": state,
        "postal_code": postalCode,
        "country": country,
        "phone": phone,
      };
}

class OrderStatusHistory {
  int? id;
  int? orderId;
  String? status;
  String? notes;
  String? createdAt;

  OrderStatusHistory({
    this.id,
    this.orderId,
    this.status,
    this.notes,
    this.createdAt,
  });

  factory OrderStatusHistory.fromJson(Map<String, dynamic> json) => OrderStatusHistory(
        id: json["id"],
        orderId: json["order_id"],
        status: json["status"],
        notes: json["notes"],
        createdAt: json["created_at"],
      );

  Map<String, dynamic> toJson() => {
        "id": id,
        "order_id": orderId,
        "status": status,
        "notes": notes,
        "created_at": createdAt,
      };
}

class Pagination {
  int? currentPage;
  int? lastPage;
  int? perPage;
  int? total;

  Pagination({
    this.currentPage,
    this.lastPage,
    this.perPage,
    this.total,
  });

  factory Pagination.fromJson(Map<String, dynamic> json) => Pagination(
        currentPage: json["current_page"],
        lastPage: json["last_page"],
        perPage: json["per_page"],
        total: json["total"],
      );

  Map<String, dynamic> toJson() => {
        "current_page": currentPage,
        "last_page": lastPage,
        "per_page": perPage,
        "total": total,
      };
}

// Create order request model
class CreateOrderRequest {
  String paymentMethod;
  bool useEscrow;
  ShippingAddress shippingAddress;
  BillingAddress? billingAddress;
  String? notes;
  String? couponCode;

  CreateOrderRequest({
    required this.paymentMethod,
    required this.useEscrow,
    required this.shippingAddress,
    this.billingAddress,
    this.notes,
    this.couponCode,
  });

  Map<String, dynamic> toJson() => {
        "payment_method": paymentMethod,
        "use_escrow": useEscrow,
        "shipping_address": shippingAddress.toJson(),
        if (billingAddress != null) "billing_address": billingAddress!.toJson(),
        if (notes != null) "notes": notes,
        if (couponCode != null) "coupon_code": couponCode,
      };
}
