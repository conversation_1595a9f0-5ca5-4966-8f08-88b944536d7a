import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:viserpay/core/utils/app_theme.dart';

class ThemeController extends GetxController {
  static const String _themeKey = 'app_theme';
  
  final SharedPreferences _sharedPreferences = Get.find();
  
  // Current theme
  Rx<AppThemeType> currentTheme = AppThemeType.personal.obs;
  
  @override
  void onInit() {
    super.onInit();
    _loadTheme();
  }

  // Load saved theme from preferences
  void _loadTheme() {
    final String? savedTheme = _sharedPreferences.getString(_themeKey);
    if (savedTheme != null) {
      currentTheme.value = AppThemeType.values.firstWhere(
        (theme) => theme.toString() == savedTheme,
        orElse: () => AppThemeType.personal,
      );
    }
  }

  // Save theme to preferences
  Future<void> _saveTheme(AppThemeType theme) async {
    await _sharedPreferences.setString(_themeKey, theme.toString());
  }

  // Change theme
  Future<void> changeTheme(AppThemeType newTheme) async {
    currentTheme.value = newTheme;
    await _saveTheme(newTheme);
    
    // Update the app theme
    Get.changeTheme(AppTheme.getTheme(newTheme));
    
    // Show feedback to user
    Get.snackbar(
      'Theme Changed',
      'Successfully switched to ${_getThemeName(newTheme)} theme',
      snackPosition: SnackPosition.BOTTOM,
      duration: const Duration(seconds: 2),
      backgroundColor: AppTheme.getTheme(newTheme).primaryColor.withOpacity(0.1),
      colorText: AppTheme.getTheme(newTheme).primaryColor,
    );
  }

  // Get current theme data
  ThemeData get currentThemeData => AppTheme.getTheme(currentTheme.value);

  // Get theme name for display
  String _getThemeName(AppThemeType theme) {
    switch (theme) {
      case AppThemeType.personal:
        return 'Personal';
      case AppThemeType.business:
        return 'Business';
      case AppThemeType.cyberpunk:
        return 'Cyberpunk';
      case AppThemeType.kids:
        return 'Kids';
    }
  }

  // Get all available themes with their display names
  Map<AppThemeType, String> get availableThemes => {
    AppThemeType.personal: 'Personal',
    AppThemeType.business: 'Business',
    AppThemeType.kids: 'Kids',
  };

  // Check if theme is dark (none of our themes are dark now)
  bool get isDarkTheme => false;

  // Get theme icon
  IconData getThemeIcon(AppThemeType theme) {
    switch (theme) {
      case AppThemeType.personal:
        return Icons.person;
      case AppThemeType.business:
        return Icons.business;
      case AppThemeType.kids:
        return Icons.child_care;
    }
  }

  // Get theme description
  String getThemeDescription(AppThemeType theme) {
    switch (theme) {
      case AppThemeType.personal:
        return 'Clean and modern design for personal use';
      case AppThemeType.business:
        return 'Professional and minimal design for business';
      case AppThemeType.kids:
        return 'Colorful and playful design for children';
    }
  }

  // Get theme primary color
  Color getThemePrimaryColor(AppThemeType theme) {
    return AppTheme.getTheme(theme).primaryColor;
  }
}
