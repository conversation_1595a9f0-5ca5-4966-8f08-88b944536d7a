class ProductResponseModel {
  String? status;
  Message? message;
  Data? data;

  ProductResponseModel({
    this.status,
    this.message,
    this.data,
  });

  factory ProductResponseModel.fromJson(Map<String, dynamic> json) => ProductResponseModel(
        status: json["status"],
        message: json["message"] == null ? null : Message.fromJson(json["message"]),
        data: json["data"] == null ? null : Data.fromJson(json["data"]),
      );

  Map<String, dynamic> toJson() => {
        "status": status,
        "message": message?.toJson(),
        "data": data?.toJson(),
      };
}

class Message {
  List<String>? success;
  List<String>? error;

  Message({
    this.success,
    this.error,
  });

  factory Message.fromJson(Map<String, dynamic> json) => Message(
        success: json["success"] == null ? [] : List<String>.from(json["success"]!.map((x) => x)),
        error: json["error"] == null ? [] : List<String>.from(json["error"]!.map((x) => x)),
      );

  Map<String, dynamic> toJson() => {
        "success": success == null ? [] : List<dynamic>.from(success!.map((x) => x)),
        "error": error == null ? [] : List<dynamic>.from(error!.map((x) => x)),
      };
}

class Data {
  List<Product>? products;
  List<Category>? categories;
  List<Store>? stores;
  Pagination? pagination;

  Data({
    this.products,
    this.categories,
    this.stores,
    this.pagination,
  });

  factory Data.fromJson(Map<String, dynamic> json) => Data(
        products: json["products"] == null ? [] : List<Product>.from(json["products"]!.map((x) => Product.fromJson(x))),
        categories: json["categories"] == null ? [] : List<Category>.from(json["categories"]!.map((x) => Category.fromJson(x))),
        stores: json["stores"] == null ? [] : List<Store>.from(json["stores"]!.map((x) => Store.fromJson(x))),
        pagination: json["pagination"] == null ? null : Pagination.fromJson(json["pagination"]),
      );

  Map<String, dynamic> toJson() => {
        "products": products == null ? [] : List<dynamic>.from(products!.map((x) => x.toJson())),
        "categories": categories == null ? [] : List<dynamic>.from(categories!.map((x) => x.toJson())),
        "stores": stores == null ? [] : List<dynamic>.from(stores!.map((x) => x.toJson())),
        "pagination": pagination?.toJson(),
      };
}

class Product {
  int? id;
  int? storeId;
  int? categoryId;
  String? name;
  String? description;
  String? shortDescription;
  String? price;
  String? comparePrice;
  String? costPrice;
  int? quantity;
  String? sku;
  String? weight;
  List<String>? images;
  String? featuredImage;
  List<ProductVariant>? variants;
  List<String>? tags;
  String? status;
  bool? isFeatured;
  bool? isDigital;
  bool? requiresShipping;
  String? metaTitle;
  String? metaDescription;
  double? rating;
  int? reviewCount;
  int? soldCount;
  Store? store;
  Category? category;
  String? createdAt;
  String? updatedAt;

  Product({
    this.id,
    this.storeId,
    this.categoryId,
    this.name,
    this.description,
    this.shortDescription,
    this.price,
    this.comparePrice,
    this.costPrice,
    this.quantity,
    this.sku,
    this.weight,
    this.images,
    this.featuredImage,
    this.variants,
    this.tags,
    this.status,
    this.isFeatured,
    this.isDigital,
    this.requiresShipping,
    this.metaTitle,
    this.metaDescription,
    this.rating,
    this.reviewCount,
    this.soldCount,
    this.store,
    this.category,
    this.createdAt,
    this.updatedAt,
  });

  factory Product.fromJson(Map<String, dynamic> json) => Product(
        id: json["id"],
        storeId: json["store_id"],
        categoryId: json["category_id"],
        name: json["name"],
        description: json["description"],
        shortDescription: json["short_description"],
        price: json["price"],
        comparePrice: json["compare_price"],
        costPrice: json["cost_price"],
        quantity: json["quantity"],
        sku: json["sku"],
        weight: json["weight"],
        images: json["images"] == null ? [] : List<String>.from(json["images"]!.map((x) => x)),
        featuredImage: json["featured_image"],
        variants: json["variants"] == null ? [] : List<ProductVariant>.from(json["variants"]!.map((x) => ProductVariant.fromJson(x))),
        tags: json["tags"] == null ? [] : List<String>.from(json["tags"]!.map((x) => x)),
        status: json["status"],
        isFeatured: json["is_featured"] == 1,
        isDigital: json["is_digital"] == 1,
        requiresShipping: json["requires_shipping"] == 1,
        metaTitle: json["meta_title"],
        metaDescription: json["meta_description"],
        rating: json["rating"]?.toDouble(),
        reviewCount: json["review_count"],
        soldCount: json["sold_count"],
        store: json["store"] == null ? null : Store.fromJson(json["store"]),
        category: json["category"] == null ? null : Category.fromJson(json["category"]),
        createdAt: json["created_at"],
        updatedAt: json["updated_at"],
      );

  Map<String, dynamic> toJson() => {
        "id": id,
        "store_id": storeId,
        "category_id": categoryId,
        "name": name,
        "description": description,
        "short_description": shortDescription,
        "price": price,
        "compare_price": comparePrice,
        "cost_price": costPrice,
        "quantity": quantity,
        "sku": sku,
        "weight": weight,
        "images": images == null ? [] : List<dynamic>.from(images!.map((x) => x)),
        "featured_image": featuredImage,
        "variants": variants == null ? [] : List<dynamic>.from(variants!.map((x) => x.toJson())),
        "tags": tags == null ? [] : List<dynamic>.from(tags!.map((x) => x)),
        "status": status,
        "is_featured": isFeatured == true ? 1 : 0,
        "is_digital": isDigital == true ? 1 : 0,
        "requires_shipping": requiresShipping == true ? 1 : 0,
        "meta_title": metaTitle,
        "meta_description": metaDescription,
        "rating": rating,
        "review_count": reviewCount,
        "sold_count": soldCount,
        "store": store?.toJson(),
        "category": category?.toJson(),
        "created_at": createdAt,
        "updated_at": updatedAt,
      };
}

class ProductVariant {
  int? id;
  int? productId;
  String? name;
  String? value;
  String? price;
  int? quantity;
  String? sku;
  String? image;

  ProductVariant({
    this.id,
    this.productId,
    this.name,
    this.value,
    this.price,
    this.quantity,
    this.sku,
    this.image,
  });

  factory ProductVariant.fromJson(Map<String, dynamic> json) => ProductVariant(
        id: json["id"],
        productId: json["product_id"],
        name: json["name"],
        value: json["value"],
        price: json["price"],
        quantity: json["quantity"],
        sku: json["sku"],
        image: json["image"],
      );

  Map<String, dynamic> toJson() => {
        "id": id,
        "product_id": productId,
        "name": name,
        "value": value,
        "price": price,
        "quantity": quantity,
        "sku": sku,
        "image": image,
      };
}

class Category {
  int? id;
  String? name;
  String? description;
  String? image;
  String? icon;
  int? parentId;
  int? sortOrder;
  String? status;
  List<Category>? children;

  Category({
    this.id,
    this.name,
    this.description,
    this.image,
    this.icon,
    this.parentId,
    this.sortOrder,
    this.status,
    this.children,
  });

  factory Category.fromJson(Map<String, dynamic> json) => Category(
        id: json["id"],
        name: json["name"],
        description: json["description"],
        image: json["image"],
        icon: json["icon"],
        parentId: json["parent_id"],
        sortOrder: json["sort_order"],
        status: json["status"],
        children: json["children"] == null ? [] : List<Category>.from(json["children"]!.map((x) => Category.fromJson(x))),
      );

  Map<String, dynamic> toJson() => {
        "id": id,
        "name": name,
        "description": description,
        "image": image,
        "icon": icon,
        "parent_id": parentId,
        "sort_order": sortOrder,
        "status": status,
        "children": children == null ? [] : List<dynamic>.from(children!.map((x) => x.toJson())),
      };
}

class Store {
  int? id;
  int? userId;
  String? name;
  String? description;
  String? logo;
  String? banner;
  String? address;
  String? city;
  String? state;
  String? country;
  String? phone;
  String? email;
  String? website;
  double? latitude;
  double? longitude;
  String? status;
  bool? isVerified;
  double? rating;
  int? reviewCount;
  int? productCount;
  String? createdAt;
  String? updatedAt;

  Store({
    this.id,
    this.userId,
    this.name,
    this.description,
    this.logo,
    this.banner,
    this.address,
    this.city,
    this.state,
    this.country,
    this.phone,
    this.email,
    this.website,
    this.latitude,
    this.longitude,
    this.status,
    this.isVerified,
    this.rating,
    this.reviewCount,
    this.productCount,
    this.createdAt,
    this.updatedAt,
  });

  factory Store.fromJson(Map<String, dynamic> json) => Store(
        id: json["id"],
        userId: json["user_id"],
        name: json["name"],
        description: json["description"],
        logo: json["logo"],
        banner: json["banner"],
        address: json["address"],
        city: json["city"],
        state: json["state"],
        country: json["country"],
        phone: json["phone"],
        email: json["email"],
        website: json["website"],
        latitude: json["latitude"]?.toDouble(),
        longitude: json["longitude"]?.toDouble(),
        status: json["status"],
        isVerified: json["is_verified"] == 1,
        rating: json["rating"]?.toDouble(),
        reviewCount: json["review_count"],
        productCount: json["product_count"],
        createdAt: json["created_at"],
        updatedAt: json["updated_at"],
      );

  Map<String, dynamic> toJson() => {
        "id": id,
        "user_id": userId,
        "name": name,
        "description": description,
        "logo": logo,
        "banner": banner,
        "address": address,
        "city": city,
        "state": state,
        "country": country,
        "phone": phone,
        "email": email,
        "website": website,
        "latitude": latitude,
        "longitude": longitude,
        "status": status,
        "is_verified": isVerified == true ? 1 : 0,
        "rating": rating,
        "review_count": reviewCount,
        "product_count": productCount,
        "created_at": createdAt,
        "updated_at": updatedAt,
      };
}

class Pagination {
  int? currentPage;
  int? lastPage;
  int? perPage;
  int? total;
  String? nextPageUrl;
  String? prevPageUrl;

  Pagination({
    this.currentPage,
    this.lastPage,
    this.perPage,
    this.total,
    this.nextPageUrl,
    this.prevPageUrl,
  });

  factory Pagination.fromJson(Map<String, dynamic> json) => Pagination(
        currentPage: json["current_page"],
        lastPage: json["last_page"],
        perPage: json["per_page"],
        total: json["total"],
        nextPageUrl: json["next_page_url"],
        prevPageUrl: json["prev_page_url"],
      );

  Map<String, dynamic> toJson() => {
        "current_page": currentPage,
        "last_page": lastPage,
        "per_page": perPage,
        "total": total,
        "next_page_url": nextPageUrl,
        "prev_page_url": prevPageUrl,
      };
}
