<?php

namespace App\Models;

use App\Traits\Searchable;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;

class LoanProduct extends Model
{
    use Searchable;

    protected $fillable = [
        'name',
        'type',
        'description',
        'minimum_amount',
        'maximum_amount',
        'interest_rate',
        'duration_min_months',
        'duration_max_months',
        'processing_fee_rate',
        'late_payment_penalty',
        'eligibility_criteria',
        'required_documents',
        'is_active',
        'admin_configurable_rate',
        'max_active_loans'
    ];

    protected $casts = [
        'minimum_amount' => 'decimal:8',
        'maximum_amount' => 'decimal:8',
        'interest_rate' => 'decimal:2',
        'processing_fee_rate' => 'decimal:2',
        'late_payment_penalty' => 'decimal:2',
        'eligibility_criteria' => 'array',
        'required_documents' => 'array',
        'is_active' => 'boolean',
        'admin_configurable_rate' => 'boolean'
    ];

    protected $appends = [
        'type_display_name',
        'formatted_minimum_amount',
        'formatted_maximum_amount',
        'monthly_interest_rate',
        'total_loans_disbursed',
        'active_loans_count'
    ];

    // Relationships
    public function userLoans(): HasMany
    {
        return $this->hasMany(UserLoan::class, 'product_id');
    }

    public function eligibilityChecks(): HasMany
    {
        return $this->hasMany(LoanEligibilityCheck::class, 'product_id');
    }

    // Accessors
    public function getTypeDisplayNameAttribute(): string
    {
        return match($this->type) {
            'personal' => 'Personal Loan',
            'business' => 'Business Loan',
            'emergency' => 'Emergency Loan',
            'salary_advance' => 'Salary Advance',
            default => ucfirst($this->type)
        };
    }

    public function getFormattedMinimumAmountAttribute(): string
    {
        return '₦' . number_format($this->minimum_amount, 2);
    }

    public function getFormattedMaximumAmountAttribute(): string
    {
        return '₦' . number_format($this->maximum_amount, 2);
    }

    public function getMonthlyInterestRateAttribute(): float
    {
        return $this->interest_rate / 12;
    }

    public function getTotalLoansDisbursedAttribute(): float
    {
        return $this->userLoans()
                   ->whereIn('status', ['disbursed', 'repaying', 'completed'])
                   ->sum('amount');
    }

    public function getActiveLoansCountAttribute(): int
    {
        return $this->userLoans()
                   ->whereIn('status', ['disbursed', 'repaying'])
                   ->count();
    }

    // Scopes
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    public function scopeByType($query, $type)
    {
        return $query->where('type', $type);
    }

    public function scopePersonal($query)
    {
        return $query->where('type', 'personal');
    }

    public function scopeBusiness($query)
    {
        return $query->where('type', 'business');
    }

    public function scopeEmergency($query)
    {
        return $query->where('type', 'emergency');
    }

    public function scopeConfigurableRate($query)
    {
        return $query->where('admin_configurable_rate', true);
    }

    // Methods
    public function calculateMonthlyPayment(float $amount, int $durationMonths): float
    {
        $monthlyRate = $this->monthly_interest_rate / 100;
        
        if ($monthlyRate == 0) {
            return $amount / $durationMonths;
        }
        
        $numerator = $amount * $monthlyRate * pow(1 + $monthlyRate, $durationMonths);
        $denominator = pow(1 + $monthlyRate, $durationMonths) - 1;
        
        return $numerator / $denominator;
    }

    public function calculateTotalRepayment(float $amount, int $durationMonths): float
    {
        $monthlyPayment = $this->calculateMonthlyPayment($amount, $durationMonths);
        return $monthlyPayment * $durationMonths;
    }

    public function calculateProcessingFee(float $amount): float
    {
        return $amount * ($this->processing_fee_rate / 100);
    }

    public function checkEligibility($userId, float $requestedAmount, int $durationMonths): array
    {
        $user = User::find($userId);
        if (!$user) {
            return [
                'eligible' => false,
                'reasons' => ['User not found']
            ];
        }

        $reasons = [];
        $eligible = true;

        // Check if product is active
        if (!$this->is_active) {
            $eligible = false;
            $reasons[] = 'Loan product is not available';
        }

        // Check amount limits
        if ($requestedAmount < $this->minimum_amount) {
            $eligible = false;
            $reasons[] = "Minimum loan amount is {$this->formatted_minimum_amount}";
        }

        if ($requestedAmount > $this->maximum_amount) {
            $eligible = false;
            $reasons[] = "Maximum loan amount is {$this->formatted_maximum_amount}";
        }

        // Check duration limits
        if ($durationMonths < $this->duration_min_months) {
            $eligible = false;
            $reasons[] = "Minimum loan duration is {$this->duration_min_months} months";
        }

        if ($durationMonths > $this->duration_max_months) {
            $eligible = false;
            $reasons[] = "Maximum loan duration is {$this->duration_max_months} months";
        }

        // Check existing active loans
        $activeLoans = $user->loans()
                           ->whereIn('status', ['approved', 'disbursed', 'repaying'])
                           ->count();

        if ($activeLoans >= $this->max_active_loans) {
            $eligible = false;
            $reasons[] = "Maximum of {$this->max_active_loans} active loans allowed";
        }

        // Check credit score
        $creditScore = $user->creditScore;
        if ($creditScore && $creditScore->score < 500) {
            $eligible = false;
            $reasons[] = 'Credit score too low';
        }

        // Check KYC status
        if ($user->kv != 1) {
            $eligible = false;
            $reasons[] = 'KYC verification required';
        }

        // Check account age (minimum 30 days)
        if ($user->created_at->diffInDays(now()) < 30) {
            $eligible = false;
            $reasons[] = 'Account must be at least 30 days old';
        }

        // Check eligibility criteria from product settings
        if ($this->eligibility_criteria) {
            foreach ($this->eligibility_criteria as $criterion => $requirement) {
                switch ($criterion) {
                    case 'minimum_monthly_income':
                        if ($user->monthly_income < $requirement) {
                            $eligible = false;
                            $reasons[] = "Minimum monthly income of ₦" . number_format($requirement) . " required";
                        }
                        break;
                    
                    case 'minimum_account_balance':
                        if ($user->balance < $requirement) {
                            $eligible = false;
                            $reasons[] = "Minimum account balance of ₦" . number_format($requirement) . " required";
                        }
                        break;
                    
                    case 'minimum_transaction_volume':
                        $transactionVolume = $user->transactions()
                                                 ->where('created_at', '>=', now()->subMonths(3))
                                                 ->sum('amount');
                        if ($transactionVolume < $requirement) {
                            $eligible = false;
                            $reasons[] = "Minimum 3-month transaction volume of ₦" . number_format($requirement) . " required";
                        }
                        break;
                }
            }
        }

        // Calculate recommended amount and rate based on credit score
        $recommendedAmount = $requestedAmount;
        $recommendedRate = $this->interest_rate;

        if ($creditScore) {
            // Adjust based on credit score
            if ($creditScore->score >= 750) {
                $recommendedRate *= 0.9; // 10% discount
            } elseif ($creditScore->score >= 650) {
                $recommendedRate *= 0.95; // 5% discount
            } elseif ($creditScore->score < 550) {
                $recommendedRate *= 1.2; // 20% premium
                $recommendedAmount = min($recommendedAmount, $this->maximum_amount * 0.5);
            }
        }

        return [
            'eligible' => $eligible,
            'reasons' => $reasons,
            'recommended_amount' => $eligible ? $recommendedAmount : 0,
            'recommended_rate' => $eligible ? $recommendedRate : $this->interest_rate,
            'monthly_payment' => $eligible ? $this->calculateMonthlyPayment($recommendedAmount, $durationMonths) : 0,
            'total_repayment' => $eligible ? $this->calculateTotalRepayment($recommendedAmount, $durationMonths) : 0,
            'processing_fee' => $eligible ? $this->calculateProcessingFee($recommendedAmount) : 0
        ];
    }

    public function getPerformanceMetrics(): array
    {
        $loans = $this->userLoans();
        
        $totalDisbursed = $loans->whereIn('status', ['disbursed', 'repaying', 'completed'])->sum('amount');
        $totalRepaid = $loans->whereIn('status', ['completed'])->sum('amount');
        $defaultedAmount = $loans->where('status', 'defaulted')->sum('amount');
        
        $defaultRate = $totalDisbursed > 0 ? ($defaultedAmount / $totalDisbursed) * 100 : 0;
        $completionRate = $loans->count() > 0 ? 
                         ($loans->where('status', 'completed')->count() / $loans->count()) * 100 : 0;

        return [
            'total_loans' => $loans->count(),
            'total_disbursed' => $totalDisbursed,
            'total_repaid' => $totalRepaid,
            'defaulted_amount' => $defaultedAmount,
            'default_rate' => round($defaultRate, 2),
            'completion_rate' => round($completionRate, 2),
            'average_loan_amount' => $loans->avg('amount'),
            'active_loans' => $this->active_loans_count
        ];
    }

    public static function getDefaultProducts(): array
    {
        return [
            [
                'name' => 'Personal Loan',
                'type' => 'personal',
                'description' => 'Quick personal loans for your immediate needs',
                'minimum_amount' => 10000,
                'maximum_amount' => 500000,
                'interest_rate' => 18.0,
                'duration_min_months' => 1,
                'duration_max_months' => 12,
                'processing_fee_rate' => 2.0,
                'late_payment_penalty' => 5.0,
                'eligibility_criteria' => [
                    'minimum_monthly_income' => 50000,
                    'minimum_account_balance' => 5000
                ],
                'required_documents' => ['id_card', 'bank_statement'],
                'max_active_loans' => 1
            ],
            [
                'name' => 'Business Loan',
                'type' => 'business',
                'description' => 'Loans for business expansion and working capital',
                'minimum_amount' => 50000,
                'maximum_amount' => 2000000,
                'interest_rate' => 20.0,
                'duration_min_months' => 3,
                'duration_max_months' => 24,
                'processing_fee_rate' => 3.0,
                'late_payment_penalty' => 7.0,
                'eligibility_criteria' => [
                    'minimum_monthly_income' => 100000,
                    'minimum_transaction_volume' => 500000
                ],
                'required_documents' => ['business_registration', 'financial_statements'],
                'max_active_loans' => 2
            ],
            [
                'name' => 'Emergency Loan',
                'type' => 'emergency',
                'description' => 'Quick loans for emergency situations',
                'minimum_amount' => 5000,
                'maximum_amount' => 100000,
                'interest_rate' => 25.0,
                'duration_min_months' => 1,
                'duration_max_months' => 6,
                'processing_fee_rate' => 1.0,
                'late_payment_penalty' => 10.0,
                'eligibility_criteria' => [
                    'minimum_account_balance' => 1000
                ],
                'required_documents' => ['id_card'],
                'max_active_loans' => 1
            ]
        ];
    }
}
