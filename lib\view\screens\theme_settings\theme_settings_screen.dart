import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:viserpay/core/utils/app_theme.dart';
import 'package:viserpay/core/utils/dimensions.dart';
import 'package:viserpay/core/utils/my_color.dart';
import 'package:viserpay/core/utils/style.dart';
import 'package:viserpay/data/controller/theme/theme_controller.dart';
import 'package:viserpay/view/components/app-bar/custom_appbar.dart';

class ThemeSettingsScreen extends StatelessWidget {
  const ThemeSettingsScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: CustomAppBar(
        title: 'Theme Settings',
        isShowBackBtn: true,
      ),
      body: GetBuilder<ThemeController>(
        builder: (themeController) => Padding(
          padding: const EdgeInsets.all(Dimensions.space15),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'Choose Your Theme',
                style: boldLarge.copyWith(
                  color: MyColor.primaryTextColor,
                  fontSize: Dimensions.fontExtraLarge,
                ),
              ),
              const SizedBox(height: Dimensions.space10),
              Text(
                'Select a theme that matches your style and preferences',
                style: regularDefault.copyWith(
                  color: MyColor.contentTextColor,
                ),
              ),
              const SizedBox(height: Dimensions.space30),
              Expanded(
                child: GridView.builder(
                  gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                    crossAxisCount: 2,
                    crossAxisSpacing: Dimensions.space15,
                    mainAxisSpacing: Dimensions.space15,
                    childAspectRatio: 0.8,
                  ),
                  itemCount: themeController.availableThemes.length,
                  itemBuilder: (context, index) {
                    final themeType = AppThemeType.values[index];
                    final themeName = themeController.availableThemes[themeType]!;
                    final isSelected = themeController.currentTheme.value == themeType;
                    
                    return _ThemeCard(
                      themeType: themeType,
                      themeName: themeName,
                      isSelected: isSelected,
                      onTap: () => themeController.changeTheme(themeType),
                      themeController: themeController,
                    );
                  },
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}

class _ThemeCard extends StatelessWidget {
  final AppThemeType themeType;
  final String themeName;
  final bool isSelected;
  final VoidCallback onTap;
  final ThemeController themeController;

  const _ThemeCard({
    required this.themeType,
    required this.themeName,
    required this.isSelected,
    required this.onTap,
    required this.themeController,
  });

  @override
  Widget build(BuildContext context) {
    final themeColor = themeController.getThemePrimaryColor(themeType);
    
    return GestureDetector(
      onTap: onTap,
      child: AnimatedContainer(
        duration: const Duration(milliseconds: 300),
        decoration: BoxDecoration(
          color: MyColor.brandWhite,
          borderRadius: BorderRadius.circular(Dimensions.cardRadius),
          border: Border.all(
            color: isSelected ? themeColor : MyColor.borderColor,
            width: isSelected ? 3 : 1,
          ),
          boxShadow: [
            BoxShadow(
              offset: const Offset(0, 4),
              blurRadius: isSelected ? 12 : 6,
              color: isSelected 
                ? themeColor.withOpacity(0.3)
                : MyColor.brandBlack.withOpacity(0.1),
            ),
          ],
        ),
        child: Padding(
          padding: const EdgeInsets.all(Dimensions.space15),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              // Theme preview
              Container(
                width: 60,
                height: 60,
                decoration: BoxDecoration(
                  color: themeColor,
                  borderRadius: BorderRadius.circular(30),
                  boxShadow: [
                    BoxShadow(
                      offset: const Offset(0, 2),
                      blurRadius: 8,
                      color: themeColor.withOpacity(0.3),
                    ),
                  ],
                ),
                child: Icon(
                  themeController.getThemeIcon(themeType),
                  color: _getIconColor(themeType),
                  size: 30,
                ),
              ),
              const SizedBox(height: Dimensions.space15),
              
              // Theme name
              Text(
                themeName,
                style: semiBoldDefault.copyWith(
                  color: isSelected ? themeColor : MyColor.primaryTextColor,
                  fontSize: Dimensions.fontLarge,
                ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: Dimensions.space8),
              
              // Theme description
              Text(
                themeController.getThemeDescription(themeType),
                style: regularSmall.copyWith(
                  color: MyColor.contentTextColor,
                ),
                textAlign: TextAlign.center,
                maxLines: 3,
                overflow: TextOverflow.ellipsis,
              ),
              
              if (isSelected) ...[
                const SizedBox(height: Dimensions.space10),
                Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: Dimensions.space10,
                    vertical: Dimensions.space5,
                  ),
                  decoration: BoxDecoration(
                    color: themeColor.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(20),
                  ),
                  child: Text(
                    'Active',
                    style: mediumSmall.copyWith(
                      color: themeColor,
                    ),
                  ),
                ),
              ],
            ],
          ),
        ),
      ),
    );
  }

  Color _getIconColor(AppThemeType themeType) {
    switch (themeType) {
      case AppThemeType.personal:
        return MyColor.brandWhite;
      case AppThemeType.business:
        return MyColor.brandWhite;
      case AppThemeType.cyberpunk:
        return MyColor.brandBlack;
      case AppThemeType.kids:
        return MyColor.brandWhite;
    }
  }
}
