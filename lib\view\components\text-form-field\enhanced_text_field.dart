import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:viserpay/core/utils/dimensions.dart';
import 'package:viserpay/core/utils/my_color.dart';
import 'package:viserpay/core/utils/style.dart';

enum TextFieldVariant { filled, outlined, underlined }
enum TextFieldSize { small, medium, large }

class EnhancedTextField extends StatefulWidget {
  final String? label;
  final String? hint;
  final String? helperText;
  final String? errorText;
  final TextEditingController? controller;
  final FocusNode? focusNode;
  final TextInputType? keyboardType;
  final TextInputAction? textInputAction;
  final bool obscureText;
  final bool enabled;
  final bool readOnly;
  final int? maxLines;
  final int? maxLength;
  final List<TextInputFormatter>? inputFormatters;
  final FormFieldValidator<String>? validator;
  final ValueChanged<String>? onChanged;
  final VoidCallback? onTap;
  final VoidCallback? onEditingComplete;
  final ValueChanged<String>? onSubmitted;
  final Widget? prefixIcon;
  final Widget? suffixIcon;
  final String? prefixText;
  final String? suffixText;
  final TextFieldVariant variant;
  final TextFieldSize size;
  final bool hasFloatingLabel;
  final bool showCharacterCount;

  const EnhancedTextField({
    super.key,
    this.label,
    this.hint,
    this.helperText,
    this.errorText,
    this.controller,
    this.focusNode,
    this.keyboardType,
    this.textInputAction,
    this.obscureText = false,
    this.enabled = true,
    this.readOnly = false,
    this.maxLines = 1,
    this.maxLength,
    this.inputFormatters,
    this.validator,
    this.onChanged,
    this.onTap,
    this.onEditingComplete,
    this.onSubmitted,
    this.prefixIcon,
    this.suffixIcon,
    this.prefixText,
    this.suffixText,
    this.variant = TextFieldVariant.outlined,
    this.size = TextFieldSize.medium,
    this.hasFloatingLabel = true,
    this.showCharacterCount = false,
  });

  @override
  State<EnhancedTextField> createState() => _EnhancedTextFieldState();
}

class _EnhancedTextFieldState extends State<EnhancedTextField> {
  late FocusNode _focusNode;
  bool _isFocused = false;

  @override
  void initState() {
    super.initState();
    _focusNode = widget.focusNode ?? FocusNode();
    _focusNode.addListener(_onFocusChange);
  }

  @override
  void dispose() {
    if (widget.focusNode == null) {
      _focusNode.dispose();
    } else {
      _focusNode.removeListener(_onFocusChange);
    }
    super.dispose();
  }

  void _onFocusChange() {
    setState(() {
      _isFocused = _focusNode.hasFocus;
    });
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        if (widget.label != null && !widget.hasFloatingLabel) ...[
          Text(
            widget.label!,
            style: _getLabelStyle(),
          ),
          const SizedBox(height: Dimensions.space8),
        ],
        
        Container(
          decoration: _getContainerDecoration(),
          child: TextFormField(
            controller: widget.controller,
            focusNode: _focusNode,
            keyboardType: widget.keyboardType,
            textInputAction: widget.textInputAction,
            obscureText: widget.obscureText,
            enabled: widget.enabled,
            readOnly: widget.readOnly,
            maxLines: widget.maxLines,
            maxLength: widget.maxLength,
            inputFormatters: widget.inputFormatters,
            validator: widget.validator,
            onChanged: widget.onChanged,
            onTap: widget.onTap,
            onEditingComplete: widget.onEditingComplete,
            onFieldSubmitted: widget.onSubmitted,
            style: _getTextStyle(),
            decoration: _getInputDecoration(),
          ),
        ),
        
        if (widget.helperText != null || widget.errorText != null || widget.showCharacterCount) ...[
          const SizedBox(height: Dimensions.space5),
          Row(
            children: [
              Expanded(
                child: Text(
                  widget.errorText ?? widget.helperText ?? '',
                  style: _getHelperTextStyle(),
                ),
              ),
              if (widget.showCharacterCount && widget.maxLength != null)
                Text(
                  '${widget.controller?.text.length ?? 0}/${widget.maxLength}',
                  style: _getHelperTextStyle(),
                ),
            ],
          ),
        ],
      ],
    );
  }

  BoxDecoration? _getContainerDecoration() {
    if (widget.variant == TextFieldVariant.filled) {
      return BoxDecoration(
        color: _isFocused 
          ? MyColor.primaryColor.withOpacity(0.05)
          : MyColor.colorGrey2,
        borderRadius: BorderRadius.circular(_getBorderRadius()),
        border: _isFocused 
          ? Border.all(color: MyColor.primaryColor, width: 2)
          : null,
      );
    }
    return null;
  }

  InputDecoration _getInputDecoration() {
    final borderRadius = BorderRadius.circular(_getBorderRadius());
    
    return InputDecoration(
      labelText: widget.hasFloatingLabel ? widget.label : null,
      hintText: widget.hint,
      prefixIcon: widget.prefixIcon,
      suffixIcon: widget.suffixIcon,
      prefixText: widget.prefixText,
      suffixText: widget.suffixText,
      contentPadding: _getContentPadding(),
      
      // Label styling
      labelStyle: _getLabelStyle(),
      floatingLabelStyle: _getFloatingLabelStyle(),
      
      // Hint styling
      hintStyle: _getHintStyle(),
      
      // Border styling
      border: _getBorder(borderRadius),
      enabledBorder: _getEnabledBorder(borderRadius),
      focusedBorder: _getFocusedBorder(borderRadius),
      errorBorder: _getErrorBorder(borderRadius),
      focusedErrorBorder: _getFocusedErrorBorder(borderRadius),
      disabledBorder: _getDisabledBorder(borderRadius),
      
      // Fill
      filled: widget.variant == TextFieldVariant.filled,
      fillColor: widget.variant == TextFieldVariant.filled 
        ? (_isFocused 
          ? MyColor.primaryColor.withOpacity(0.05)
          : MyColor.colorGrey2)
        : null,
      
      // Counter
      counterText: widget.showCharacterCount ? null : '',
    );
  }

  EdgeInsets _getContentPadding() {
    switch (widget.size) {
      case TextFieldSize.small:
        return const EdgeInsets.symmetric(
          horizontal: Dimensions.space12,
          vertical: Dimensions.space8,
        );
      case TextFieldSize.medium:
        return const EdgeInsets.symmetric(
          horizontal: Dimensions.space16,
          vertical: Dimensions.space12,
        );
      case TextFieldSize.large:
        return const EdgeInsets.symmetric(
          horizontal: Dimensions.space20,
          vertical: Dimensions.space16,
        );
    }
  }

  double _getBorderRadius() {
    switch (widget.size) {
      case TextFieldSize.small:
        return Dimensions.buttonRadius;
      case TextFieldSize.medium:
        return Dimensions.mediumRadius;
      case TextFieldSize.large:
        return Dimensions.largeRadius;
    }
  }

  TextStyle _getTextStyle() {
    switch (widget.size) {
      case TextFieldSize.small:
        return regularSmall.copyWith(color: MyColor.primaryTextColor);
      case TextFieldSize.medium:
        return regularDefault.copyWith(color: MyColor.primaryTextColor);
      case TextFieldSize.large:
        return regularLarge.copyWith(color: MyColor.primaryTextColor);
    }
  }

  TextStyle _getLabelStyle() {
    return mediumDefault.copyWith(
      color: MyColor.labelTextColor,
    );
  }

  TextStyle _getFloatingLabelStyle() {
    return mediumDefault.copyWith(
      color: _isFocused ? MyColor.primaryColor : MyColor.labelTextColor,
    );
  }

  TextStyle _getHintStyle() {
    return regularDefault.copyWith(
      color: MyColor.hintTextColor,
    );
  }

  TextStyle _getHelperTextStyle() {
    return regularSmall.copyWith(
      color: widget.errorText != null 
        ? MyColor.colorRed 
        : MyColor.contentTextColor,
    );
  }

  InputBorder _getBorder(BorderRadius borderRadius) {
    switch (widget.variant) {
      case TextFieldVariant.filled:
        return InputBorder.none;
      case TextFieldVariant.outlined:
        return OutlineInputBorder(
          borderRadius: borderRadius,
          borderSide: const BorderSide(color: MyColor.borderColor),
        );
      case TextFieldVariant.underlined:
        return const UnderlineInputBorder(
          borderSide: BorderSide(color: MyColor.borderColor),
        );
    }
  }

  InputBorder _getEnabledBorder(BorderRadius borderRadius) {
    return _getBorder(borderRadius);
  }

  InputBorder _getFocusedBorder(BorderRadius borderRadius) {
    switch (widget.variant) {
      case TextFieldVariant.filled:
        return InputBorder.none;
      case TextFieldVariant.outlined:
        return OutlineInputBorder(
          borderRadius: borderRadius,
          borderSide: const BorderSide(color: MyColor.primaryColor, width: 2),
        );
      case TextFieldVariant.underlined:
        return const UnderlineInputBorder(
          borderSide: BorderSide(color: MyColor.primaryColor, width: 2),
        );
    }
  }

  InputBorder _getErrorBorder(BorderRadius borderRadius) {
    switch (widget.variant) {
      case TextFieldVariant.filled:
        return InputBorder.none;
      case TextFieldVariant.outlined:
        return OutlineInputBorder(
          borderRadius: borderRadius,
          borderSide: const BorderSide(color: MyColor.colorRed),
        );
      case TextFieldVariant.underlined:
        return const UnderlineInputBorder(
          borderSide: BorderSide(color: MyColor.colorRed),
        );
    }
  }

  InputBorder _getFocusedErrorBorder(BorderRadius borderRadius) {
    switch (widget.variant) {
      case TextFieldVariant.filled:
        return InputBorder.none;
      case TextFieldVariant.outlined:
        return OutlineInputBorder(
          borderRadius: borderRadius,
          borderSide: const BorderSide(color: MyColor.colorRed, width: 2),
        );
      case TextFieldVariant.underlined:
        return const UnderlineInputBorder(
          borderSide: BorderSide(color: MyColor.colorRed, width: 2),
        );
    }
  }

  InputBorder _getDisabledBorder(BorderRadius borderRadius) {
    switch (widget.variant) {
      case TextFieldVariant.filled:
        return InputBorder.none;
      case TextFieldVariant.outlined:
        return OutlineInputBorder(
          borderRadius: borderRadius,
          borderSide: BorderSide(color: MyColor.borderColor.withOpacity(0.5)),
        );
      case TextFieldVariant.underlined:
        return UnderlineInputBorder(
          borderSide: BorderSide(color: MyColor.borderColor.withOpacity(0.5)),
        );
    }
  }
}
