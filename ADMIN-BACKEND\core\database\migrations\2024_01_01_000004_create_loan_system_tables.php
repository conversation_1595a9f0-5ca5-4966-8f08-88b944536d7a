<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Loan Products Table
        Schema::create('loan_products', function (Blueprint $table) {
            $table->id();
            $table->string('name');
            $table->enum('type', ['personal', 'business', 'emergency', 'salary_advance']);
            $table->text('description')->nullable();
            $table->decimal('minimum_amount', 28, 8);
            $table->decimal('maximum_amount', 28, 8);
            $table->decimal('interest_rate', 5, 2); // Annual percentage rate
            $table->integer('duration_min_months');
            $table->integer('duration_max_months');
            $table->decimal('processing_fee_rate', 5, 2)->default(0);
            $table->decimal('late_payment_penalty', 5, 2)->default(5); // Percentage
            $table->json('eligibility_criteria')->nullable();
            $table->json('required_documents')->nullable();
            $table->boolean('is_active')->default(true);
            $table->boolean('admin_configurable_rate')->default(true);
            $table->integer('max_active_loans')->default(1); // Max loans per user
            $table->timestamps();
            
            $table->index(['type', 'is_active']);
        });

        // User Credit Scores Table
        Schema::create('user_credit_scores', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('user_id');
            $table->integer('score')->default(500); // 300-850 range
            $table->enum('grade', ['A', 'B', 'C', 'D', 'F'])->default('C');
            $table->decimal('account_age_months', 8, 2)->default(0);
            $table->decimal('average_balance', 28, 8)->default(0);
            $table->integer('transaction_count')->default(0);
            $table->decimal('total_inflow', 28, 8)->default(0);
            $table->decimal('total_outflow', 28, 8)->default(0);
            $table->integer('loan_history_count')->default(0);
            $table->integer('successful_repayments')->default(0);
            $table->integer('late_payments')->default(0);
            $table->integer('defaults')->default(0);
            $table->date('last_calculated');
            $table->timestamps();
            
            $table->foreign('user_id')->references('id')->on('users')->onDelete('cascade');
            $table->unique('user_id');
            $table->index('score');
        });

        // User Loans Table
        Schema::create('user_loans', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('user_id');
            $table->unsignedBigInteger('product_id');
            $table->string('loan_number')->unique();
            $table->decimal('amount', 28, 8);
            $table->decimal('interest_rate', 5, 2);
            $table->integer('duration_months');
            $table->decimal('monthly_payment', 28, 8);
            $table->decimal('total_repayment', 28, 8);
            $table->decimal('outstanding_balance', 28, 8);
            $table->decimal('processing_fee', 28, 8)->default(0);
            $table->date('next_payment_date')->nullable();
            $table->enum('status', ['pending', 'approved', 'rejected', 'disbursed', 'repaying', 'completed', 'defaulted'])->default('pending');
            $table->text('purpose')->nullable();
            $table->decimal('monthly_income', 28, 8)->nullable();
            $table->string('employer_name')->nullable();
            $table->string('employment_type')->nullable();
            $table->string('guarantor_name')->nullable();
            $table->string('guarantor_phone', 20)->nullable();
            $table->string('guarantor_relationship')->nullable();
            $table->text('admin_notes')->nullable();
            $table->text('rejection_reason')->nullable();
            $table->unsignedBigInteger('approved_by')->nullable();
            $table->timestamp('approved_at')->nullable();
            $table->timestamp('disbursed_at')->nullable();
            $table->timestamp('completed_at')->nullable();
            $table->string('trx', 40)->unique();
            $table->timestamps();
            
            $table->foreign('user_id')->references('id')->on('users')->onDelete('cascade');
            $table->foreign('product_id')->references('id')->on('loan_products')->onDelete('cascade');
            $table->foreign('approved_by')->references('id')->on('admins')->onDelete('set null');
            $table->index(['user_id', 'status']);
            $table->index(['status', 'next_payment_date']);
        });

        // Loan Repayments Table
        Schema::create('loan_repayments', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('loan_id');
            $table->unsignedBigInteger('user_id');
            $table->decimal('amount', 28, 8);
            $table->date('payment_date');
            $table->date('due_date');
            $table->decimal('principal_amount', 28, 8);
            $table->decimal('interest_amount', 28, 8);
            $table->decimal('penalty_amount', 28, 8)->default(0);
            $table->decimal('outstanding_before', 28, 8);
            $table->decimal('outstanding_after', 28, 8);
            $table->enum('status', ['pending', 'paid', 'overdue', 'partial'])->default('pending');
            $table->enum('payment_method', ['wallet', 'card', 'bank_transfer', 'auto_debit'])->nullable();
            $table->string('payment_reference')->nullable();
            $table->string('trx', 40)->unique();
            $table->timestamps();
            
            $table->foreign('loan_id')->references('id')->on('user_loans')->onDelete('cascade');
            $table->foreign('user_id')->references('id')->on('users')->onDelete('cascade');
            $table->index(['loan_id', 'status']);
            $table->index(['user_id', 'due_date']);
        });

        // Loan Documents Table
        Schema::create('loan_documents', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('loan_id');
            $table->unsignedBigInteger('user_id');
            $table->string('document_type'); // ID, salary_slip, bank_statement, etc.
            $table->string('file_name');
            $table->string('file_path');
            $table->string('file_size');
            $table->string('mime_type');
            $table->enum('verification_status', ['pending', 'verified', 'rejected'])->default('pending');
            $table->text('verification_notes')->nullable();
            $table->unsignedBigInteger('verified_by')->nullable();
            $table->timestamp('verified_at')->nullable();
            $table->timestamps();
            
            $table->foreign('loan_id')->references('id')->on('user_loans')->onDelete('cascade');
            $table->foreign('user_id')->references('id')->on('users')->onDelete('cascade');
            $table->foreign('verified_by')->references('id')->on('admins')->onDelete('set null');
            $table->index(['loan_id', 'document_type']);
        });

        // Loan Eligibility Checks Table
        Schema::create('loan_eligibility_checks', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('user_id');
            $table->unsignedBigInteger('product_id');
            $table->decimal('requested_amount', 28, 8);
            $table->integer('requested_duration');
            $table->integer('credit_score');
            $table->boolean('is_eligible');
            $table->decimal('max_eligible_amount', 28, 8)->nullable();
            $table->decimal('recommended_rate', 5, 2)->nullable();
            $table->json('eligibility_factors')->nullable();
            $table->text('rejection_reasons')->nullable();
            $table->timestamps();
            
            $table->foreign('user_id')->references('id')->on('users')->onDelete('cascade');
            $table->foreign('product_id')->references('id')->on('loan_products')->onDelete('cascade');
            $table->index(['user_id', 'is_eligible']);
        });

        // Loan Auto-Debit Settings Table
        Schema::create('loan_auto_debit_settings', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('loan_id');
            $table->unsignedBigInteger('user_id');
            $table->boolean('is_enabled')->default(false);
            $table->enum('debit_source', ['wallet', 'card', 'bank_account'])->default('wallet');
            $table->string('source_id')->nullable(); // Card ID or Bank Account ID
            $table->integer('days_before_due')->default(1); // Auto-debit X days before due
            $table->boolean('partial_payment_allowed')->default(true);
            $table->timestamps();
            
            $table->foreign('loan_id')->references('id')->on('user_loans')->onDelete('cascade');
            $table->foreign('user_id')->references('id')->on('users')->onDelete('cascade');
            $table->unique('loan_id');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('loan_auto_debit_settings');
        Schema::dropIfExists('loan_eligibility_checks');
        Schema::dropIfExists('loan_documents');
        Schema::dropIfExists('loan_repayments');
        Schema::dropIfExists('user_loans');
        Schema::dropIfExists('user_credit_scores');
        Schema::dropIfExists('loan_products');
    }
};
