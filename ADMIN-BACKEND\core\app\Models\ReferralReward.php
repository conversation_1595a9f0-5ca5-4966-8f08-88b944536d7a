<?php

namespace App\Models;

use App\Traits\Searchable;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Carbon\Carbon;

class ReferralReward extends Model
{
    use Searchable;

    protected $fillable = [
        'user_id',
        'referral_id',
        'type',
        'amount',
        'status',
        'paid_date',
        'transaction_reference'
    ];

    protected $casts = [
        'amount' => 'decimal:8',
        'paid_date' => 'date'
    ];

    protected $appends = [
        'is_paid',
        'days_pending',
        'formatted_amount'
    ];

    // Relationships
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    public function referral(): BelongsTo
    {
        return $this->belongsTo(Referral::class);
    }

    // Accessors
    public function getIsPaidAttribute(): bool
    {
        return $this->status === 'paid';
    }

    public function getDaysPendingAttribute(): int
    {
        if ($this->is_paid) {
            return 0;
        }
        
        return $this->created_at->diffInDays(now());
    }

    public function getFormattedAmountAttribute(): string
    {
        return '₦' . number_format($this->amount, 2);
    }

    // Scopes
    public function scopePending($query)
    {
        return $query->where('status', 'pending');
    }

    public function scopePaid($query)
    {
        return $query->where('status', 'paid');
    }

    public function scopeCancelled($query)
    {
        return $query->where('status', 'cancelled');
    }

    public function scopeForUser($query, $userId)
    {
        return $query->where('user_id', $userId);
    }

    public function scopeReferrerRewards($query)
    {
        return $query->where('type', 'referrer_bonus');
    }

    public function scopeRefereeRewards($query)
    {
        return $query->where('type', 'referee_bonus');
    }

    // Methods
    public function pay(): array
    {
        if ($this->status !== 'pending') {
            return [
                'success' => false,
                'message' => 'Reward is not in pending status'
            ];
        }

        try {
            // Update user balance
            $user = $this->user;
            $user->balance += $this->amount;
            $user->save();

            // Create transaction record
            $transaction = \App\Models\Transaction::create([
                'user_id' => $this->user_id,
                'amount' => $this->amount,
                'post_balance' => $user->balance,
                'charge' => 0,
                'trx_type' => '+',
                'details' => 'Referral reward payment',
                'trx' => getTrx(),
                'remark' => 'referral_reward'
            ]);

            // Update reward status
            $this->status = 'paid';
            $this->paid_date = Carbon::now()->toDateString();
            $this->transaction_reference = $transaction->trx;
            $this->save();

            // Update referral status if both rewards are paid
            $this->updateReferralStatus();

            return [
                'success' => true,
                'message' => 'Reward paid successfully',
                'transaction_reference' => $this->transaction_reference
            ];

        } catch (\Exception $e) {
            return [
                'success' => false,
                'message' => 'Failed to pay reward: ' . $e->getMessage()
            ];
        }
    }

    public function cancel(): array
    {
        if ($this->status !== 'pending') {
            return [
                'success' => false,
                'message' => 'Reward is not in pending status'
            ];
        }

        $this->status = 'cancelled';
        $this->save();

        return [
            'success' => true,
            'message' => 'Reward cancelled successfully'
        ];
    }

    private function updateReferralStatus(): void
    {
        $referral = $this->referral;
        
        // Check if all rewards for this referral are paid
        $allRewardsPaid = $referral->rewards()
                                 ->where('status', '!=', 'paid')
                                 ->count() === 0;

        if ($allRewardsPaid) {
            $referral->markAsRewarded();
        }
    }

    // Static methods
    public static function processPendingRewards(): int
    {
        $pendingRewards = self::pending()
                             ->where('created_at', '<=', Carbon::now()->subDays(1))
                             ->get();

        $processed = 0;

        foreach ($pendingRewards as $reward) {
            $result = $reward->pay();
            if ($result['success']) {
                $processed++;
            }
        }

        return $processed;
    }

    public static function getRewardStats(): array
    {
        return [
            'total_rewards' => self::count(),
            'pending_rewards' => self::where('status', 'pending')->count(),
            'paid_rewards' => self::where('status', 'paid')->count(),
            'cancelled_rewards' => self::where('status', 'cancelled')->count(),
            'total_amount_paid' => self::where('status', 'paid')->sum('amount'),
            'pending_amount' => self::where('status', 'pending')->sum('amount'),
            'average_reward_amount' => self::avg('amount'),
            'rewards_by_type' => [
                'referrer_bonuses' => self::where('type', 'referrer_bonus')->count(),
                'referee_bonuses' => self::where('type', 'referee_bonus')->count()
            ]
        ];
    }

    public static function getUserRewardSummary($userId): array
    {
        $rewards = self::where('user_id', $userId)->get();

        return [
            'total_rewards' => $rewards->count(),
            'total_earned' => $rewards->where('status', 'paid')->sum('amount'),
            'pending_amount' => $rewards->where('status', 'pending')->sum('amount'),
            'last_reward_date' => $rewards->where('status', 'paid')->max('paid_date'),
            'rewards_by_type' => [
                'referrer_bonuses' => $rewards->where('type', 'referrer_bonus')->sum('amount'),
                'referee_bonuses' => $rewards->where('type', 'referee_bonus')->sum('amount')
            ]
        ];
    }

    public static function getMonthlyRewardTrend($months = 12): array
    {
        $trend = [];
        
        for ($i = $months - 1; $i >= 0; $i--) {
            $date = Carbon::now()->subMonths($i);
            
            $monthlyData = self::whereMonth('created_at', $date->month)
                              ->whereYear('created_at', $date->year)
                              ->selectRaw('
                                  COUNT(*) as count,
                                  SUM(CASE WHEN status = "paid" THEN amount ELSE 0 END) as paid_amount,
                                  SUM(CASE WHEN status = "pending" THEN amount ELSE 0 END) as pending_amount
                              ')
                              ->first();

            $trend[] = [
                'month' => $date->format('M Y'),
                'count' => $monthlyData->count ?? 0,
                'paid_amount' => $monthlyData->paid_amount ?? 0,
                'pending_amount' => $monthlyData->pending_amount ?? 0
            ];
        }

        return $trend;
    }

    public static function getTopEarners($limit = 10): array
    {
        return self::selectRaw('user_id, SUM(amount) as total_earned')
                  ->where('status', 'paid')
                  ->groupBy('user_id')
                  ->orderByDesc('total_earned')
                  ->limit($limit)
                  ->with('user:id,firstname,lastname,image')
                  ->get()
                  ->map(function ($item) {
                      return [
                          'user' => $item->user,
                          'total_earned' => $item->total_earned,
                          'formatted_amount' => '₦' . number_format($item->total_earned, 2)
                      ];
                  })
                  ->toArray();
    }
}
