class ReferralResponseModel {
  String? status;
  Message? message;
  ReferralData? data;

  ReferralResponseModel({
    this.status,
    this.message,
    this.data,
  });

  factory ReferralResponseModel.fromJson(Map<String, dynamic> json) => ReferralResponseModel(
        status: json["status"],
        message: json["message"] == null ? null : Message.fromJson(json["message"]),
        data: json["data"] == null ? null : ReferralData.fromJson(json["data"]),
      );

  Map<String, dynamic> toJson() => {
        "status": status,
        "message": message?.toJson(),
        "data": data?.toJson(),
      };
}

class Message {
  List<String>? success;
  List<String>? error;

  Message({
    this.success,
    this.error,
  });

  factory Message.fromJson(Map<String, dynamic> json) => Message(
        success: json["success"] == null ? [] : List<String>.from(json["success"]!.map((x) => x)),
        error: json["error"] == null ? [] : List<String>.from(json["error"]!.map((x) => x)),
      );

  Map<String, dynamic> toJson() => {
        "success": success == null ? [] : List<dynamic>.from(success!.map((x) => x)),
        "error": error == null ? [] : List<dynamic>.from(error!.map((x) => x)),
      };
}

class ReferralData {
  ReferralProfile? profile;
  List<Referral>? referrals;
  List<ReferralReward>? rewards;
  ReferralStats? stats;
  List<ReferralTier>? tiers;
  ReferralLeaderboard? leaderboard;

  ReferralData({
    this.profile,
    this.referrals,
    this.rewards,
    this.stats,
    this.tiers,
    this.leaderboard,
  });

  factory ReferralData.fromJson(Map<String, dynamic> json) => ReferralData(
        profile: json["profile"] == null ? null : ReferralProfile.fromJson(json["profile"]),
        referrals: json["referrals"] == null ? [] : List<Referral>.from(json["referrals"]!.map((x) => Referral.fromJson(x))),
        rewards: json["rewards"] == null ? [] : List<ReferralReward>.from(json["rewards"]!.map((x) => ReferralReward.fromJson(x))),
        stats: json["stats"] == null ? null : ReferralStats.fromJson(json["stats"]),
        tiers: json["tiers"] == null ? [] : List<ReferralTier>.from(json["tiers"]!.map((x) => ReferralTier.fromJson(x))),
        leaderboard: json["leaderboard"] == null ? null : ReferralLeaderboard.fromJson(json["leaderboard"]),
      );

  Map<String, dynamic> toJson() => {
        "profile": profile?.toJson(),
        "referrals": referrals == null ? [] : List<dynamic>.from(referrals!.map((x) => x.toJson())),
        "rewards": rewards == null ? [] : List<dynamic>.from(rewards!.map((x) => x.toJson())),
        "stats": stats?.toJson(),
        "tiers": tiers == null ? [] : List<dynamic>.from(tiers!.map((x) => x.toJson())),
        "leaderboard": leaderboard?.toJson(),
      };
}

class ReferralProfile {
  int? userId;
  String? referralCode;
  String? referralLink;
  int? currentTier;
  String? tierName;
  int? totalReferrals;
  int? activeReferrals;
  String? totalEarnings;
  String? pendingEarnings;
  String? paidEarnings;
  int? nextTierRequirement;
  String? joinedAt;

  ReferralProfile({
    this.userId,
    this.referralCode,
    this.referralLink,
    this.currentTier,
    this.tierName,
    this.totalReferrals,
    this.activeReferrals,
    this.totalEarnings,
    this.pendingEarnings,
    this.paidEarnings,
    this.nextTierRequirement,
    this.joinedAt,
  });

  factory ReferralProfile.fromJson(Map<String, dynamic> json) => ReferralProfile(
        userId: json["user_id"],
        referralCode: json["referral_code"],
        referralLink: json["referral_link"],
        currentTier: json["current_tier"],
        tierName: json["tier_name"],
        totalReferrals: json["total_referrals"],
        activeReferrals: json["active_referrals"],
        totalEarnings: json["total_earnings"],
        pendingEarnings: json["pending_earnings"],
        paidEarnings: json["paid_earnings"],
        nextTierRequirement: json["next_tier_requirement"],
        joinedAt: json["joined_at"],
      );

  Map<String, dynamic> toJson() => {
        "user_id": userId,
        "referral_code": referralCode,
        "referral_link": referralLink,
        "current_tier": currentTier,
        "tier_name": tierName,
        "total_referrals": totalReferrals,
        "active_referrals": activeReferrals,
        "total_earnings": totalEarnings,
        "pending_earnings": pendingEarnings,
        "paid_earnings": paidEarnings,
        "next_tier_requirement": nextTierRequirement,
        "joined_at": joinedAt,
      };
}

class Referral {
  int? id;
  int? referrerId;
  int? refereeId;
  String? referralCode;
  String? status; // 'pending', 'active', 'completed', 'expired'
  String? refereeFirstName;
  String? refereeLastName;
  String? refereeEmail;
  String? refereePhone;
  String? referrerReward;
  String? refereeReward;
  String? qualificationDate;
  String? rewardPaidDate;
  String? createdAt;

  Referral({
    this.id,
    this.referrerId,
    this.refereeId,
    this.referralCode,
    this.status,
    this.refereeFirstName,
    this.refereeLastName,
    this.refereeEmail,
    this.refereePhone,
    this.referrerReward,
    this.refereeReward,
    this.qualificationDate,
    this.rewardPaidDate,
    this.createdAt,
  });

  factory Referral.fromJson(Map<String, dynamic> json) => Referral(
        id: json["id"],
        referrerId: json["referrer_id"],
        refereeId: json["referee_id"],
        referralCode: json["referral_code"],
        status: json["status"],
        refereeFirstName: json["referee_first_name"],
        refereeLastName: json["referee_last_name"],
        refereeEmail: json["referee_email"],
        refereePhone: json["referee_phone"],
        referrerReward: json["referrer_reward"],
        refereeReward: json["referee_reward"],
        qualificationDate: json["qualification_date"],
        rewardPaidDate: json["reward_paid_date"],
        createdAt: json["created_at"],
      );

  Map<String, dynamic> toJson() => {
        "id": id,
        "referrer_id": referrerId,
        "referee_id": refereeId,
        "referral_code": referralCode,
        "status": status,
        "referee_first_name": refereeFirstName,
        "referee_last_name": refereeLastName,
        "referee_email": refereeEmail,
        "referee_phone": refereePhone,
        "referrer_reward": referrerReward,
        "referee_reward": refereeReward,
        "qualification_date": qualificationDate,
        "reward_paid_date": rewardPaidDate,
        "created_at": createdAt,
      };
}

class ReferralReward {
  int? id;
  int? userId;
  int? referralId;
  String? type; // 'signup', 'first_transaction', 'milestone', 'tier_bonus'
  String? amount;
  String? currency;
  String? status; // 'pending', 'paid', 'cancelled'
  String? description;
  String? earnedAt;
  String? paidAt;

  ReferralReward({
    this.id,
    this.userId,
    this.referralId,
    this.type,
    this.amount,
    this.currency,
    this.status,
    this.description,
    this.earnedAt,
    this.paidAt,
  });

  factory ReferralReward.fromJson(Map<String, dynamic> json) => ReferralReward(
        id: json["id"],
        userId: json["user_id"],
        referralId: json["referral_id"],
        type: json["type"],
        amount: json["amount"],
        currency: json["currency"],
        status: json["status"],
        description: json["description"],
        earnedAt: json["earned_at"],
        paidAt: json["paid_at"],
      );

  Map<String, dynamic> toJson() => {
        "id": id,
        "user_id": userId,
        "referral_id": referralId,
        "type": type,
        "amount": amount,
        "currency": currency,
        "status": status,
        "description": description,
        "earned_at": earnedAt,
        "paid_at": paidAt,
      };
}

class ReferralStats {
  String? thisMonth;
  String? lastMonth;
  String? thisYear;
  String? allTime;
  List<MonthlyStats>? monthlyBreakdown;
  List<TierProgress>? tierProgress;

  ReferralStats({
    this.thisMonth,
    this.lastMonth,
    this.thisYear,
    this.allTime,
    this.monthlyBreakdown,
    this.tierProgress,
  });

  factory ReferralStats.fromJson(Map<String, dynamic> json) => ReferralStats(
        thisMonth: json["this_month"],
        lastMonth: json["last_month"],
        thisYear: json["this_year"],
        allTime: json["all_time"],
        monthlyBreakdown: json["monthly_breakdown"] == null ? [] : List<MonthlyStats>.from(json["monthly_breakdown"]!.map((x) => MonthlyStats.fromJson(x))),
        tierProgress: json["tier_progress"] == null ? [] : List<TierProgress>.from(json["tier_progress"]!.map((x) => TierProgress.fromJson(x))),
      );

  Map<String, dynamic> toJson() => {
        "this_month": thisMonth,
        "last_month": lastMonth,
        "this_year": thisYear,
        "all_time": allTime,
        "monthly_breakdown": monthlyBreakdown == null ? [] : List<dynamic>.from(monthlyBreakdown!.map((x) => x.toJson())),
        "tier_progress": tierProgress == null ? [] : List<dynamic>.from(tierProgress!.map((x) => x.toJson())),
      };
}

class MonthlyStats {
  String? month;
  int? referrals;
  String? earnings;

  MonthlyStats({
    this.month,
    this.referrals,
    this.earnings,
  });

  factory MonthlyStats.fromJson(Map<String, dynamic> json) => MonthlyStats(
        month: json["month"],
        referrals: json["referrals"],
        earnings: json["earnings"],
      );

  Map<String, dynamic> toJson() => {
        "month": month,
        "referrals": referrals,
        "earnings": earnings,
      };
}

class TierProgress {
  int? tier;
  String? name;
  int? required;
  int? current;
  bool? achieved;

  TierProgress({
    this.tier,
    this.name,
    this.required,
    this.current,
    this.achieved,
  });

  factory TierProgress.fromJson(Map<String, dynamic> json) => TierProgress(
        tier: json["tier"],
        name: json["name"],
        required: json["required"],
        current: json["current"],
        achieved: json["achieved"] == 1,
      );

  Map<String, dynamic> toJson() => {
        "tier": tier,
        "name": name,
        "required": required,
        "current": current,
        "achieved": achieved == true ? 1 : 0,
      };
}

class ReferralTier {
  int? tier;
  String? name;
  String? description;
  int? requiredReferrals;
  String? signupBonus;
  String? transactionBonus;
  String? monthlyBonus;
  List<String>? benefits;
  String? badgeIcon;
  String? badgeColor;

  ReferralTier({
    this.tier,
    this.name,
    this.description,
    this.requiredReferrals,
    this.signupBonus,
    this.transactionBonus,
    this.monthlyBonus,
    this.benefits,
    this.badgeIcon,
    this.badgeColor,
  });

  factory ReferralTier.fromJson(Map<String, dynamic> json) => ReferralTier(
        tier: json["tier"],
        name: json["name"],
        description: json["description"],
        requiredReferrals: json["required_referrals"],
        signupBonus: json["signup_bonus"],
        transactionBonus: json["transaction_bonus"],
        monthlyBonus: json["monthly_bonus"],
        benefits: json["benefits"] == null ? [] : List<String>.from(json["benefits"]!.map((x) => x)),
        badgeIcon: json["badge_icon"],
        badgeColor: json["badge_color"],
      );

  Map<String, dynamic> toJson() => {
        "tier": tier,
        "name": name,
        "description": description,
        "required_referrals": requiredReferrals,
        "signup_bonus": signupBonus,
        "transaction_bonus": transactionBonus,
        "monthly_bonus": monthlyBonus,
        "benefits": benefits == null ? [] : List<dynamic>.from(benefits!.map((x) => x)),
        "badge_icon": badgeIcon,
        "badge_color": badgeColor,
      };
}

class ReferralLeaderboard {
  List<LeaderboardEntry>? topReferrers;
  LeaderboardEntry? userRank;

  ReferralLeaderboard({
    this.topReferrers,
    this.userRank,
  });

  factory ReferralLeaderboard.fromJson(Map<String, dynamic> json) => ReferralLeaderboard(
        topReferrers: json["top_referrers"] == null ? [] : List<LeaderboardEntry>.from(json["top_referrers"]!.map((x) => LeaderboardEntry.fromJson(x))),
        userRank: json["user_rank"] == null ? null : LeaderboardEntry.fromJson(json["user_rank"]),
      );

  Map<String, dynamic> toJson() => {
        "top_referrers": topReferrers == null ? [] : List<dynamic>.from(topReferrers!.map((x) => x.toJson())),
        "user_rank": userRank?.toJson(),
      };
}

class LeaderboardEntry {
  int? rank;
  int? userId;
  String? firstName;
  String? lastName;
  String? avatar;
  int? totalReferrals;
  String? totalEarnings;
  int? tier;
  String? tierName;

  LeaderboardEntry({
    this.rank,
    this.userId,
    this.firstName,
    this.lastName,
    this.avatar,
    this.totalReferrals,
    this.totalEarnings,
    this.tier,
    this.tierName,
  });

  factory LeaderboardEntry.fromJson(Map<String, dynamic> json) => LeaderboardEntry(
        rank: json["rank"],
        userId: json["user_id"],
        firstName: json["first_name"],
        lastName: json["last_name"],
        avatar: json["avatar"],
        totalReferrals: json["total_referrals"],
        totalEarnings: json["total_earnings"],
        tier: json["tier"],
        tierName: json["tier_name"],
      );

  Map<String, dynamic> toJson() => {
        "rank": rank,
        "user_id": userId,
        "first_name": firstName,
        "last_name": lastName,
        "avatar": avatar,
        "total_referrals": totalReferrals,
        "total_earnings": totalEarnings,
        "tier": tier,
        "tier_name": tierName,
      };
}

// Request models
class UseReferralCodeRequest {
  String referralCode;

  UseReferralCodeRequest({
    required this.referralCode,
  });

  Map<String, dynamic> toJson() => {
        "referral_code": referralCode,
      };
}

class WithdrawEarningsRequest {
  String amount;
  String withdrawalMethod;
  String? accountDetails;

  WithdrawEarningsRequest({
    required this.amount,
    required this.withdrawalMethod,
    this.accountDetails,
  });

  Map<String, dynamic> toJson() => {
        "amount": amount,
        "withdrawal_method": withdrawalMethod,
        if (accountDetails != null) "account_details": accountDetails,
      };
}
