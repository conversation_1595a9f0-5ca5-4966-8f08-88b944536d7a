import 'dart:convert';
import 'package:viserpay/core/utils/method.dart';
import 'package:viserpay/core/utils/url_container.dart';
import 'package:viserpay/data/model/ecommerce/cart_model.dart';
import 'package:viserpay/data/model/ecommerce/order_model.dart';
import 'package:viserpay/data/model/global/response_model/response_model.dart';
import 'package:viserpay/data/services/api_service.dart';

class EcommerceRepo {
  ApiClient apiClient;
  
  EcommerceRepo({required this.apiClient});

  // Product Management
  Future<ResponseModel> getProducts({
    int page = 1,
    String? search,
    int? categoryId,
    int? storeId,
    String? sortBy,
    String? sortOrder,
  }) async {
    String url = '${UrlContainer.baseUrl}${UrlContainer.productsEndPoint}?page=$page';
    if (search != null && search.isNotEmpty) url += '&search=$search';
    if (categoryId != null) url += '&category_id=$categoryId';
    if (storeId != null) url += '&store_id=$storeId';
    if (sortBy != null) url += '&sort_by=$sortBy';
    if (sortOrder != null) url += '&sort_order=$sortOrder';
    
    ResponseModel responseModel = await apiClient.request(url, Method.getMethod, null, passHeader: true);
    return responseModel;
  }

  Future<ResponseModel> getProductDetails(int productId) async {
    String url = '${UrlContainer.baseUrl}${UrlContainer.productsEndPoint}/$productId';
    ResponseModel responseModel = await apiClient.request(url, Method.getMethod, null, passHeader: true);
    return responseModel;
  }

  Future<ResponseModel> getFeaturedProducts() async {
    String url = '${UrlContainer.baseUrl}${UrlContainer.productsEndPoint}/featured';
    ResponseModel responseModel = await apiClient.request(url, Method.getMethod, null, passHeader: true);
    return responseModel;
  }

  Future<ResponseModel> getRecommendedProducts() async {
    String url = '${UrlContainer.baseUrl}${UrlContainer.productsEndPoint}/recommended';
    ResponseModel responseModel = await apiClient.request(url, Method.getMethod, null, passHeader: true);
    return responseModel;
  }

  // Category Management
  Future<ResponseModel> getCategories() async {
    String url = '${UrlContainer.baseUrl}${UrlContainer.categoriesEndPoint}';
    ResponseModel responseModel = await apiClient.request(url, Method.getMethod, null, passHeader: true);
    return responseModel;
  }

  Future<ResponseModel> getCategoryProducts(int categoryId, {int page = 1}) async {
    String url = '${UrlContainer.baseUrl}${UrlContainer.categoriesEndPoint}/$categoryId/products?page=$page';
    ResponseModel responseModel = await apiClient.request(url, Method.getMethod, null, passHeader: true);
    return responseModel;
  }

  // Store Management
  Future<ResponseModel> getStores({
    int page = 1,
    String? search,
    double? latitude,
    double? longitude,
    double? radius,
  }) async {
    String url = '${UrlContainer.baseUrl}${UrlContainer.storesEndPoint}?page=$page';
    if (search != null && search.isNotEmpty) url += '&search=$search';
    if (latitude != null) url += '&latitude=$latitude';
    if (longitude != null) url += '&longitude=$longitude';
    if (radius != null) url += '&radius=$radius';
    
    ResponseModel responseModel = await apiClient.request(url, Method.getMethod, null, passHeader: true);
    return responseModel;
  }

  Future<ResponseModel> getStoreDetails(int storeId) async {
    String url = '${UrlContainer.baseUrl}${UrlContainer.storesEndPoint}/$storeId';
    ResponseModel responseModel = await apiClient.request(url, Method.getMethod, null, passHeader: true);
    return responseModel;
  }

  Future<ResponseModel> getStoreProducts(int storeId, {int page = 1}) async {
    String url = '${UrlContainer.baseUrl}${UrlContainer.storesEndPoint}/$storeId/products?page=$page';
    ResponseModel responseModel = await apiClient.request(url, Method.getMethod, null, passHeader: true);
    return responseModel;
  }

  Future<ResponseModel> getNearbyStores(double latitude, double longitude, {double radius = 10.0}) async {
    String url = '${UrlContainer.baseUrl}${UrlContainer.storesEndPoint}/nearby?latitude=$latitude&longitude=$longitude&radius=$radius';
    ResponseModel responseModel = await apiClient.request(url, Method.getMethod, null, passHeader: true);
    return responseModel;
  }

  // Cart Management
  Future<ResponseModel> getCart() async {
    String url = '${UrlContainer.baseUrl}${UrlContainer.cartEndPoint}';
    ResponseModel responseModel = await apiClient.request(url, Method.getMethod, null, passHeader: true);
    return responseModel;
  }

  Future<ResponseModel> addToCart(AddToCartRequest request) async {
    String url = '${UrlContainer.baseUrl}${UrlContainer.addToCartEndPoint}';
    Map<String, String> params = {
      'product_id': request.productId.toString(),
      'quantity': request.quantity.toString(),
      if (request.variantId != null) 'variant_id': request.variantId.toString(),
    };
    ResponseModel responseModel = await apiClient.request(url, Method.postMethod, params, passHeader: true);
    return responseModel;
  }

  Future<ResponseModel> updateCartItem(UpdateCartItemRequest request) async {
    String url = '${UrlContainer.baseUrl}${UrlContainer.updateCartEndPoint}';
    Map<String, String> params = {
      'cart_item_id': request.cartItemId.toString(),
      'quantity': request.quantity.toString(),
    };
    ResponseModel responseModel = await apiClient.request(url, Method.postMethod, params, passHeader: true);
    return responseModel;
  }

  Future<ResponseModel> removeFromCart(int cartItemId) async {
    String url = '${UrlContainer.baseUrl}${UrlContainer.removeFromCartEndPoint}';
    Map<String, String> params = {
      'cart_item_id': cartItemId.toString(),
    };
    ResponseModel responseModel = await apiClient.request(url, Method.postMethod, params, passHeader: true);
    return responseModel;
  }

  Future<ResponseModel> clearCart() async {
    String url = '${UrlContainer.baseUrl}${UrlContainer.clearCartEndPoint}';
    ResponseModel responseModel = await apiClient.request(url, Method.postMethod, {}, passHeader: true);
    return responseModel;
  }

  Future<ResponseModel> applyCoupon(ApplyCouponRequest request) async {
    String url = '${UrlContainer.baseUrl}${UrlContainer.applyCouponEndPoint}';
    Map<String, String> params = {
      'coupon_code': request.couponCode,
    };
    ResponseModel responseModel = await apiClient.request(url, Method.postMethod, params, passHeader: true);
    return responseModel;
  }

  Future<ResponseModel> removeCoupon(String couponCode) async {
    String url = '${UrlContainer.baseUrl}${UrlContainer.removeCouponEndPoint}';
    Map<String, String> params = {
      'coupon_code': couponCode,
    };
    ResponseModel responseModel = await apiClient.request(url, Method.postMethod, params, passHeader: true);
    return responseModel;
  }

  // Order Management
  Future<ResponseModel> createOrder(CreateOrderRequest request) async {
    String url = '${UrlContainer.baseUrl}${UrlContainer.createOrderEndPoint}';
    Map<String, dynamic> params = request.toJson();
    ResponseModel responseModel = await apiClient.request(url, Method.postMethod, params, passHeader: true);
    return responseModel;
  }

  Future<ResponseModel> getOrders({int page = 1, String? status}) async {
    String url = '${UrlContainer.baseUrl}${UrlContainer.ordersEndPoint}?page=$page';
    if (status != null && status.isNotEmpty) url += '&status=$status';
    ResponseModel responseModel = await apiClient.request(url, Method.getMethod, null, passHeader: true);
    return responseModel;
  }

  Future<ResponseModel> getOrderDetails(int orderId) async {
    String url = '${UrlContainer.baseUrl}${UrlContainer.orderDetailsEndPoint}/$orderId';
    ResponseModel responseModel = await apiClient.request(url, Method.getMethod, null, passHeader: true);
    return responseModel;
  }

  Future<ResponseModel> cancelOrder(int orderId, String reason) async {
    String url = '${UrlContainer.baseUrl}${UrlContainer.cancelOrderEndPoint}';
    Map<String, String> params = {
      'order_id': orderId.toString(),
      'reason': reason,
    };
    ResponseModel responseModel = await apiClient.request(url, Method.postMethod, params, passHeader: true);
    return responseModel;
  }

  Future<ResponseModel> trackOrder(String orderNumber) async {
    String url = '${UrlContainer.baseUrl}${UrlContainer.trackOrderEndPoint}/$orderNumber';
    ResponseModel responseModel = await apiClient.request(url, Method.getMethod, null, passHeader: true);
    return responseModel;
  }

  Future<ResponseModel> getOrderHistory({int page = 1}) async {
    String url = '${UrlContainer.baseUrl}${UrlContainer.orderHistoryEndPoint}?page=$page';
    ResponseModel responseModel = await apiClient.request(url, Method.getMethod, null, passHeader: true);
    return responseModel;
  }

  // Search and Filters
  Future<ResponseModel> searchProducts(String query, {
    int page = 1,
    int? categoryId,
    String? minPrice,
    String? maxPrice,
    String? sortBy,
    String? sortOrder,
  }) async {
    String url = '${UrlContainer.baseUrl}${UrlContainer.productsEndPoint}/search?q=$query&page=$page';
    if (categoryId != null) url += '&category_id=$categoryId';
    if (minPrice != null) url += '&min_price=$minPrice';
    if (maxPrice != null) url += '&max_price=$maxPrice';
    if (sortBy != null) url += '&sort_by=$sortBy';
    if (sortOrder != null) url += '&sort_order=$sortOrder';
    
    ResponseModel responseModel = await apiClient.request(url, Method.getMethod, null, passHeader: true);
    return responseModel;
  }

  Future<ResponseModel> getProductReviews(int productId, {int page = 1}) async {
    String url = '${UrlContainer.baseUrl}${UrlContainer.productsEndPoint}/$productId/reviews?page=$page';
    ResponseModel responseModel = await apiClient.request(url, Method.getMethod, null, passHeader: true);
    return responseModel;
  }

  Future<ResponseModel> addProductReview(int productId, int rating, String review) async {
    String url = '${UrlContainer.baseUrl}${UrlContainer.productsEndPoint}/$productId/reviews';
    Map<String, String> params = {
      'rating': rating.toString(),
      'review': review,
    };
    ResponseModel responseModel = await apiClient.request(url, Method.postMethod, params, passHeader: true);
    return responseModel;
  }

  // Wishlist Management
  Future<ResponseModel> getWishlist() async {
    String url = '${UrlContainer.baseUrl}wishlist';
    ResponseModel responseModel = await apiClient.request(url, Method.getMethod, null, passHeader: true);
    return responseModel;
  }

  Future<ResponseModel> addToWishlist(int productId) async {
    String url = '${UrlContainer.baseUrl}wishlist/add';
    Map<String, String> params = {
      'product_id': productId.toString(),
    };
    ResponseModel responseModel = await apiClient.request(url, Method.postMethod, params, passHeader: true);
    return responseModel;
  }

  Future<ResponseModel> removeFromWishlist(int productId) async {
    String url = '${UrlContainer.baseUrl}wishlist/remove';
    Map<String, String> params = {
      'product_id': productId.toString(),
    };
    ResponseModel responseModel = await apiClient.request(url, Method.postMethod, params, passHeader: true);
    return responseModel;
  }
}
