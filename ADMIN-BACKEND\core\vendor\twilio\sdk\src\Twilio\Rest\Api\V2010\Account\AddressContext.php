<?php

/**
 * This code was generated by
 * \ / _    _  _|   _  _
 * | (_)\/(_)(_|\/| |(/_  v1.0.0
 * /       /
 */

namespace Twilio\Rest\Api\V2010\Account;

use Twilio\Exceptions\TwilioException;
use <PERSON><PERSON>lio\InstanceContext;
use <PERSON>wi<PERSON>\ListResource;
use Twi<PERSON>\Options;
use Twilio\Rest\Api\V2010\Account\Address\DependentPhoneNumberList;
use Twilio\Serialize;
use Twilio\Values;
use Twilio\Version;

/**
 * @property DependentPhoneNumberList $dependentPhoneNumbers
 */
class AddressContext extends InstanceContext {
    protected $_dependentPhoneNumbers;

    /**
     * Initialize the AddressContext
     *
     * @param Version $version Version that contains the resource
     * @param string $accountSid The SID of the Account that is responsible for
     *                           this address
     * @param string $sid The unique string that identifies the resource
     */
    public function __construct(Version $version, $accountSid, $sid) {
        parent::__construct($version);

        // Path Solution
        $this->solution = ['accountSid' => $accountSid, 'sid' => $sid, ];

        $this->uri = '/Accounts/' . \rawurlencode($accountSid) . '/Addresses/' . \rawurlencode($sid) . '.json';
    }

    /**
     * Delete the AddressInstance
     *
     * @return bool True if delete succeeds, false otherwise
     * @throws TwilioException When an HTTP error occurs.
     */
    public function delete(): bool {
        return $this->version->delete('DELETE', $this->uri);
    }

    /**
     * Fetch the AddressInstance
     *
     * @return AddressInstance Fetched AddressInstance
     * @throws TwilioException When an HTTP error occurs.
     */
    public function fetch(): AddressInstance {
        $payload = $this->version->fetch('GET', $this->uri);

        return new AddressInstance(
            $this->version,
            $payload,
            $this->solution['accountSid'],
            $this->solution['sid']
        );
    }

    /**
     * Update the AddressInstance
     *
     * @param array|Options $options Optional Arguments
     * @return AddressInstance Updated AddressInstance
     * @throws TwilioException When an HTTP error occurs.
     */
    public function update(array $options = []): AddressInstance {
        $options = new Values($options);

        $data = Values::of([
            'FriendlyName' => $options['friendlyName'],
            'CustomerName' => $options['customerName'],
            'Street' => $options['street'],
            'City' => $options['city'],
            'Region' => $options['region'],
            'PostalCode' => $options['postalCode'],
            'EmergencyEnabled' => Serialize::booleanToString($options['emergencyEnabled']),
            'AutoCorrectAddress' => Serialize::booleanToString($options['autoCorrectAddress']),
            'StreetSecondary' => $options['streetSecondary'],
        ]);

        $payload = $this->version->update('POST', $this->uri, [], $data);

        return new AddressInstance(
            $this->version,
            $payload,
            $this->solution['accountSid'],
            $this->solution['sid']
        );
    }

    /**
     * Access the dependentPhoneNumbers
     */
    protected function getDependentPhoneNumbers(): DependentPhoneNumberList {
        if (!$this->_dependentPhoneNumbers) {
            $this->_dependentPhoneNumbers = new DependentPhoneNumberList(
                $this->version,
                $this->solution['accountSid'],
                $this->solution['sid']
            );
        }

        return $this->_dependentPhoneNumbers;
    }

    /**
     * Magic getter to lazy load subresources
     *
     * @param string $name Subresource to return
     * @return ListResource The requested subresource
     * @throws TwilioException For unknown subresources
     */
    public function __get(string $name): ListResource {
        if (\property_exists($this, '_' . $name)) {
            $method = 'get' . \ucfirst($name);
            return $this->$method();
        }

        throw new TwilioException('Unknown subresource ' . $name);
    }

    /**
     * Magic caller to get resource contexts
     *
     * @param string $name Resource to return
     * @param array $arguments Context parameters
     * @return InstanceContext The requested resource context
     * @throws TwilioException For unknown resource
     */
    public function __call(string $name, array $arguments): InstanceContext {
        $property = $this->$name;
        if (\method_exists($property, 'getContext')) {
            return \call_user_func_array(array($property, 'getContext'), $arguments);
        }

        throw new TwilioException('Resource does not have a context');
    }

    /**
     * Provide a friendly representation
     *
     * @return string Machine friendly representation
     */
    public function __toString(): string {
        $context = [];
        foreach ($this->solution as $key => $value) {
            $context[] = "$key=$value";
        }
        return '[Twilio.Api.V2010.AddressContext ' . \implode(' ', $context) . ']';
    }
}