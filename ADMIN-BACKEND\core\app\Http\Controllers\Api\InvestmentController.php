<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\InvestmentProduct;
use App\Models\UserInvestment;
use App\Models\InvestmentTransaction;
use App\Models\InvestmentPortfolio;
use App\Models\AdminRate;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Validator;
use Carbon\Carbon;

class InvestmentController extends Controller
{
    public function __construct()
    {
        $this->middleware('auth:api');
    }

    /**
     * Get investment products
     */
    public function products(): JsonResponse
    {
        $user = Auth::user();
        $rates = AdminRate::getInvestmentRates();
        
        // Get products based on user's risk profile
        $products = InvestmentProduct::getProductsByRiskProfile($user->risk_profile ?? 'moderate');
        
        // Update rates from admin configuration
        foreach ($products as $product) {
            $minKey = $product->type . '_min';
            $maxKey = $product->type . '_max';
            
            if (isset($rates[$minKey])) {
                $product->interest_rate_min = $rates[$minKey];
            }
            if (isset($rates[$maxKey])) {
                $product->interest_rate_max = $rates[$maxKey];
            }
        }

        return response()->json([
            'status' => 'success',
            'data' => [
                'products' => $products,
                'user_risk_profile' => $user->risk_profile ?? 'moderate',
                'recommended_allocation' => $this->getRecommendedAllocation($user->risk_profile ?? 'moderate')
            ]
        ]);
    }

    /**
     * Get user's investment portfolio
     */
    public function portfolio(): JsonResponse
    {
        $user = Auth::user();
        
        $investments = UserInvestment::forUser($user->id)
                                   ->with(['product', 'transactions'])
                                   ->get();

        // Calculate portfolio metrics
        $totalInvested = $investments->sum('amount');
        $currentValue = $investments->sum('current_value');
        $totalReturns = $investments->sum('total_returns');
        $unrealizedGains = $investments->sum('unrealized_gains');
        $realizedGains = $investments->sum('realized_gains');

        $returnPercentage = $totalInvested > 0 ? (($currentValue - $totalInvested) / $totalInvested) * 100 : 0;

        // Asset allocation
        $allocation = [];
        foreach ($investments->groupBy('product.type') as $type => $typeInvestments) {
            $typeValue = $typeInvestments->sum('current_value');
            $allocation[$type] = [
                'value' => $typeValue,
                'percentage' => $currentValue > 0 ? ($typeValue / $currentValue) * 100 : 0,
                'count' => $typeInvestments->count()
            ];
        }

        return response()->json([
            'status' => 'success',
            'data' => [
                'portfolio_summary' => [
                    'total_invested' => $totalInvested,
                    'current_value' => $currentValue,
                    'total_returns' => $totalReturns,
                    'unrealized_gains' => $unrealizedGains,
                    'realized_gains' => $realizedGains,
                    'return_percentage' => round($returnPercentage, 2),
                    'active_investments' => $investments->where('status', 'active')->count()
                ],
                'asset_allocation' => $allocation,
                'investments' => $investments,
                'recent_transactions' => $this->getRecentInvestmentTransactions($user->id)
            ]
        ]);
    }

    /**
     * Buy investment
     */
    public function buyInvestment(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'product_id' => 'required|exists:investment_products,id',
            'amount' => 'required|numeric|min:1'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'status' => 'error',
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], 422);
        }

        $user = Auth::user();
        $productId = $request->product_id;
        $amount = $request->amount;

        $result = UserInvestment::createInvestment($user->id, $productId, $amount);

        if (!$result['success']) {
            return response()->json([
                'status' => 'error',
                'message' => $result['message']
            ], 400);
        }

        // Update portfolio summary
        $this->updatePortfolioSummary($user->id);

        return response()->json([
            'status' => 'success',
            'message' => 'Investment purchased successfully',
            'data' => [
                'investment' => $result['investment'],
                'units_purchased' => $result['units_purchased'],
                'unit_price' => $result['unit_price']
            ]
        ]);
    }

    /**
     * Sell/liquidate investment
     */
    public function sellInvestment(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'investment_id' => 'required|exists:user_investments,id',
            'units' => 'nullable|numeric|min:0',
            'reason' => 'nullable|string|max:255'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'status' => 'error',
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], 422);
        }

        $user = Auth::user();
        $investment = UserInvestment::where('id', $request->investment_id)
                                   ->where('user_id', $user->id)
                                   ->first();

        if (!$investment) {
            return response()->json([
                'status' => 'error',
                'message' => 'Investment not found'
            ], 404);
        }

        $result = $investment->liquidate(null, $request->reason ?? 'User liquidation');

        if (!$result['success']) {
            return response()->json([
                'status' => 'error',
                'message' => $result['message']
            ], 400);
        }

        // Update portfolio summary
        $this->updatePortfolioSummary($user->id);

        return response()->json([
            'status' => 'success',
            'message' => 'Investment liquidated successfully',
            'data' => $result
        ]);
    }

    /**
     * Get investment performance
     */
    public function performance(Request $request): JsonResponse
    {
        $user = Auth::user();
        $period = $request->get('period', 30); // days
        
        $investments = UserInvestment::forUser($user->id)->with('product')->get();
        
        $performance = [];
        foreach ($investments as $investment) {
            $performanceData = $investment->getPerformanceHistory($period);
            $riskMetrics = $investment->getRiskMetrics();
            
            $performance[] = [
                'investment' => $investment,
                'performance_history' => $performanceData,
                'risk_metrics' => $riskMetrics,
                'current_return' => $investment->return_percentage,
                'annualized_return' => $investment->annualized_return
            ];
        }

        // Get overall portfolio performance
        $portfolioAnalytics = InvestmentTransaction::getInvestmentAnalytics($user->id, $period);
        $monthlyTrend = InvestmentTransaction::getMonthlyInvestmentTrend($user->id, 12);

        return response()->json([
            'status' => 'success',
            'data' => [
                'individual_performance' => $performance,
                'portfolio_analytics' => $portfolioAnalytics,
                'monthly_trend' => $monthlyTrend,
                'period_days' => $period
            ]
        ]);
    }

    /**
     * Get market data
     */
    public function marketData(): JsonResponse
    {
        // This would typically fetch real market data
        // For now, we'll return sample data
        $marketData = [
            'nse_all_share_index' => [
                'value' => 52341.20,
                'change' => 1089.45,
                'change_percentage' => 2.13,
                'last_updated' => Carbon::now()->toISOString()
            ],
            'top_performers' => [
                ['symbol' => 'DANGCEM', 'name' => 'Dangote Cement', 'price' => 285.50, 'change' => 5.2],
                ['symbol' => 'GTCO', 'name' => 'Guaranty Trust Bank', 'price' => 28.75, 'change' => 3.8],
                ['symbol' => 'MTNN', 'name' => 'MTN Nigeria', 'price' => 195.20, 'change' => 2.1],
                ['symbol' => 'BUACEMENT', 'name' => 'BUA Cement', 'price' => 78.90, 'change' => -1.2],
                ['symbol' => 'ZENITHBANK', 'name' => 'Zenith Bank', 'price' => 24.15, 'change' => 1.8]
            ],
            'market_summary' => [
                'total_volume' => *********,
                'total_value' => **********,
                'market_cap' => **************,
                'gainers' => 45,
                'losers' => 23,
                'unchanged' => 12
            ]
        ];

        return response()->json([
            'status' => 'success',
            'data' => $marketData
        ]);
    }

    /**
     * Get investment analytics
     */
    public function analytics(Request $request): JsonResponse
    {
        $user = Auth::user();
        $period = $request->get('period', 30);
        
        $analytics = InvestmentTransaction::getInvestmentAnalytics($user->id, $period);
        $productPerformance = InvestmentTransaction::getProductPerformance($user->id);
        
        return response()->json([
            'status' => 'success',
            'data' => [
                'period_analytics' => $analytics,
                'product_performance' => $productPerformance,
                'investment_recommendations' => $this->getInvestmentRecommendations($user)
            ]
        ]);
    }

    private function getRecommendedAllocation($riskProfile): array
    {
        return match($riskProfile) {
            'conservative' => [
                'treasury_bills' => 60,
                'corporate_bonds' => 30,
                'mutual_funds' => 10,
                'stocks' => 0,
                'real_estate' => 0
            ],
            'moderate' => [
                'treasury_bills' => 30,
                'corporate_bonds' => 25,
                'mutual_funds' => 25,
                'stocks' => 15,
                'real_estate' => 5
            ],
            'aggressive' => [
                'treasury_bills' => 10,
                'corporate_bonds' => 15,
                'mutual_funds' => 25,
                'stocks' => 35,
                'real_estate' => 15
            ],
            default => [
                'treasury_bills' => 30,
                'corporate_bonds' => 25,
                'mutual_funds' => 25,
                'stocks' => 15,
                'real_estate' => 5
            ]
        };
    }

    private function getRecentInvestmentTransactions($userId, $limit = 10)
    {
        return InvestmentTransaction::forUser($userId)
                                  ->with(['product', 'investment'])
                                  ->orderBy('created_at', 'desc')
                                  ->limit($limit)
                                  ->get();
    }

    private function updatePortfolioSummary($userId): void
    {
        $investments = UserInvestment::forUser($userId)->get();
        
        $totalInvested = $investments->sum('amount');
        $currentValue = $investments->sum('current_value');
        $totalReturns = $investments->sum('total_returns');
        $unrealizedGains = $investments->sum('unrealized_gains');
        $realizedGains = $investments->sum('realized_gains');
        $returnPercentage = $totalInvested > 0 ? (($currentValue - $totalInvested) / $totalInvested) * 100 : 0;

        InvestmentPortfolio::updateOrCreate(
            ['user_id' => $userId],
            [
                'total_invested' => $totalInvested,
                'current_value' => $currentValue,
                'total_returns' => $totalReturns,
                'unrealized_gains' => $unrealizedGains,
                'realized_gains' => $realizedGains,
                'return_percentage' => $returnPercentage,
                'active_investments' => $investments->where('status', 'active')->count(),
                'last_updated' => Carbon::now()->toDateString()
            ]
        );
    }

    private function getInvestmentRecommendations($user): array
    {
        // Simple recommendation logic based on portfolio
        $recommendations = [];
        
        $currentAllocation = $this->getCurrentAllocation($user->id);
        $recommendedAllocation = $this->getRecommendedAllocation($user->risk_profile ?? 'moderate');
        
        foreach ($recommendedAllocation as $type => $recommendedPercentage) {
            $currentPercentage = $currentAllocation[$type] ?? 0;
            
            if ($currentPercentage < $recommendedPercentage - 5) {
                $recommendations[] = [
                    'type' => 'increase',
                    'product_type' => $type,
                    'message' => "Consider increasing your {$type} allocation to {$recommendedPercentage}%",
                    'current_percentage' => $currentPercentage,
                    'recommended_percentage' => $recommendedPercentage
                ];
            } elseif ($currentPercentage > $recommendedPercentage + 5) {
                $recommendations[] = [
                    'type' => 'decrease',
                    'product_type' => $type,
                    'message' => "Consider reducing your {$type} allocation to {$recommendedPercentage}%",
                    'current_percentage' => $currentPercentage,
                    'recommended_percentage' => $recommendedPercentage
                ];
            }
        }
        
        return $recommendations;
    }

    private function getCurrentAllocation($userId): array
    {
        $investments = UserInvestment::forUser($userId)->with('product')->get();
        $totalValue = $investments->sum('current_value');
        
        if ($totalValue <= 0) return [];
        
        $allocation = [];
        foreach ($investments->groupBy('product.type') as $type => $typeInvestments) {
            $typeValue = $typeInvestments->sum('current_value');
            $allocation[$type] = ($typeValue / $totalValue) * 100;
        }
        
        return $allocation;
    }
}
