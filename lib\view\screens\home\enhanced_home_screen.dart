import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:viserpay/core/utils/dimensions.dart';
import 'package:viserpay/core/utils/my_color.dart';
import 'package:viserpay/core/utils/my_strings.dart';
import 'package:viserpay/core/utils/style.dart';
import 'package:viserpay/view/components/balance_card/enhanced_balance_card.dart';
import 'package:viserpay/view/components/services/services_grid.dart';
import 'package:viserpay/view/components/transaction/modern_transaction_list.dart';

class EnhancedHomeScreen extends StatefulWidget {
  const EnhancedHomeScreen({super.key});

  @override
  State<EnhancedHomeScreen> createState() => _EnhancedHomeScreenState();
}

class _EnhancedHomeScreenState extends State<EnhancedHomeScreen>
    with TickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;
  late Animation<Offset> _slideAnimation;

  bool isBalanceHidden = false;
  bool isBusinessAccount = false;
  String currentBalance = '125,450.75';
  String accountNumber = '**********';
  String accountName = 'John <PERSON>';

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 1200),
      vsync: this,
    );

    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: const Interval(0.0, 0.6, curve: Curves.easeOut),
    ));

    _slideAnimation = Tween<Offset>(
      begin: const Offset(0, 0.3),
      end: Offset.zero,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: const Interval(0.3, 1.0, curve: Curves.easeOut),
    ));

    _animationController.forward();
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: MyColor.screenBgColor,
      body: SafeArea(
        child: RefreshIndicator(
          onRefresh: _onRefresh,
          color: MyColor.primaryColor,
          backgroundColor: MyColor.colorWhite,
          child: CustomScrollView(
            physics: const AlwaysScrollableScrollPhysics(),
            slivers: [
              // Custom App Bar
              SliverAppBar(
                expandedHeight: 80,
                floating: false,
                pinned: true,
                backgroundColor: MyColor.screenBgColor,
                elevation: 0,
                flexibleSpace: FlexibleSpaceBar(
                  background: _buildAppBarContent(),
                ),
              ),

              // Balance Card
              SliverToBoxAdapter(
                child: FadeTransition(
                  opacity: _fadeAnimation,
                  child: SlideTransition(
                    position: _slideAnimation,
                    child: EnhancedBalanceCard(
                      balance: currentBalance,
                      accountNumber: accountNumber,
                      accountName: accountName,
                      isBalanceHidden: isBalanceHidden,
                      isBusinessAccount: isBusinessAccount,
                      onToggleBalance: () {
                        setState(() {
                          isBalanceHidden = !isBalanceHidden;
                        });
                      },
                      onAddMoney: () => Get.toNamed('/add-money'),
                      onSendMoney: () => Get.toNamed('/send-money'),
                      onWithdraw: () => Get.toNamed('/withdraw'),
                      onMoreOptions: () => _showMoreOptions(),
                    ),
                  ),
                ),
              ),

              // Quick Stats
              SliverToBoxAdapter(
                child: FadeTransition(
                  opacity: _fadeAnimation,
                  child: _buildQuickStats(),
                ),
              ),

              // Services Grid
              SliverToBoxAdapter(
                child: FadeTransition(
                  opacity: _fadeAnimation,
                  child: ServicesGrid(
                    isBusinessAccount: isBusinessAccount,
                  ),
                ),
              ),

              // Recent Transactions Header
              SliverToBoxAdapter(
                child: FadeTransition(
                  opacity: _fadeAnimation,
                  child: _buildTransactionsHeader(),
                ),
              ),

              // Recent Transactions List
              SliverToBoxAdapter(
                child: FadeTransition(
                  opacity: _fadeAnimation,
                  child: Container(
                    height: 400,
                    margin: const EdgeInsets.symmetric(horizontal: Dimensions.space15),
                    decoration: BoxDecoration(
                      color: MyColor.colorWhite,
                      borderRadius: BorderRadius.circular(Dimensions.mediumRadius),
                      boxShadow: [
                        BoxShadow(
                          color: MyColor.colorGrey.withOpacity(0.1),
                          blurRadius: 10,
                          offset: const Offset(0, 2),
                        ),
                      ],
                    ),
                    child: ModernTransactionList(
                      transactions: _getDummyTransactions(),
                      onTransactionTap: (transaction) {
                        Get.toNamed('/transaction-details', arguments: transaction);
                      },
                      onRefresh: _onRefresh,
                    ),
                  ),
                ),
              ),

              // Bottom Spacing
              const SliverToBoxAdapter(
                child: SizedBox(height: Dimensions.space30),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildAppBarContent() {
    return Padding(
      padding: const EdgeInsets.symmetric(
        horizontal: Dimensions.space20,
        vertical: Dimensions.space15,
      ),
      child: Row(
        children: [
          // Profile Avatar
          GestureDetector(
            onTap: () => Get.toNamed('/profile'),
            child: Container(
              width: 45,
              height: 45,
              decoration: BoxDecoration(
                gradient: MyColor.primaryGradient,
                shape: BoxShape.circle,
                boxShadow: [
                  BoxShadow(
                    color: MyColor.primaryColor.withOpacity(0.3),
                    blurRadius: 10,
                    offset: const Offset(0, 3),
                  ),
                ],
              ),
              child: const Icon(
                Icons.person,
                color: MyColor.colorWhite,
                size: 24,
              ),
            ),
          ),

          const SizedBox(width: Dimensions.space15),

          // Greeting and Name
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Text(
                  _getGreeting(),
                  style: regularDefault.copyWith(
                    color: MyColor.colorGrey,
                    fontSize: 12,
                  ),
                ),
                const SizedBox(height: 2),
                Text(
                  accountName,
                  style: semiBoldLarge.copyWith(
                    color: MyColor.colorBlack,
                    fontSize: 16,
                  ),
                ),
              ],
            ),
          ),

          // Notification Bell
          GestureDetector(
            onTap: () => Get.toNamed('/notifications'),
            child: Container(
              width: 40,
              height: 40,
              decoration: BoxDecoration(
                color: MyColor.colorWhite,
                shape: BoxShape.circle,
                boxShadow: [
                  BoxShadow(
                    color: MyColor.colorGrey.withOpacity(0.1),
                    blurRadius: 10,
                    offset: const Offset(0, 2),
                  ),
                ],
              ),
              child: Stack(
                children: [
                  const Center(
                    child: Icon(
                      Icons.notifications_outlined,
                      color: MyColor.colorBlack,
                      size: 20,
                    ),
                  ),
                  // Notification Badge
                  Positioned(
                    top: 8,
                    right: 8,
                    child: Container(
                      width: 8,
                      height: 8,
                      decoration: const BoxDecoration(
                        color: MyColor.colorRed,
                        shape: BoxShape.circle,
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),

          const SizedBox(width: Dimensions.space10),

          // QR Code Scanner
          GestureDetector(
            onTap: () => Get.toNamed('/qr-scanner'),
            child: Container(
              width: 40,
              height: 40,
              decoration: BoxDecoration(
                color: MyColor.colorWhite,
                shape: BoxShape.circle,
                boxShadow: [
                  BoxShadow(
                    color: MyColor.colorGrey.withOpacity(0.1),
                    blurRadius: 10,
                    offset: const Offset(0, 2),
                  ),
                ],
              ),
              child: const Icon(
                Icons.qr_code_scanner,
                color: MyColor.colorBlack,
                size: 20,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildQuickStats() {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: Dimensions.space15),
      padding: const EdgeInsets.all(Dimensions.space15),
      decoration: BoxDecoration(
        color: MyColor.colorWhite,
        borderRadius: BorderRadius.circular(Dimensions.mediumRadius),
        boxShadow: [
          BoxShadow(
            color: MyColor.colorGrey.withOpacity(0.1),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Row(
        children: [
          Expanded(
            child: _buildStatItem(
              icon: Icons.trending_up,
              label: 'This Month',
              value: '₦45,200',
              color: MyColor.colorGreen,
            ),
          ),
          Container(
            width: 1,
            height: 40,
            color: MyColor.borderColor,
          ),
          Expanded(
            child: _buildStatItem(
              icon: Icons.savings,
              label: 'Savings',
              value: '₦125,000',
              color: MyColor.colorOrange,
            ),
          ),
          Container(
            width: 1,
            height: 40,
            color: MyColor.borderColor,
          ),
          Expanded(
            child: _buildStatItem(
              icon: Icons.card_giftcard,
              label: 'Rewards',
              value: '₦2,450',
              color: MyColor.colorCyan,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildStatItem({
    required IconData icon,
    required String label,
    required String value,
    required Color color,
  }) {
    return Column(
      children: [
        Icon(
          icon,
          color: color,
          size: 20,
        ),
        const SizedBox(height: Dimensions.space5),
        Text(
          value,
          style: semiBoldDefault.copyWith(
            color: MyColor.colorBlack,
            fontSize: 14,
          ),
        ),
        Text(
          label,
          style: regularSmall.copyWith(
            color: MyColor.colorGrey,
            fontSize: 10,
          ),
        ),
      ],
    );
  }

  Widget _buildTransactionsHeader() {
    return Padding(
      padding: const EdgeInsets.all(Dimensions.space20),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            'Recent Transactions',
            style: semiBoldLarge.copyWith(
              color: MyColor.colorBlack,
              fontSize: 18,
            ),
          ),
          GestureDetector(
            onTap: () => Get.toNamed('/transactions'),
            child: Text(
              'See All',
              style: semiBoldDefault.copyWith(
                color: MyColor.primaryColor,
                fontSize: 14,
              ),
            ),
          ),
        ],
      ),
    );
  }

  String _getGreeting() {
    final hour = DateTime.now().hour;
    if (hour < 12) {
      return 'Good Morning';
    } else if (hour < 17) {
      return 'Good Afternoon';
    } else {
      return 'Good Evening';
    }
  }

  void _showMoreOptions() {
    Get.bottomSheet(
      Container(
        padding: const EdgeInsets.all(Dimensions.space20),
        decoration: const BoxDecoration(
          color: MyColor.colorWhite,
          borderRadius: BorderRadius.only(
            topLeft: Radius.circular(Dimensions.cardRadius),
            topRight: Radius.circular(Dimensions.cardRadius),
          ),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
              'Account Options',
              style: semiBoldLarge.copyWith(
                color: MyColor.colorBlack,
              ),
            ),
            const SizedBox(height: Dimensions.space20),
            ListTile(
              leading: const Icon(Icons.account_balance, color: MyColor.primaryColor),
              title: const Text('Account Details'),
              onTap: () {
                Get.back();
                Get.toNamed('/account-details');
              },
            ),
            ListTile(
              leading: const Icon(Icons.receipt_long, color: MyColor.colorOrange),
              title: const Text('Account Statement'),
              onTap: () {
                Get.back();
                Get.toNamed('/account-statement');
              },
            ),
            ListTile(
              leading: const Icon(Icons.settings, color: MyColor.colorGrey),
              title: const Text('Account Settings'),
              onTap: () {
                Get.back();
                Get.toNamed('/account-settings');
              },
            ),
          ],
        ),
      ),
    );
  }

  Future<void> _onRefresh() async {
    // Simulate API call
    await Future.delayed(const Duration(seconds: 2));
    setState(() {
      // Refresh data
    });
  }

  List<dynamic> _getDummyTransactions() {
    // Return dummy transaction data for demo
    return [];
  }
}
