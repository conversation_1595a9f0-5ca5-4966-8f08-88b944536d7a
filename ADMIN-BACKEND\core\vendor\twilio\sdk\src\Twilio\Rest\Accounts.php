<?php

/**
 * This code was generated by
 * \ / _    _  _|   _  _
 * | (_)\/(_)(_|\/| |(/_  v1.0.0
 * /       /
 */

namespace Twilio\Rest;

use Twilio\Domain;
use Twi<PERSON>\Exceptions\TwilioException;
use T<PERSON><PERSON>\Rest\Accounts\V1;

/**
 * @property \Twilio\Rest\Accounts\V1 $v1
 * @property \Twilio\Rest\Accounts\V1\AuthTokenPromotionList $authTokenPromotion
 * @property \Twilio\Rest\Accounts\V1\CredentialList $credentials
 * @property \Twilio\Rest\Accounts\V1\SecondaryAuthTokenList $secondaryAuthToken
 * @method \Twilio\Rest\Accounts\V1\AuthTokenPromotionContext authTokenPromotion()
 * @method \Twilio\Rest\Accounts\V1\SecondaryAuthTokenContext secondaryAuthToken()
 */
class Accounts extends Domain {
    protected $_v1;

    /**
     * Construct the Accounts Domain
     *
     * @param Client $client Client to communicate with <PERSON><PERSON><PERSON>
     */
    public function __construct(Client $client) {
        parent::__construct($client);

        $this->baseUrl = 'https://accounts.twilio.com';
    }

    /**
     * @return V1 Version v1 of accounts
     */
    protected function getV1(): V1 {
        if (!$this->_v1) {
            $this->_v1 = new V1($this);
        }
        return $this->_v1;
    }

    /**
     * Magic getter to lazy load version
     *
     * @param string $name Version to return
     * @return \Twilio\Version The requested version
     * @throws TwilioException For unknown versions
     */
    public function __get(string $name) {
        $method = 'get' . \ucfirst($name);
        if (\method_exists($this, $method)) {
            return $this->$method();
        }

        throw new TwilioException('Unknown version ' . $name);
    }

    /**
     * Magic caller to get resource contexts
     *
     * @param string $name Resource to return
     * @param array $arguments Context parameters
     * @return \Twilio\InstanceContext The requested resource context
     * @throws TwilioException For unknown resource
     */
    public function __call(string $name, array $arguments) {
        $method = 'context' . \ucfirst($name);
        if (\method_exists($this, $method)) {
            return \call_user_func_array([$this, $method], $arguments);
        }

        throw new TwilioException('Unknown context ' . $name);
    }

    protected function getAuthTokenPromotion(): \Twilio\Rest\Accounts\V1\AuthTokenPromotionList {
        return $this->v1->authTokenPromotion;
    }

    protected function contextAuthTokenPromotion(): \Twilio\Rest\Accounts\V1\AuthTokenPromotionContext {
        return $this->v1->authTokenPromotion();
    }

    protected function getCredentials(): \Twilio\Rest\Accounts\V1\CredentialList {
        return $this->v1->credentials;
    }

    protected function getSecondaryAuthToken(): \Twilio\Rest\Accounts\V1\SecondaryAuthTokenList {
        return $this->v1->secondaryAuthToken;
    }

    protected function contextSecondaryAuthToken(): \Twilio\Rest\Accounts\V1\SecondaryAuthTokenContext {
        return $this->v1->secondaryAuthToken();
    }

    /**
     * Provide a friendly representation
     *
     * @return string Machine friendly representation
     */
    public function __toString(): string {
        return '[Twilio.Accounts]';
    }
}