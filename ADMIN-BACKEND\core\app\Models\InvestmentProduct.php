<?php

namespace App\Models;

use App\Traits\Searchable;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;

class InvestmentProduct extends Model
{
    use Searchable;

    protected $fillable = [
        'name',
        'type',
        'description',
        'minimum_amount',
        'maximum_amount',
        'interest_rate_min',
        'interest_rate_max',
        'risk_level',
        'duration_days',
        'management_fee',
        'is_active',
        'admin_configurable_rate',
        'features'
    ];

    protected $casts = [
        'minimum_amount' => 'decimal:8',
        'maximum_amount' => 'decimal:8',
        'interest_rate_min' => 'decimal:2',
        'interest_rate_max' => 'decimal:2',
        'management_fee' => 'decimal:2',
        'is_active' => 'boolean',
        'admin_configurable_rate' => 'boolean',
        'features' => 'array'
    ];

    protected $appends = [
        'average_return',
        'risk_badge_class',
        'type_display_name',
        'formatted_minimum_amount',
        'formatted_maximum_amount'
    ];

    // Relationships
    public function userInvestments(): HasMany
    {
        return $this->hasMany(UserInvestment::class, 'product_id');
    }

    public function investmentTransactions(): HasMany
    {
        return $this->hasMany(InvestmentTransaction::class, 'product_id');
    }

    public function performanceHistory(): HasMany
    {
        return $this->hasMany(InvestmentPerformanceHistory::class, 'product_id');
    }

    // Accessors
    public function getAverageReturnAttribute(): float
    {
        return ($this->interest_rate_min + $this->interest_rate_max) / 2;
    }

    public function getRiskBadgeClassAttribute(): string
    {
        return match($this->risk_level) {
            'low' => 'badge-success',
            'medium' => 'badge-warning',
            'high' => 'badge-danger',
            default => 'badge-secondary'
        };
    }

    public function getTypeDisplayNameAttribute(): string
    {
        return match($this->type) {
            'treasury_bills' => 'Treasury Bills',
            'mutual_funds' => 'Mutual Funds',
            'corporate_bonds' => 'Corporate Bonds',
            'stocks' => 'Nigerian Stocks',
            'real_estate' => 'Real Estate Funds',
            default => ucfirst(str_replace('_', ' ', $this->type))
        };
    }

    public function getFormattedMinimumAmountAttribute(): string
    {
        return '₦' . number_format($this->minimum_amount, 2);
    }

    public function getFormattedMaximumAmountAttribute(): string
    {
        return $this->maximum_amount ? '₦' . number_format($this->maximum_amount, 2) : 'No limit';
    }

    // Scopes
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    public function scopeByType($query, $type)
    {
        return $query->where('type', $type);
    }

    public function scopeByRiskLevel($query, $riskLevel)
    {
        return $query->where('risk_level', $riskLevel);
    }

    public function scopeLowRisk($query)
    {
        return $query->where('risk_level', 'low');
    }

    public function scopeMediumRisk($query)
    {
        return $query->where('risk_level', 'medium');
    }

    public function scopeHighRisk($query)
    {
        return $query->where('risk_level', 'high');
    }

    public function scopeConfigurableRate($query)
    {
        return $query->where('admin_configurable_rate', true);
    }

    // Methods
    public function getCurrentUnitPrice(): float
    {
        $latestPerformance = $this->performanceHistory()
                                 ->latest('date')
                                 ->first();
        
        return $latestPerformance ? $latestPerformance->unit_price : 1.0;
    }

    public function getPerformanceData($days = 30): array
    {
        $performance = $this->performanceHistory()
                           ->where('date', '>=', now()->subDays($days))
                           ->orderBy('date')
                           ->get();

        if ($performance->isEmpty()) {
            return [
                'performance' => [],
                'total_return' => 0,
                'volatility' => 0
            ];
        }

        $returns = [];
        for ($i = 1; $i < $performance->count(); $i++) {
            $previousPrice = $performance[$i - 1]->unit_price;
            $currentPrice = $performance[$i]->unit_price;
            $returns[] = (($currentPrice - $previousPrice) / $previousPrice) * 100;
        }

        $totalReturn = $performance->isEmpty() ? 0 : 
                      (($performance->last()->unit_price - $performance->first()->unit_price) / 
                       $performance->first()->unit_price) * 100;

        $volatility = empty($returns) ? 0 : $this->calculateStandardDeviation($returns);

        return [
            'performance' => $performance->toArray(),
            'total_return' => round($totalReturn, 2),
            'volatility' => round($volatility, 2),
            'daily_returns' => $returns
        ];
    }

    private function calculateStandardDeviation($values): float
    {
        if (empty($values)) return 0;
        
        $mean = array_sum($values) / count($values);
        $variance = array_sum(array_map(function($x) use ($mean) {
            return pow($x - $mean, 2);
        }, $values)) / count($values);
        
        return sqrt($variance);
    }

    public function getTotalInvestments(): float
    {
        return $this->userInvestments()
                   ->where('status', 'active')
                   ->sum('amount');
    }

    public function getInvestorCount(): int
    {
        return $this->userInvestments()
                   ->where('status', 'active')
                   ->distinct('user_id')
                   ->count();
    }

    public function canUserInvest($userId, $amount): array
    {
        $user = User::find($userId);
        
        if (!$user) {
            return ['can_invest' => false, 'reason' => 'User not found'];
        }

        if (!$this->is_active) {
            return ['can_invest' => false, 'reason' => 'Investment product is not active'];
        }

        if ($amount < $this->minimum_amount) {
            return [
                'can_invest' => false, 
                'reason' => "Minimum investment amount is {$this->formatted_minimum_amount}"
            ];
        }

        if ($this->maximum_amount && $amount > $this->maximum_amount) {
            return [
                'can_invest' => false, 
                'reason' => "Maximum investment amount is {$this->formatted_maximum_amount}"
            ];
        }

        if ($user->balance < $amount) {
            return ['can_invest' => false, 'reason' => 'Insufficient wallet balance'];
        }

        return ['can_invest' => true];
    }

    public static function getProductsByRiskProfile($riskProfile): \Illuminate\Database\Eloquent\Collection
    {
        $riskMapping = [
            'conservative' => ['low'],
            'moderate' => ['low', 'medium'],
            'aggressive' => ['low', 'medium', 'high']
        ];

        $allowedRiskLevels = $riskMapping[$riskProfile] ?? ['low'];

        return self::active()
                  ->whereIn('risk_level', $allowedRiskLevels)
                  ->orderBy('risk_level')
                  ->orderBy('interest_rate_max', 'desc')
                  ->get();
    }

    public static function getTopPerformers($limit = 5): \Illuminate\Database\Eloquent\Collection
    {
        return self::active()
                  ->withCount('userInvestments')
                  ->orderBy('interest_rate_max', 'desc')
                  ->orderBy('user_investments_count', 'desc')
                  ->limit($limit)
                  ->get();
    }
}
