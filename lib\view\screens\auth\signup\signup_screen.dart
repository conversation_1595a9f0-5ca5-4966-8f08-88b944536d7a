import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:viserpay/core/utils/dimensions.dart';
import 'package:viserpay/core/utils/my_color.dart';
import 'package:viserpay/core/utils/style.dart';
import 'package:viserpay/view/components/app-bar/custom_appbar.dart';
import 'package:viserpay/view/components/buttons/enhanced_button.dart';
import 'package:viserpay/view/screens/auth/signup/signup_controller.dart';
import 'package:viserpay/view/screens/auth/signup/steps/personal_info_step.dart';
import 'package:viserpay/view/screens/auth/signup/steps/security_step.dart';
import 'package:viserpay/view/screens/auth/signup/steps/verification_step.dart';
import 'package:viserpay/view/screens/auth/signup/steps/bvn_step.dart';
import 'package:viserpay/view/screens/auth/signup/steps/account_creation_step.dart';

class SignupScreen extends StatelessWidget {
  const SignupScreen({super.key});

  @override
  Widget build(BuildContext context) {
    final SignupController controller = Get.put(SignupController());

    return Scaffold(
      appBar: CustomAppBar(
        title: 'Create Account',
        isShowBackBtn: true,
        bgColor: MyColor.brandWhite,
      ),
      backgroundColor: MyColor.screenBgColor,
      body: GetBuilder<SignupController>(
        builder: (controller) => Column(
          children: [
            // Progress indicator
            _buildProgressIndicator(controller),
            
            // Step content
            Expanded(
              child: SingleChildScrollView(
                padding: const EdgeInsets.all(Dimensions.space20),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Step title and description
                    _buildStepHeader(controller),
                    
                    const SizedBox(height: Dimensions.space30),
                    
                    // Step content
                    _buildStepContent(controller),
                    
                    const SizedBox(height: Dimensions.space30),
                    
                    // Error message
                    Obx(() {
                      if (controller.errorMessage.value.isNotEmpty) {
                        return Container(
                          width: double.infinity,
                          padding: const EdgeInsets.all(Dimensions.space15),
                          margin: const EdgeInsets.only(bottom: Dimensions.space20),
                          decoration: BoxDecoration(
                            color: MyColor.colorRed.withOpacity(0.1),
                            borderRadius: BorderRadius.circular(Dimensions.mediumRadius),
                            border: Border.all(color: MyColor.colorRed.withOpacity(0.3)),
                          ),
                          child: Row(
                            children: [
                              Icon(
                                Icons.error_outline,
                                color: MyColor.colorRed,
                                size: 20,
                              ),
                              const SizedBox(width: Dimensions.space10),
                              Expanded(
                                child: Text(
                                  controller.errorMessage.value,
                                  style: regularDefault.copyWith(
                                    color: MyColor.colorRed,
                                  ),
                                ),
                              ),
                            ],
                          ),
                        );
                      }
                      return const SizedBox.shrink();
                    }),
                    
                    // Action buttons
                    _buildActionButtons(controller),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildProgressIndicator(SignupController controller) {
    return Container(
      padding: const EdgeInsets.all(Dimensions.space20),
      decoration: BoxDecoration(
        color: MyColor.brandWhite,
        boxShadow: [
          BoxShadow(
            offset: const Offset(0, 2),
            blurRadius: 8,
            color: MyColor.brandBlack.withOpacity(0.1),
          ),
        ],
      ),
      child: Column(
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'Step ${controller.currentStep.value.index + 1} of 5',
                style: mediumDefault.copyWith(
                  color: MyColor.contentTextColor,
                ),
              ),
              Text(
                '${(controller.progressValue * 100).toInt()}%',
                style: mediumDefault.copyWith(
                  color: MyColor.primaryColor,
                ),
              ),
            ],
          ),
          const SizedBox(height: Dimensions.space10),
          LinearProgressIndicator(
            value: controller.progressValue,
            backgroundColor: MyColor.borderColor,
            valueColor: AlwaysStoppedAnimation<Color>(MyColor.primaryColor),
            minHeight: 6,
          ),
        ],
      ),
    );
  }

  Widget _buildStepHeader(SignupController controller) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          controller.currentStepTitle,
          style: boldExtraLarge.copyWith(
            color: MyColor.primaryTextColor,
          ),
        ),
        const SizedBox(height: Dimensions.space8),
        Text(
          controller.currentStepDescription,
          style: regularDefault.copyWith(
            color: MyColor.contentTextColor,
          ),
        ),
      ],
    );
  }

  Widget _buildStepContent(SignupController controller) {
    switch (controller.currentStep.value) {
      case SignupStep.personalInfo:
        return PersonalInfoStep(controller: controller);
      case SignupStep.security:
        return SecurityStep(controller: controller);
      case SignupStep.verification:
        return VerificationStep(controller: controller);
      case SignupStep.bvnVerification:
        return BvnStep(controller: controller);
      case SignupStep.accountCreation:
        return AccountCreationStep(controller: controller);
    }
  }

  Widget _buildActionButtons(SignupController controller) {
    return Column(
      children: [
        // Continue/Next button
        Obx(() => SizedBox(
          width: double.infinity,
          child: EnhancedButton(
            text: _getButtonText(controller),
            onPressed: controller.canContinue || controller.currentStep.value == SignupStep.accountCreation
                ? controller.nextStep
                : null,
            isLoading: controller.isLoading.value,
            isDisabled: !controller.canContinue && controller.currentStep.value != SignupStep.accountCreation,
            size: ButtonSize.large,
          ),
        )),
        
        // Back button
        if (controller.canGoBack) ...[
          const SizedBox(height: Dimensions.space15),
          SizedBox(
            width: double.infinity,
            child: EnhancedButton(
              text: 'Back',
              onPressed: controller.previousStep,
              variant: ButtonVariant.outline,
              size: ButtonSize.large,
            ),
          ),
        ],
        
        // Login link
        const SizedBox(height: Dimensions.space20),
        Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Text(
              'Already have an account? ',
              style: regularDefault.copyWith(
                color: MyColor.contentTextColor,
              ),
            ),
            GestureDetector(
              onTap: () {
                // Navigate to login screen
                Get.back();
              },
              child: Text(
                'Sign In',
                style: mediumDefault.copyWith(
                  color: MyColor.primaryColor,
                  decoration: TextDecoration.underline,
                ),
              ),
            ),
          ],
        ),
      ],
    );
  }

  String _getButtonText(SignupController controller) {
    switch (controller.currentStep.value) {
      case SignupStep.personalInfo:
        return 'Continue';
      case SignupStep.security:
        return 'Send OTP';
      case SignupStep.verification:
        return 'Verify OTP';
      case SignupStep.bvnVerification:
        return 'Verify BVN';
      case SignupStep.accountCreation:
        return 'Create Account';
    }
  }
}
