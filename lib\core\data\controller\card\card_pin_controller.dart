import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

enum CardPinAction { create, change, reset, unlock }
enum CardAccountType { personal, business }

class CardPinController extends GetxController {
  bool isLoading = false;
  bool submitLoading = false;
  bool isPinVisible = false;
  bool isConfirmPinVisible = false;

  // Form controllers
  final TextEditingController currentPinController = TextEditingController();
  final TextEditingController newPinController = TextEditingController();
  final TextEditingController confirmPinController = TextEditingController();
  final TextEditingController transactionPinController = TextEditingController();

  // Focus nodes
  final FocusNode currentPinFocusNode = FocusNode();
  final FocusNode newPinFocusNode = FocusNode();
  final FocusNode confirmPinFocusNode = FocusNode();
  final FocusNode transactionPinFocusNode = FocusNode();

  // Selected values
  CardPinAction selectedAction = CardPinAction.create;
  String selectedCardId = '';
  String selectedCardType = ''; // physical, virtual
  CardAccountType selectedAccountType = CardAccountType.personal;

  // Card data
  Map<String, dynamic> selectedCard = {};
  List<Map<String, dynamic>> userCards = [];

  @override
  void onInit() {
    super.onInit();
    loadUserCards();
  }

  void togglePinVisibility() {
    isPinVisible = !isPinVisible;
    update();
  }

  void toggleConfirmPinVisibility() {
    isConfirmPinVisible = !isConfirmPinVisible;
    update();
  }

  void selectAction(CardPinAction action) {
    selectedAction = action;
    clearForm();
    update();
  }

  void selectCard(Map<String, dynamic> card) {
    selectedCard = card;
    selectedCardId = card['id']?.toString() ?? '';
    selectedCardType = card['type']?.toString() ?? '';
    selectedAccountType = card['account_type']?.toString() == 'business' 
        ? CardAccountType.business 
        : CardAccountType.personal;
    update();
  }

  Future<void> loadUserCards() async {
    isLoading = true;
    update();

    try {
      // Simulate API call to get user cards
      await Future.delayed(const Duration(seconds: 1));
      
      // Mock data for both personal and business cards
      userCards = [
        {
          'id': 'PC001',
          'type': 'physical',
          'account_type': 'personal',
          'card_name': 'John Doe Personal Card',
          'card_number': '**** **** **** 1234',
          'status': 'active',
          'pin_set': true,
          'pin_locked': false,
          'failed_attempts': 0,
          'spending_limit': 100000,
          'created_at': '2024-01-15',
        },
        {
          'id': 'VC001',
          'type': 'virtual',
          'account_type': 'personal',
          'card_name': 'John Doe Virtual Card',
          'card_number': '**** **** **** 5678',
          'status': 'active',
          'pin_set': true,
          'pin_locked': false,
          'failed_attempts': 0,
          'spending_limit': 50000,
          'created_at': '2024-01-20',
        },
        {
          'id': 'BC001',
          'type': 'physical',
          'account_type': 'business',
          'card_name': 'ABC Company Business Card',
          'card_number': '**** **** **** 9012',
          'status': 'active',
          'pin_set': false,
          'pin_locked': false,
          'failed_attempts': 0,
          'spending_limit': 500000,
          'created_at': '2024-01-25',
        },
        {
          'id': 'BV001',
          'type': 'virtual',
          'account_type': 'business',
          'card_name': 'ABC Company Virtual Card',
          'card_number': '**** **** **** 3456',
          'status': 'active',
          'pin_set': true,
          'pin_locked': true,
          'failed_attempts': 3,
          'spending_limit': 200000,
          'created_at': '2024-01-30',
        },
      ];
    } catch (e) {
      Get.snackbar(
        'Error',
        'Failed to load cards: ${e.toString()}',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red.withOpacity(0.1),
        colorText: Colors.red,
      );
    }

    isLoading = false;
    update();
  }

  Future<void> createCardPin() async {
    if (!validateCreatePinForm()) return;

    submitLoading = true;
    update();

    try {
      // Simulate API call
      await Future.delayed(const Duration(seconds: 2));
      
      // Update card data
      int cardIndex = userCards.indexWhere((card) => card['id'] == selectedCardId);
      if (cardIndex != -1) {
        userCards[cardIndex]['pin_set'] = true;
        userCards[cardIndex]['pin_locked'] = false;
        userCards[cardIndex]['failed_attempts'] = 0;
      }

      Get.snackbar(
        'PIN Created',
        'Card PIN has been created successfully.',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.green.withOpacity(0.1),
        colorText: Colors.green,
        duration: const Duration(seconds: 3),
      );
      
      clearForm();
      Get.back();
      
    } catch (e) {
      Get.snackbar(
        'PIN Creation Failed',
        'Failed to create card PIN. Please try again.',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red.withOpacity(0.1),
        colorText: Colors.red,
      );
    }

    submitLoading = false;
    update();
  }

  Future<void> changeCardPin() async {
    if (!validateChangePinForm()) return;

    submitLoading = true;
    update();

    try {
      // Simulate API call
      await Future.delayed(const Duration(seconds: 2));
      
      Get.snackbar(
        'PIN Changed',
        'Card PIN has been changed successfully.',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.green.withOpacity(0.1),
        colorText: Colors.green,
        duration: const Duration(seconds: 3),
      );
      
      clearForm();
      Get.back();
      
    } catch (e) {
      Get.snackbar(
        'PIN Change Failed',
        'Failed to change card PIN. Please verify your current PIN.',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red.withOpacity(0.1),
        colorText: Colors.red,
      );
    }

    submitLoading = false;
    update();
  }

  Future<void> resetCardPin() async {
    if (!validateResetPinForm()) return;

    submitLoading = true;
    update();

    try {
      // Simulate API call
      await Future.delayed(const Duration(seconds: 2));
      
      // Update card data
      int cardIndex = userCards.indexWhere((card) => card['id'] == selectedCardId);
      if (cardIndex != -1) {
        userCards[cardIndex]['pin_set'] = true;
        userCards[cardIndex]['pin_locked'] = false;
        userCards[cardIndex]['failed_attempts'] = 0;
      }

      Get.snackbar(
        'PIN Reset',
        'Card PIN has been reset successfully.',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.green.withOpacity(0.1),
        colorText: Colors.green,
        duration: const Duration(seconds: 3),
      );
      
      clearForm();
      Get.back();
      
    } catch (e) {
      Get.snackbar(
        'PIN Reset Failed',
        'Failed to reset card PIN. Please try again.',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red.withOpacity(0.1),
        colorText: Colors.red,
      );
    }

    submitLoading = false;
    update();
  }

  Future<void> unlockCardPin() async {
    if (!validateUnlockPinForm()) return;

    submitLoading = true;
    update();

    try {
      // Simulate API call
      await Future.delayed(const Duration(seconds: 2));
      
      // Update card data
      int cardIndex = userCards.indexWhere((card) => card['id'] == selectedCardId);
      if (cardIndex != -1) {
        userCards[cardIndex]['pin_locked'] = false;
        userCards[cardIndex]['failed_attempts'] = 0;
      }

      Get.snackbar(
        'PIN Unlocked',
        'Card PIN has been unlocked successfully.',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.green.withOpacity(0.1),
        colorText: Colors.green,
        duration: const Duration(seconds: 3),
      );
      
      clearForm();
      Get.back();
      
    } catch (e) {
      Get.snackbar(
        'PIN Unlock Failed',
        'Failed to unlock card PIN. Please verify your transaction PIN.',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red.withOpacity(0.1),
        colorText: Colors.red,
      );
    }

    submitLoading = false;
    update();
  }

  bool validateCreatePinForm() {
    if (newPinController.text.trim().length != 4) {
      Get.snackbar(
        'Invalid PIN',
        'PIN must be exactly 4 digits.',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red.withOpacity(0.1),
        colorText: Colors.red,
      );
      return false;
    }

    if (newPinController.text != confirmPinController.text) {
      Get.snackbar(
        'PIN Mismatch',
        'PIN and confirm PIN do not match.',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red.withOpacity(0.1),
        colorText: Colors.red,
      );
      return false;
    }

    if (transactionPinController.text.trim().length != 4) {
      Get.snackbar(
        'Transaction PIN Required',
        'Please enter your 4-digit transaction PIN.',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red.withOpacity(0.1),
        colorText: Colors.red,
      );
      return false;
    }

    return true;
  }

  bool validateChangePinForm() {
    if (currentPinController.text.trim().length != 4) {
      Get.snackbar(
        'Current PIN Required',
        'Please enter your current 4-digit PIN.',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red.withOpacity(0.1),
        colorText: Colors.red,
      );
      return false;
    }

    return validateCreatePinForm();
  }

  bool validateResetPinForm() {
    return validateCreatePinForm();
  }

  bool validateUnlockPinForm() {
    if (transactionPinController.text.trim().length != 4) {
      Get.snackbar(
        'Transaction PIN Required',
        'Please enter your 4-digit transaction PIN to unlock card.',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red.withOpacity(0.1),
        colorText: Colors.red,
      );
      return false;
    }

    return true;
  }

  void clearForm() {
    currentPinController.clear();
    newPinController.clear();
    confirmPinController.clear();
    transactionPinController.clear();
    isPinVisible = false;
    isConfirmPinVisible = false;
  }

  String getCardTypeText(String type) {
    switch (type.toLowerCase()) {
      case 'physical':
        return 'Physical Card';
      case 'virtual':
        return 'Virtual Card';
      default:
        return 'Card';
    }
  }

  String getAccountTypeText(String accountType) {
    switch (accountType.toLowerCase()) {
      case 'business':
        return 'Business';
      case 'personal':
        return 'Personal';
      default:
        return 'Personal';
    }
  }

  Color getCardStatusColor(String status) {
    switch (status.toLowerCase()) {
      case 'active':
        return Colors.green;
      case 'locked':
        return Colors.red;
      case 'suspended':
        return Colors.orange;
      case 'expired':
        return Colors.grey;
      default:
        return Colors.grey;
    }
  }

  IconData getCardTypeIcon(String type) {
    switch (type.toLowerCase()) {
      case 'physical':
        return Icons.credit_card;
      case 'virtual':
        return Icons.credit_card_outlined;
      default:
        return Icons.credit_card;
    }
  }

  IconData getAccountTypeIcon(String accountType) {
    switch (accountType.toLowerCase()) {
      case 'business':
        return Icons.business;
      case 'personal':
        return Icons.person;
      default:
        return Icons.person;
    }
  }
}
