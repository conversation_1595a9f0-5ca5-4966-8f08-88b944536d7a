import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:viserpay/core/utils/my_strings.dart';
import 'package:viserpay/data/model/ecommerce/cart_model.dart';
import 'package:viserpay/data/model/ecommerce/order_model.dart';
import 'package:viserpay/data/model/ecommerce/product_model.dart';
import 'package:viserpay/data/model/global/response_model/response_model.dart';
import 'package:viserpay/data/repo/ecommerce/ecommerce_repo.dart';
import 'package:viserpay/view/components/snack_bar/show_custom_snackbar.dart';

class EcommerceController extends GetxController {
  EcommerceRepo ecommerceRepo;
  
  EcommerceController({required this.ecommerceRepo});

  bool isLoading = false;
  bool isSubmitLoading = false;
  bool isCartLoading = false;
  
  // Products
  List<Product> products = [];
  List<Product> featuredProducts = [];
  List<Product> recommendedProducts = [];
  Product? selectedProduct;
  
  // Categories
  List<Category> categories = [];
  Category? selectedCategory;
  
  // Stores
  List<Store> stores = [];
  List<Store> nearbyStores = [];
  Store? selectedStore;
  
  // Cart
  List<CartItem> cartItems = [];
  CartSummary? cartSummary;
  int cartItemCount = 0;
  
  // Orders
  List<Order> orders = [];
  Order? selectedOrder;
  
  // Search and Filters
  TextEditingController searchController = TextEditingController();
  String currentSearchQuery = '';
  String selectedSortBy = 'name';
  String selectedSortOrder = 'asc';
  String? minPrice;
  String? maxPrice;
  
  // Pagination
  int currentPage = 1;
  bool hasMoreData = true;
  
  String curSymbol = '₦';

  @override
  void onInit() {
    super.onInit();
    loadInitialData();
  }

  @override
  void dispose() {
    searchController.dispose();
    super.dispose();
  }

  // Load initial data
  Future<void> loadInitialData() async {
    isLoading = true;
    update();

    try {
      await Future.wait([
        loadCategories(),
        loadFeaturedProducts(),
        loadProducts(),
        loadCart(),
      ]);
    } catch (e) {
      CustomSnackBar.error(errorList: [MyStrings.somethingWentWrong]);
    }

    isLoading = false;
    update();
  }

  // Product Management
  Future<void> loadProducts({bool isRefresh = false}) async {
    if (isRefresh) {
      currentPage = 1;
      hasMoreData = true;
      products.clear();
    }

    if (!hasMoreData) return;

    try {
      ResponseModel response = await ecommerceRepo.getProducts(
        page: currentPage,
        search: currentSearchQuery.isEmpty ? null : currentSearchQuery,
        categoryId: selectedCategory?.id,
        sortBy: selectedSortBy,
        sortOrder: selectedSortOrder,
      );

      if (response.statusCode == 200) {
        ProductResponseModel model = ProductResponseModel.fromJson(jsonDecode(response.responseJson));
        if (model.status?.toLowerCase() == MyStrings.success.toLowerCase()) {
          if (isRefresh) {
            products.clear();
          }
          products.addAll(model.data?.products ?? []);
          
          // Check if there's more data
          if (model.data?.pagination != null) {
            hasMoreData = currentPage < (model.data!.pagination!.lastPage ?? 1);
            currentPage++;
          }
        }
      }
    } catch (e) {
      CustomSnackBar.error(errorList: [MyStrings.somethingWentWrong]);
    }

    update();
  }

  Future<void> loadFeaturedProducts() async {
    try {
      ResponseModel response = await ecommerceRepo.getFeaturedProducts();
      if (response.statusCode == 200) {
        ProductResponseModel model = ProductResponseModel.fromJson(jsonDecode(response.responseJson));
        if (model.status?.toLowerCase() == MyStrings.success.toLowerCase()) {
          featuredProducts.clear();
          featuredProducts.addAll(model.data?.products ?? []);
        }
      }
    } catch (e) {
      CustomSnackBar.error(errorList: [MyStrings.somethingWentWrong]);
    }
  }

  Future<void> loadRecommendedProducts() async {
    try {
      ResponseModel response = await ecommerceRepo.getRecommendedProducts();
      if (response.statusCode == 200) {
        ProductResponseModel model = ProductResponseModel.fromJson(jsonDecode(response.responseJson));
        if (model.status?.toLowerCase() == MyStrings.success.toLowerCase()) {
          recommendedProducts.clear();
          recommendedProducts.addAll(model.data?.products ?? []);
        }
      }
    } catch (e) {
      CustomSnackBar.error(errorList: [MyStrings.somethingWentWrong]);
    }
  }

  Future<void> loadProductDetails(int productId) async {
    isLoading = true;
    update();

    try {
      ResponseModel response = await ecommerceRepo.getProductDetails(productId);
      if (response.statusCode == 200) {
        ProductResponseModel model = ProductResponseModel.fromJson(jsonDecode(response.responseJson));
        if (model.status?.toLowerCase() == MyStrings.success.toLowerCase()) {
          selectedProduct = model.data?.products?.first;
        }
      }
    } catch (e) {
      CustomSnackBar.error(errorList: [MyStrings.somethingWentWrong]);
    }

    isLoading = false;
    update();
  }

  // Category Management
  Future<void> loadCategories() async {
    try {
      ResponseModel response = await ecommerceRepo.getCategories();
      if (response.statusCode == 200) {
        ProductResponseModel model = ProductResponseModel.fromJson(jsonDecode(response.responseJson));
        if (model.status?.toLowerCase() == MyStrings.success.toLowerCase()) {
          categories.clear();
          categories.addAll(model.data?.categories ?? []);
        }
      }
    } catch (e) {
      CustomSnackBar.error(errorList: [MyStrings.somethingWentWrong]);
    }
  }

  void selectCategory(Category? category) {
    selectedCategory = category;
    currentPage = 1;
    hasMoreData = true;
    loadProducts(isRefresh: true);
  }

  // Store Management
  Future<void> loadStores({bool isRefresh = false}) async {
    if (isRefresh) {
      stores.clear();
    }

    try {
      ResponseModel response = await ecommerceRepo.getStores();
      if (response.statusCode == 200) {
        ProductResponseModel model = ProductResponseModel.fromJson(jsonDecode(response.responseJson));
        if (model.status?.toLowerCase() == MyStrings.success.toLowerCase()) {
          stores.clear();
          stores.addAll(model.data?.stores ?? []);
        }
      }
    } catch (e) {
      CustomSnackBar.error(errorList: [MyStrings.somethingWentWrong]);
    }

    update();
  }

  Future<void> loadNearbyStores(double latitude, double longitude) async {
    try {
      ResponseModel response = await ecommerceRepo.getNearbyStores(latitude, longitude);
      if (response.statusCode == 200) {
        ProductResponseModel model = ProductResponseModel.fromJson(jsonDecode(response.responseJson));
        if (model.status?.toLowerCase() == MyStrings.success.toLowerCase()) {
          nearbyStores.clear();
          nearbyStores.addAll(model.data?.stores ?? []);
        }
      }
    } catch (e) {
      CustomSnackBar.error(errorList: [MyStrings.somethingWentWrong]);
    }

    update();
  }

  // Cart Management
  Future<void> loadCart() async {
    isCartLoading = true;
    update();

    try {
      ResponseModel response = await ecommerceRepo.getCart();
      if (response.statusCode == 200) {
        CartResponseModel model = CartResponseModel.fromJson(jsonDecode(response.responseJson));
        if (model.status?.toLowerCase() == MyStrings.success.toLowerCase()) {
          cartItems.clear();
          cartItems.addAll(model.data?.items ?? []);
          cartSummary = model.data?.summary;
          cartItemCount = cartItems.length;
        }
      }
    } catch (e) {
      CustomSnackBar.error(errorList: [MyStrings.somethingWentWrong]);
    }

    isCartLoading = false;
    update();
  }

  Future<void> addToCart(int productId, int quantity, {int? variantId}) async {
    isSubmitLoading = true;
    update();

    try {
      AddToCartRequest request = AddToCartRequest(
        productId: productId,
        quantity: quantity,
        variantId: variantId,
      );

      ResponseModel response = await ecommerceRepo.addToCart(request);
      if (response.statusCode == 200) {
        CartResponseModel model = CartResponseModel.fromJson(jsonDecode(response.responseJson));
        if (model.status?.toLowerCase() == MyStrings.success.toLowerCase()) {
          CustomSnackBar.success(successList: model.message?.success ?? ['Added to cart successfully']);
          await loadCart();
        } else {
          CustomSnackBar.error(errorList: model.message?.error ?? [MyStrings.requestFail]);
        }
      } else {
        CustomSnackBar.error(errorList: [response.message]);
      }
    } catch (e) {
      CustomSnackBar.error(errorList: [MyStrings.somethingWentWrong]);
    }

    isSubmitLoading = false;
    update();
  }

  Future<void> updateCartItem(int cartItemId, int quantity) async {
    try {
      UpdateCartItemRequest request = UpdateCartItemRequest(
        cartItemId: cartItemId,
        quantity: quantity,
      );

      ResponseModel response = await ecommerceRepo.updateCartItem(request);
      if (response.statusCode == 200) {
        CartResponseModel model = CartResponseModel.fromJson(jsonDecode(response.responseJson));
        if (model.status?.toLowerCase() == MyStrings.success.toLowerCase()) {
          await loadCart();
        } else {
          CustomSnackBar.error(errorList: model.message?.error ?? [MyStrings.requestFail]);
        }
      } else {
        CustomSnackBar.error(errorList: [response.message]);
      }
    } catch (e) {
      CustomSnackBar.error(errorList: [MyStrings.somethingWentWrong]);
    }
  }

  Future<void> removeFromCart(int cartItemId) async {
    try {
      ResponseModel response = await ecommerceRepo.removeFromCart(cartItemId);
      if (response.statusCode == 200) {
        CartResponseModel model = CartResponseModel.fromJson(jsonDecode(response.responseJson));
        if (model.status?.toLowerCase() == MyStrings.success.toLowerCase()) {
          CustomSnackBar.success(successList: model.message?.success ?? ['Item removed from cart']);
          await loadCart();
        } else {
          CustomSnackBar.error(errorList: model.message?.error ?? [MyStrings.requestFail]);
        }
      } else {
        CustomSnackBar.error(errorList: [response.message]);
      }
    } catch (e) {
      CustomSnackBar.error(errorList: [MyStrings.somethingWentWrong]);
    }
  }

  // Search and Filter
  void searchProducts(String query) {
    currentSearchQuery = query;
    currentPage = 1;
    hasMoreData = true;
    loadProducts(isRefresh: true);
  }

  void applySortFilter(String sortBy, String sortOrder) {
    selectedSortBy = sortBy;
    selectedSortOrder = sortOrder;
    currentPage = 1;
    hasMoreData = true;
    loadProducts(isRefresh: true);
  }

  void applyPriceFilter(String? min, String? max) {
    minPrice = min;
    maxPrice = max;
    currentPage = 1;
    hasMoreData = true;
    loadProducts(isRefresh: true);
  }

  void clearFilters() {
    selectedCategory = null;
    currentSearchQuery = '';
    selectedSortBy = 'name';
    selectedSortOrder = 'asc';
    minPrice = null;
    maxPrice = null;
    searchController.clear();
    currentPage = 1;
    hasMoreData = true;
    loadProducts(isRefresh: true);
  }

  // Utility methods
  String formatPrice(String? price) {
    if (price == null || price.isEmpty) return '$curSymbol 0.00';
    try {
      double amount = double.parse(price);
      return '$curSymbol ${amount.toStringAsFixed(2)}';
    } catch (e) {
      return '$curSymbol $price';
    }
  }

  bool isInCart(int productId) {
    return cartItems.any((item) => item.productId == productId);
  }

  int getCartItemQuantity(int productId) {
    final item = cartItems.firstWhereOrNull((item) => item.productId == productId);
    return item?.quantity ?? 0;
  }
}
