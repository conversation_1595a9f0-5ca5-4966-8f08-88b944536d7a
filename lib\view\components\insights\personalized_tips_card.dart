import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:viserpay/core/utils/dimensions.dart';
import 'package:viserpay/core/utils/my_color.dart';
import 'package:viserpay/core/utils/style.dart';
import 'package:viserpay/data/model/insights/insights_model.dart';

class PersonalizedTipsCard extends StatelessWidget {
  final List<PersonalizedTip> tips;

  const PersonalizedTipsCard({
    super.key,
    required this.tips,
  });

  @override
  Widget build(BuildContext context) {
    if (tips.isEmpty) {
      return const SizedBox.shrink();
    }

    return Container(
      padding: const EdgeInsets.all(Dimensions.space20),
      decoration: BoxDecoration(
        color: MyColor.colorWhite,
        borderRadius: BorderRadius.circular(Dimensions.cardRadius),
        boxShadow: [
          BoxShadow(
            color: MyColor.colorGrey.withOpacity(0.1),
            blurRadius: 15,
            offset: const Offset(0, 5),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(Dimensions.space10),
                decoration: BoxDecoration(
                  color: MyColor.colorCyan.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(Dimensions.smallRadius),
                ),
                child: const Icon(
                  Icons.lightbulb,
                  color: MyColor.colorCyan,
                  size: 24,
                ),
              ),
              const SizedBox(width: Dimensions.space15),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Personalized Tips',
                      style: semiBoldLarge.copyWith(
                        color: MyColor.colorBlack,
                        fontSize: 18,
                      ),
                    ),
                    Text(
                      'Smart recommendations for you',
                      style: regularDefault.copyWith(
                        color: MyColor.colorGrey,
                        fontSize: 12,
                      ),
                    ),
                  ],
                ),
              ),
              GestureDetector(
                onTap: () => Get.toNamed('/all-tips'),
                child: Text(
                  'See All',
                  style: semiBoldDefault.copyWith(
                    color: MyColor.primaryColor,
                    fontSize: 12,
                  ),
                ),
              ),
            ],
          ),

          const SizedBox(height: Dimensions.space20),

          // Tips List
          ...tips.map((tip) => _buildTipItem(tip)),
        ],
      ),
    );
  }

  Widget _buildTipItem(PersonalizedTip tip) {
    Color priorityColor = _getPriorityColor(tip.priority ?? 'medium');
    IconData tipIcon = _getTipIcon(tip.category ?? 'general');

    return Container(
      margin: const EdgeInsets.only(bottom: Dimensions.space15),
      padding: const EdgeInsets.all(Dimensions.space15),
      decoration: BoxDecoration(
        color: priorityColor.withOpacity(0.05),
        borderRadius: BorderRadius.circular(Dimensions.smallRadius),
        border: Border.all(
          color: priorityColor.withOpacity(0.2),
          width: 1,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Tip Header
          Row(
            children: [
              Container(
                width: 35,
                height: 35,
                decoration: BoxDecoration(
                  color: priorityColor.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(Dimensions.smallRadius),
                ),
                child: Icon(
                  tipIcon,
                  color: priorityColor,
                  size: 18,
                ),
              ),
              const SizedBox(width: Dimensions.space12),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      tip.title ?? 'Financial Tip',
                      style: semiBoldDefault.copyWith(
                        color: MyColor.colorBlack,
                        fontSize: 14,
                      ),
                    ),
                    if (tip.priority != null)
                      Container(
                        margin: const EdgeInsets.only(top: 2),
                        padding: const EdgeInsets.symmetric(
                          horizontal: 6,
                          vertical: 2,
                        ),
                        decoration: BoxDecoration(
                          color: priorityColor.withOpacity(0.1),
                          borderRadius: BorderRadius.circular(8),
                        ),
                        child: Text(
                          tip.priority!.toUpperCase(),
                          style: regularSmall.copyWith(
                            color: priorityColor,
                            fontSize: 9,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ),
                  ],
                ),
              ),
            ],
          ),

          const SizedBox(height: Dimensions.space10),

          // Tip Description
          Text(
            tip.description ?? 'No description available',
            style: regularDefault.copyWith(
              color: MyColor.colorGrey,
              fontSize: 12,
              height: 1.4,
            ),
          ),

          // Action Button
          if (tip.actionText != null && tip.actionText!.isNotEmpty) ...[
            const SizedBox(height: Dimensions.space12),
            GestureDetector(
              onTap: () {
                if (tip.actionUrl != null) {
                  Get.toNamed(tip.actionUrl!);
                }
              },
              child: Container(
                padding: const EdgeInsets.symmetric(
                  horizontal: Dimensions.space12,
                  vertical: Dimensions.space8,
                ),
                decoration: BoxDecoration(
                  color: priorityColor,
                  borderRadius: BorderRadius.circular(Dimensions.smallRadius),
                ),
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Text(
                      tip.actionText!,
                      style: semiBoldDefault.copyWith(
                        color: MyColor.colorWhite,
                        fontSize: 12,
                      ),
                    ),
                    const SizedBox(width: Dimensions.space5),
                    const Icon(
                      Icons.arrow_forward,
                      color: MyColor.colorWhite,
                      size: 14,
                    ),
                  ],
                ),
              ),
            ),
          ],
        ],
      ),
    );
  }

  Color _getPriorityColor(String priority) {
    switch (priority.toLowerCase()) {
      case 'high':
        return MyColor.colorRed;
      case 'medium':
        return MyColor.colorOrange;
      case 'low':
        return MyColor.colorGreen;
      default:
        return MyColor.primaryColor;
    }
  }

  IconData _getTipIcon(String category) {
    switch (category.toLowerCase()) {
      case 'spending':
        return Icons.trending_down;
      case 'savings':
        return Icons.savings;
      case 'investment':
        return Icons.trending_up;
      case 'budget':
        return Icons.account_balance_wallet;
      case 'debt':
        return Icons.credit_card;
      case 'security':
        return Icons.security;
      case 'goals':
        return Icons.flag;
      default:
        return Icons.lightbulb;
    }
  }
}
