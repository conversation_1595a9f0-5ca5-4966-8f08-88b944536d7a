<?php

namespace App\Jobs;

use App\Models\Referral;
use App\Models\ReferralReward;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;

class ProcessReferralQualifications implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    public $timeout = 300; // 5 minutes timeout

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        Log::info('Starting referral qualification processing job');

        try {
            $qualifiedCount = $this->processQualifications();
            $rewardsProcessed = $this->processRewards();

            Log::info("Referral processing completed: {$qualifiedCount} qualified, {$rewardsProcessed} rewards processed");
        } catch (\Exception $e) {
            Log::error('Referral qualification processing failed: ' . $e->getMessage());
            throw $e;
        }
    }

    /**
     * Process pending referral qualifications
     */
    private function processQualifications(): int
    {
        return Referral::processQualificationChecks();
    }

    /**
     * Process pending referral rewards
     */
    private function processRewards(): int
    {
        return ReferralReward::processPendingRewards();
    }

    /**
     * Handle job failure
     */
    public function failed(\Throwable $exception): void
    {
        Log::error('Referral qualification processing job failed: ' . $exception->getMessage());
    }
}
