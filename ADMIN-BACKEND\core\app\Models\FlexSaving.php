<?php

namespace App\Models;

use App\Traits\Searchable;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Carbon\Carbon;

class FlexSaving extends Model
{
    use Searchable;

    protected $fillable = [
        'user_id',
        'balance',
        'daily_interest_rate',
        'total_interest_earned',
        'last_interest_calculation',
        'is_active'
    ];

    protected $casts = [
        'balance' => 'decimal:8',
        'daily_interest_rate' => 'decimal:6',
        'total_interest_earned' => 'decimal:8',
        'last_interest_calculation' => 'date',
        'is_active' => 'boolean'
    ];

    protected $appends = [
        'annual_interest_rate',
        'daily_interest_amount',
        'monthly_interest_projection',
        'days_since_last_calculation',
        'pending_interest'
    ];

    // Relationships
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    public function transactions(): HasMany
    {
        return $this->hasMany(SavingsTransaction::class, 'savings_id')
                    ->where('savings_type', 'flex_saving');
    }

    // Accessors
    public function getAnnualInterestRateAttribute(): float
    {
        return $this->daily_interest_rate * 365;
    }

    public function getDailyInterestAmountAttribute(): float
    {
        return $this->balance * $this->daily_interest_rate;
    }

    public function getMonthlyInterestProjectionAttribute(): float
    {
        return $this->balance * $this->daily_interest_rate * 30;
    }

    public function getDaysSinceLastCalculationAttribute(): int
    {
        if (!$this->last_interest_calculation) {
            return Carbon::parse($this->created_at)->diffInDays(Carbon::now());
        }
        return Carbon::parse($this->last_interest_calculation)->diffInDays(Carbon::now());
    }

    public function getPendingInterestAttribute(): float
    {
        return $this->balance * $this->daily_interest_rate * $this->days_since_last_calculation;
    }

    // Scopes
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    public function scopeInactive($query)
    {
        return $query->where('is_active', false);
    }

    public function scopeForUser($query, $userId)
    {
        return $query->where('user_id', $userId);
    }

    public function scopeWithBalance($query)
    {
        return $query->where('balance', '>', 0);
    }

    public function scopeDueForInterestCalculation($query)
    {
        return $query->where('is_active', true)
                    ->where('balance', '>', 0)
                    ->where(function($q) {
                        $q->whereNull('last_interest_calculation')
                          ->orWhere('last_interest_calculation', '<', Carbon::now()->toDateString());
                    });
    }

    // Methods
    public function deposit(float $amount, string $description = 'Flex savings deposit'): array
    {
        if (!$this->is_active) {
            return ['success' => false, 'message' => 'Flex savings account is not active'];
        }

        if ($amount <= 0) {
            return ['success' => false, 'message' => 'Invalid deposit amount'];
        }

        $previousBalance = $this->balance;
        $this->balance += $amount;
        $this->save();

        // Create transaction record
        SavingsTransaction::create([
            'user_id' => $this->user_id,
            'savings_type' => 'flex_saving',
            'savings_id' => $this->id,
            'type' => 'deposit',
            'amount' => $amount,
            'balance_before' => $previousBalance,
            'balance_after' => $this->balance,
            'description' => $description,
            'trx' => getTrx()
        ]);

        return [
            'success' => true,
            'amount' => $amount,
            'new_balance' => $this->balance,
            'daily_interest' => $this->daily_interest_amount
        ];
    }

    public function withdraw(float $amount, string $description = 'Flex savings withdrawal'): array
    {
        if (!$this->is_active) {
            return ['success' => false, 'message' => 'Flex savings account is not active'];
        }

        if ($amount <= 0) {
            return ['success' => false, 'message' => 'Invalid withdrawal amount'];
        }

        if ($amount > $this->balance) {
            return ['success' => false, 'message' => 'Insufficient flex savings balance'];
        }

        $previousBalance = $this->balance;
        $this->balance -= $amount;
        $this->save();

        // Create transaction record
        SavingsTransaction::create([
            'user_id' => $this->user_id,
            'savings_type' => 'flex_saving',
            'savings_id' => $this->id,
            'type' => 'withdrawal',
            'amount' => $amount,
            'balance_before' => $previousBalance,
            'balance_after' => $this->balance,
            'description' => $description,
            'trx' => getTrx()
        ]);

        return [
            'success' => true,
            'amount' => $amount,
            'new_balance' => $this->balance,
            'daily_interest' => $this->daily_interest_amount
        ];
    }

    public function calculateDailyInterest(): float
    {
        if (!$this->is_active || $this->balance <= 0) return 0;

        $daysSinceLastCalculation = $this->days_since_last_calculation;
        if ($daysSinceLastCalculation <= 0) return 0;

        return $this->balance * $this->daily_interest_rate * $daysSinceLastCalculation;
    }

    public function applyDailyInterest(): array
    {
        if (!$this->is_active || $this->balance <= 0) {
            return ['success' => false, 'message' => 'No interest to apply'];
        }

        $interest = $this->calculateDailyInterest();
        if ($interest <= 0) {
            return ['success' => false, 'message' => 'No interest to apply'];
        }

        $previousBalance = $this->balance;
        $this->balance += $interest;
        $this->total_interest_earned += $interest;
        $this->last_interest_calculation = Carbon::now()->toDateString();
        $this->save();

        // Create interest transaction
        SavingsTransaction::create([
            'user_id' => $this->user_id,
            'savings_type' => 'flex_saving',
            'savings_id' => $this->id,
            'type' => 'interest',
            'amount' => $interest,
            'balance_before' => $previousBalance,
            'balance_after' => $this->balance,
            'description' => 'Daily interest earned',
            'trx' => getTrx()
        ]);

        return [
            'success' => true,
            'interest_earned' => $interest,
            'new_balance' => $this->balance,
            'total_interest_earned' => $this->total_interest_earned
        ];
    }

    public function activate(): void
    {
        $this->is_active = true;
        $this->save();
    }

    public function deactivate(): void
    {
        $this->is_active = false;
        $this->save();
    }

    public function getInterestHistory($days = 30): array
    {
        $transactions = $this->transactions()
                            ->where('type', 'interest')
                            ->where('created_at', '>=', Carbon::now()->subDays($days))
                            ->orderBy('created_at', 'desc')
                            ->get();

        return [
            'total_interest' => $transactions->sum('amount'),
            'transaction_count' => $transactions->count(),
            'average_daily_interest' => $transactions->avg('amount'),
            'transactions' => $transactions->toArray()
        ];
    }

    public function getBalanceHistory($days = 30): array
    {
        $transactions = $this->transactions()
                            ->where('created_at', '>=', Carbon::now()->subDays($days))
                            ->orderBy('created_at')
                            ->get();

        $history = [];
        foreach ($transactions as $transaction) {
            $history[] = [
                'date' => $transaction->created_at->toDateString(),
                'type' => $transaction->type,
                'amount' => $transaction->amount,
                'balance' => $transaction->balance_after,
                'description' => $transaction->description
            ];
        }

        return $history;
    }

    public function getProjectedEarnings($days = 365): array
    {
        $currentBalance = $this->balance;
        $dailyRate = $this->daily_interest_rate;
        
        $projections = [];
        $balance = $currentBalance;
        
        for ($day = 1; $day <= $days; $day++) {
            $dailyInterest = $balance * $dailyRate;
            $balance += $dailyInterest;
            
            if ($day % 30 == 0 || $day == 7 || $day == 365) {
                $projections[] = [
                    'days' => $day,
                    'projected_balance' => round($balance, 2),
                    'total_interest' => round($balance - $currentBalance, 2),
                    'percentage_growth' => round((($balance - $currentBalance) / $currentBalance) * 100, 2)
                ];
            }
        }

        return $projections;
    }

    public static function createForUser($userId): self
    {
        return self::create([
            'user_id' => $userId,
            'balance' => 0,
            'daily_interest_rate' => self::getDefaultDailyRate(),
            'total_interest_earned' => 0,
            'is_active' => true
        ]);
    }

    public static function getDefaultDailyRate(): float
    {
        // 10% annual rate = 0.0274% daily rate
        return 10.0 / 100 / 365;
    }

    public static function getMinimumBalance(): float
    {
        return 100; // ₦100 minimum balance
    }

    public static function getMaximumBalance(): float
    {
        return 100000000; // ₦100,000,000 maximum balance
    }
}
