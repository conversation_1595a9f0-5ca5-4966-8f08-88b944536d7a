class NigerianFintechResponseModel {
  String? status;
  Message? message;
  NigerianFintechData? data;

  NigerianFintechResponseModel({
    this.status,
    this.message,
    this.data,
  });

  factory NigerianFintechResponseModel.fromJson(Map<String, dynamic> json) => NigerianFintechResponseModel(
        status: json["status"],
        message: json["message"] == null ? null : Message.fromJson(json["message"]),
        data: json["data"] == null ? null : NigerianFintechData.fromJson(json["data"]),
      );

  Map<String, dynamic> toJson() => {
        "status": status,
        "message": message?.toJson(),
        "data": data?.toJson(),
      };
}

class Message {
  List<String>? success;
  List<String>? error;

  Message({
    this.success,
    this.error,
  });

  factory Message.fromJson(Map<String, dynamic> json) => Message(
        success: json["success"] == null ? [] : List<String>.from(json["success"]!.map((x) => x)),
        error: json["error"] == null ? [] : List<String>.from(json["error"]!.map((x) => x)),
      );

  Map<String, dynamic> toJson() => {
        "success": success == null ? [] : List<dynamic>.from(success!.map((x) => x)),
        "error": error == null ? [] : List<dynamic>.from(error!.map((x) => x)),
      };
}

class NigerianFintechData {
  List<Agent>? agents;
  List<PosTerminal>? posTerminals;
  List<CashoutTransaction>? cashoutTransactions;
  List<BulkTransfer>? bulkTransfers;
  List<MerchantPayment>? merchantPayments;
  Agent? agent;
  PosTerminal? posTerminal;
  CashoutTransaction? cashoutTransaction;
  BulkTransfer? bulkTransfer;
  MerchantPayment? merchantPayment;
  AgentStats? agentStats;

  NigerianFintechData({
    this.agents,
    this.posTerminals,
    this.cashoutTransactions,
    this.bulkTransfers,
    this.merchantPayments,
    this.agent,
    this.posTerminal,
    this.cashoutTransaction,
    this.bulkTransfer,
    this.merchantPayment,
    this.agentStats,
  });

  factory NigerianFintechData.fromJson(Map<String, dynamic> json) => NigerianFintechData(
        agents: json["agents"] == null ? [] : List<Agent>.from(json["agents"]!.map((x) => Agent.fromJson(x))),
        posTerminals: json["pos_terminals"] == null ? [] : List<PosTerminal>.from(json["pos_terminals"]!.map((x) => PosTerminal.fromJson(x))),
        cashoutTransactions: json["cashout_transactions"] == null ? [] : List<CashoutTransaction>.from(json["cashout_transactions"]!.map((x) => CashoutTransaction.fromJson(x))),
        bulkTransfers: json["bulk_transfers"] == null ? [] : List<BulkTransfer>.from(json["bulk_transfers"]!.map((x) => BulkTransfer.fromJson(x))),
        merchantPayments: json["merchant_payments"] == null ? [] : List<MerchantPayment>.from(json["merchant_payments"]!.map((x) => MerchantPayment.fromJson(x))),
        agent: json["agent"] == null ? null : Agent.fromJson(json["agent"]),
        posTerminal: json["pos_terminal"] == null ? null : PosTerminal.fromJson(json["pos_terminal"]),
        cashoutTransaction: json["cashout_transaction"] == null ? null : CashoutTransaction.fromJson(json["cashout_transaction"]),
        bulkTransfer: json["bulk_transfer"] == null ? null : BulkTransfer.fromJson(json["bulk_transfer"]),
        merchantPayment: json["merchant_payment"] == null ? null : MerchantPayment.fromJson(json["merchant_payment"]),
        agentStats: json["agent_stats"] == null ? null : AgentStats.fromJson(json["agent_stats"]),
      );

  Map<String, dynamic> toJson() => {
        "agents": agents == null ? [] : List<dynamic>.from(agents!.map((x) => x.toJson())),
        "pos_terminals": posTerminals == null ? [] : List<dynamic>.from(posTerminals!.map((x) => x.toJson())),
        "cashout_transactions": cashoutTransactions == null ? [] : List<dynamic>.from(cashoutTransactions!.map((x) => x.toJson())),
        "bulk_transfers": bulkTransfers == null ? [] : List<dynamic>.from(bulkTransfers!.map((x) => x.toJson())),
        "merchant_payments": merchantPayments == null ? [] : List<dynamic>.from(merchantPayments!.map((x) => x.toJson())),
        "agent": agent?.toJson(),
        "pos_terminal": posTerminal?.toJson(),
        "cashout_transaction": cashoutTransaction?.toJson(),
        "bulk_transfer": bulkTransfer?.toJson(),
        "merchant_payment": merchantPayment?.toJson(),
        "agent_stats": agentStats?.toJson(),
      };
}

// Agent Banking Model
class Agent {
  int? id;
  int? userId;
  String? agentCode;
  String? businessName;
  String? businessAddress;
  String? businessPhone;
  String? businessEmail;
  String? businessLicense;
  double? latitude;
  double? longitude;
  String? state;
  String? lga;
  String? status; // 'active', 'inactive', 'suspended'
  bool? isVerified;
  String? walletBalance;
  String? commissionBalance;
  String? dailyLimit;
  String? monthlyLimit;
  double? rating;
  int? reviewCount;
  int? transactionCount;
  String? createdAt;
  String? updatedAt;

  Agent({
    this.id,
    this.userId,
    this.agentCode,
    this.businessName,
    this.businessAddress,
    this.businessPhone,
    this.businessEmail,
    this.businessLicense,
    this.latitude,
    this.longitude,
    this.state,
    this.lga,
    this.status,
    this.isVerified,
    this.walletBalance,
    this.commissionBalance,
    this.dailyLimit,
    this.monthlyLimit,
    this.rating,
    this.reviewCount,
    this.transactionCount,
    this.createdAt,
    this.updatedAt,
  });

  factory Agent.fromJson(Map<String, dynamic> json) => Agent(
        id: json["id"],
        userId: json["user_id"],
        agentCode: json["agent_code"],
        businessName: json["business_name"],
        businessAddress: json["business_address"],
        businessPhone: json["business_phone"],
        businessEmail: json["business_email"],
        businessLicense: json["business_license"],
        latitude: json["latitude"]?.toDouble(),
        longitude: json["longitude"]?.toDouble(),
        state: json["state"],
        lga: json["lga"],
        status: json["status"],
        isVerified: json["is_verified"] == 1,
        walletBalance: json["wallet_balance"],
        commissionBalance: json["commission_balance"],
        dailyLimit: json["daily_limit"],
        monthlyLimit: json["monthly_limit"],
        rating: json["rating"]?.toDouble(),
        reviewCount: json["review_count"],
        transactionCount: json["transaction_count"],
        createdAt: json["created_at"],
        updatedAt: json["updated_at"],
      );

  Map<String, dynamic> toJson() => {
        "id": id,
        "user_id": userId,
        "agent_code": agentCode,
        "business_name": businessName,
        "business_address": businessAddress,
        "business_phone": businessPhone,
        "business_email": businessEmail,
        "business_license": businessLicense,
        "latitude": latitude,
        "longitude": longitude,
        "state": state,
        "lga": lga,
        "status": status,
        "is_verified": isVerified == true ? 1 : 0,
        "wallet_balance": walletBalance,
        "commission_balance": commissionBalance,
        "daily_limit": dailyLimit,
        "monthly_limit": monthlyLimit,
        "rating": rating,
        "review_count": reviewCount,
        "transaction_count": transactionCount,
        "created_at": createdAt,
        "updated_at": updatedAt,
      };
}

// POS Terminal Model
class PosTerminal {
  int? id;
  int? agentId;
  String? terminalId;
  String? serialNumber;
  String? model;
  String? brand;
  String? status; // 'active', 'inactive', 'maintenance'
  String? lastTransactionDate;
  String? dailyTransactionLimit;
  String? monthlyTransactionLimit;
  String? createdAt;
  String? updatedAt;

  PosTerminal({
    this.id,
    this.agentId,
    this.terminalId,
    this.serialNumber,
    this.model,
    this.brand,
    this.status,
    this.lastTransactionDate,
    this.dailyTransactionLimit,
    this.monthlyTransactionLimit,
    this.createdAt,
    this.updatedAt,
  });

  factory PosTerminal.fromJson(Map<String, dynamic> json) => PosTerminal(
        id: json["id"],
        agentId: json["agent_id"],
        terminalId: json["terminal_id"],
        serialNumber: json["serial_number"],
        model: json["model"],
        brand: json["brand"],
        status: json["status"],
        lastTransactionDate: json["last_transaction_date"],
        dailyTransactionLimit: json["daily_transaction_limit"],
        monthlyTransactionLimit: json["monthly_transaction_limit"],
        createdAt: json["created_at"],
        updatedAt: json["updated_at"],
      );

  Map<String, dynamic> toJson() => {
        "id": id,
        "agent_id": agentId,
        "terminal_id": terminalId,
        "serial_number": serialNumber,
        "model": model,
        "brand": brand,
        "status": status,
        "last_transaction_date": lastTransactionDate,
        "daily_transaction_limit": dailyTransactionLimit,
        "monthly_transaction_limit": monthlyTransactionLimit,
        "created_at": createdAt,
        "updated_at": updatedAt,
      };
}

// Cashout Transaction Model
class CashoutTransaction {
  int? id;
  int? userId;
  int? agentId;
  String? transactionId;
  String? reference;
  String? amount;
  String? fee;
  String? totalAmount;
  String? status; // 'pending', 'processing', 'successful', 'failed', 'cancelled'
  String? agentCode;
  String? agentName;
  String? agentPhone;
  String? agentAddress;
  String? customerPhone;
  String? customerName;
  String? pin;
  String? otp;
  String? failureReason;
  String? createdAt;
  String? updatedAt;

  CashoutTransaction({
    this.id,
    this.userId,
    this.agentId,
    this.transactionId,
    this.reference,
    this.amount,
    this.fee,
    this.totalAmount,
    this.status,
    this.agentCode,
    this.agentName,
    this.agentPhone,
    this.agentAddress,
    this.customerPhone,
    this.customerName,
    this.pin,
    this.otp,
    this.failureReason,
    this.createdAt,
    this.updatedAt,
  });

  factory CashoutTransaction.fromJson(Map<String, dynamic> json) => CashoutTransaction(
        id: json["id"],
        userId: json["user_id"],
        agentId: json["agent_id"],
        transactionId: json["transaction_id"],
        reference: json["reference"],
        amount: json["amount"],
        fee: json["fee"],
        totalAmount: json["total_amount"],
        status: json["status"],
        agentCode: json["agent_code"],
        agentName: json["agent_name"],
        agentPhone: json["agent_phone"],
        agentAddress: json["agent_address"],
        customerPhone: json["customer_phone"],
        customerName: json["customer_name"],
        pin: json["pin"],
        otp: json["otp"],
        failureReason: json["failure_reason"],
        createdAt: json["created_at"],
        updatedAt: json["updated_at"],
      );

  Map<String, dynamic> toJson() => {
        "id": id,
        "user_id": userId,
        "agent_id": agentId,
        "transaction_id": transactionId,
        "reference": reference,
        "amount": amount,
        "fee": fee,
        "total_amount": totalAmount,
        "status": status,
        "agent_code": agentCode,
        "agent_name": agentName,
        "agent_phone": agentPhone,
        "agent_address": agentAddress,
        "customer_phone": customerPhone,
        "customer_name": customerName,
        "pin": pin,
        "otp": otp,
        "failure_reason": failureReason,
        "created_at": createdAt,
        "updated_at": updatedAt,
      };
}

// Bulk Transfer Model
class BulkTransfer {
  int? id;
  int? userId;
  String? batchId;
  String? title;
  String? description;
  int? totalRecipients;
  String? totalAmount;
  String? totalFee;
  String? grandTotal;
  String? status; // 'pending', 'processing', 'completed', 'failed', 'cancelled'
  int? successfulTransfers;
  int? failedTransfers;
  List<BulkTransferRecipient>? recipients;
  String? createdAt;
  String? updatedAt;

  BulkTransfer({
    this.id,
    this.userId,
    this.batchId,
    this.title,
    this.description,
    this.totalRecipients,
    this.totalAmount,
    this.totalFee,
    this.grandTotal,
    this.status,
    this.successfulTransfers,
    this.failedTransfers,
    this.recipients,
    this.createdAt,
    this.updatedAt,
  });

  factory BulkTransfer.fromJson(Map<String, dynamic> json) => BulkTransfer(
        id: json["id"],
        userId: json["user_id"],
        batchId: json["batch_id"],
        title: json["title"],
        description: json["description"],
        totalRecipients: json["total_recipients"],
        totalAmount: json["total_amount"],
        totalFee: json["total_fee"],
        grandTotal: json["grand_total"],
        status: json["status"],
        successfulTransfers: json["successful_transfers"],
        failedTransfers: json["failed_transfers"],
        recipients: json["recipients"] == null ? [] : List<BulkTransferRecipient>.from(json["recipients"]!.map((x) => BulkTransferRecipient.fromJson(x))),
        createdAt: json["created_at"],
        updatedAt: json["updated_at"],
      );

  Map<String, dynamic> toJson() => {
        "id": id,
        "user_id": userId,
        "batch_id": batchId,
        "title": title,
        "description": description,
        "total_recipients": totalRecipients,
        "total_amount": totalAmount,
        "total_fee": totalFee,
        "grand_total": grandTotal,
        "status": status,
        "successful_transfers": successfulTransfers,
        "failed_transfers": failedTransfers,
        "recipients": recipients == null ? [] : List<dynamic>.from(recipients!.map((x) => x.toJson())),
        "created_at": createdAt,
        "updated_at": updatedAt,
      };
}

class BulkTransferRecipient {
  int? id;
  int? bulkTransferId;
  String? recipientName;
  String? recipientPhone;
  String? recipientEmail;
  String? accountNumber;
  String? bankCode;
  String? bankName;
  String? amount;
  String? fee;
  String? totalAmount;
  String? status; // 'pending', 'successful', 'failed'
  String? reference;
  String? failureReason;
  String? processedAt;

  BulkTransferRecipient({
    this.id,
    this.bulkTransferId,
    this.recipientName,
    this.recipientPhone,
    this.recipientEmail,
    this.accountNumber,
    this.bankCode,
    this.bankName,
    this.amount,
    this.fee,
    this.totalAmount,
    this.status,
    this.reference,
    this.failureReason,
    this.processedAt,
  });

  factory BulkTransferRecipient.fromJson(Map<String, dynamic> json) => BulkTransferRecipient(
        id: json["id"],
        bulkTransferId: json["bulk_transfer_id"],
        recipientName: json["recipient_name"],
        recipientPhone: json["recipient_phone"],
        recipientEmail: json["recipient_email"],
        accountNumber: json["account_number"],
        bankCode: json["bank_code"],
        bankName: json["bank_name"],
        amount: json["amount"],
        fee: json["fee"],
        totalAmount: json["total_amount"],
        status: json["status"],
        reference: json["reference"],
        failureReason: json["failure_reason"],
        processedAt: json["processed_at"],
      );

  Map<String, dynamic> toJson() => {
        "id": id,
        "bulk_transfer_id": bulkTransferId,
        "recipient_name": recipientName,
        "recipient_phone": recipientPhone,
        "recipient_email": recipientEmail,
        "account_number": accountNumber,
        "bank_code": bankCode,
        "bank_name": bankName,
        "amount": amount,
        "fee": fee,
        "total_amount": totalAmount,
        "status": status,
        "reference": reference,
        "failure_reason": failureReason,
        "processed_at": processedAt,
      };
}

// Merchant Payment Model
class MerchantPayment {
  int? id;
  int? userId;
  int? merchantId;
  String? transactionId;
  String? reference;
  String? amount;
  String? fee;
  String? totalAmount;
  String? status; // 'pending', 'successful', 'failed', 'cancelled'
  String? merchantCode;
  String? merchantName;
  String? merchantPhone;
  String? merchantAddress;
  String? paymentMethod; // 'qr_code', 'merchant_code', 'phone_number'
  String? description;
  String? customerPhone;
  String? customerName;
  String? failureReason;
  String? createdAt;
  String? updatedAt;

  MerchantPayment({
    this.id,
    this.userId,
    this.merchantId,
    this.transactionId,
    this.reference,
    this.amount,
    this.fee,
    this.totalAmount,
    this.status,
    this.merchantCode,
    this.merchantName,
    this.merchantPhone,
    this.merchantAddress,
    this.paymentMethod,
    this.description,
    this.customerPhone,
    this.customerName,
    this.failureReason,
    this.createdAt,
    this.updatedAt,
  });

  factory MerchantPayment.fromJson(Map<String, dynamic> json) => MerchantPayment(
        id: json["id"],
        userId: json["user_id"],
        merchantId: json["merchant_id"],
        transactionId: json["transaction_id"],
        reference: json["reference"],
        amount: json["amount"],
        fee: json["fee"],
        totalAmount: json["total_amount"],
        status: json["status"],
        merchantCode: json["merchant_code"],
        merchantName: json["merchant_name"],
        merchantPhone: json["merchant_phone"],
        merchantAddress: json["merchant_address"],
        paymentMethod: json["payment_method"],
        description: json["description"],
        customerPhone: json["customer_phone"],
        customerName: json["customer_name"],
        failureReason: json["failure_reason"],
        createdAt: json["created_at"],
        updatedAt: json["updated_at"],
      );

  Map<String, dynamic> toJson() => {
        "id": id,
        "user_id": userId,
        "merchant_id": merchantId,
        "transaction_id": transactionId,
        "reference": reference,
        "amount": amount,
        "fee": fee,
        "total_amount": totalAmount,
        "status": status,
        "merchant_code": merchantCode,
        "merchant_name": merchantName,
        "merchant_phone": merchantPhone,
        "merchant_address": merchantAddress,
        "payment_method": paymentMethod,
        "description": description,
        "customer_phone": customerPhone,
        "customer_name": customerName,
        "failure_reason": failureReason,
        "created_at": createdAt,
        "updated_at": updatedAt,
      };
}

class AgentStats {
  String? totalCashouts;
  int? totalTransactions;
  String? totalCommissions;
  String? thisMonth;
  String? lastMonth;
  String? todayTransactions;
  String? weeklyAverage;
  String? monthlyAverage;

  AgentStats({
    this.totalCashouts,
    this.totalTransactions,
    this.totalCommissions,
    this.thisMonth,
    this.lastMonth,
    this.todayTransactions,
    this.weeklyAverage,
    this.monthlyAverage,
  });

  factory AgentStats.fromJson(Map<String, dynamic> json) => AgentStats(
        totalCashouts: json["total_cashouts"],
        totalTransactions: json["total_transactions"],
        totalCommissions: json["total_commissions"],
        thisMonth: json["this_month"],
        lastMonth: json["last_month"],
        todayTransactions: json["today_transactions"],
        weeklyAverage: json["weekly_average"],
        monthlyAverage: json["monthly_average"],
      );

  Map<String, dynamic> toJson() => {
        "total_cashouts": totalCashouts,
        "total_transactions": totalTransactions,
        "total_commissions": totalCommissions,
        "this_month": thisMonth,
        "last_month": lastMonth,
        "today_transactions": todayTransactions,
        "weekly_average": weeklyAverage,
        "monthly_average": monthlyAverage,
      };
}

// Request Models
class CashoutRequest {
  String agentCode;
  String amount;
  String pin;

  CashoutRequest({
    required this.agentCode,
    required this.amount,
    required this.pin,
  });

  Map<String, dynamic> toJson() => {
        "agent_code": agentCode,
        "amount": amount,
        "pin": pin,
      };
}

class BulkTransferRequest {
  String title;
  String description;
  List<BulkTransferRecipientRequest> recipients;

  BulkTransferRequest({
    required this.title,
    required this.description,
    required this.recipients,
  });

  Map<String, dynamic> toJson() => {
        "title": title,
        "description": description,
        "recipients": recipients.map((x) => x.toJson()).toList(),
      };
}

class BulkTransferRecipientRequest {
  String recipientName;
  String recipientPhone;
  String accountNumber;
  String bankCode;
  String amount;

  BulkTransferRecipientRequest({
    required this.recipientName,
    required this.recipientPhone,
    required this.accountNumber,
    required this.bankCode,
    required this.amount,
  });

  Map<String, dynamic> toJson() => {
        "recipient_name": recipientName,
        "recipient_phone": recipientPhone,
        "account_number": accountNumber,
        "bank_code": bankCode,
        "amount": amount,
      };
}

class MerchantPaymentRequest {
  String merchantCode;
  String amount;
  String description;
  String pin;

  MerchantPaymentRequest({
    required this.merchantCode,
    required this.amount,
    required this.description,
    required this.pin,
  });

  Map<String, dynamic> toJson() => {
        "merchant_code": merchantCode,
        "amount": amount,
        "description": description,
        "pin": pin,
      };
}
