import 'package:flutter/material.dart';
import 'package:viserpay/core/utils/dimensions.dart';
import 'package:viserpay/core/utils/my_color.dart';

class CustomDivider extends StatelessWidget {
  final double space;
  final Color color;
  final bool? onlyTop;
  final bool? onlybottom;

  const CustomDivider({super.key, this.space = Dimensions.space20, this.color = MyColor.colorBlack, this.onlyTop = true, this.onlybottom = true});

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        onlyTop! ? SizedBox(height: space) : const SizedBox.shrink(),
        Divider(
          color: color.withOpacity(0.2),
          height: 0.5,
          thickness: 0.5,
        ),
        onlybottom! ? SizedBox(height: space) : const SizedBox.shrink(),
      ],
    );
  }
}
