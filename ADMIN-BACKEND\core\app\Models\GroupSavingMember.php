<?php

namespace App\Models;

use App\Traits\Searchable;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Carbon\Carbon;

class GroupSavingMember extends Model
{
    use Searchable;

    protected $fillable = [
        'group_id',
        'user_id',
        'position',
        'total_contributed',
        'has_received_payout',
        'payout_date',
        'status',
        'joined_at'
    ];

    protected $casts = [
        'total_contributed' => 'decimal:8',
        'has_received_payout' => 'boolean',
        'payout_date' => 'date',
        'joined_at' => 'datetime'
    ];

    protected $appends = [
        'contribution_count',
        'is_current_recipient',
        'days_until_payout',
        'contribution_percentage',
        'is_up_to_date'
    ];

    // Relationships
    public function group(): BelongsTo
    {
        return $this->belongsTo(GroupSaving::class, 'group_id');
    }

    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    // Accessors
    public function getContributionCountAttribute(): int
    {
        if ($this->group->contribution_amount <= 0) return 0;
        return (int) ($this->total_contributed / $this->group->contribution_amount);
    }

    public function getIsCurrentRecipientAttribute(): bool
    {
        if ($this->has_received_payout || $this->status !== 'active') return false;
        
        $nextRecipient = $this->group->next_recipient;
        return $nextRecipient && $nextRecipient->id === $this->id;
    }

    public function getDaysUntilPayoutAttribute(): ?int
    {
        if (!$this->is_current_recipient || !$this->group->next_payout_date) return null;
        
        return Carbon::now()->diffInDays($this->group->next_payout_date, false);
    }

    public function getContributionPercentageAttribute(): float
    {
        $expectedContributions = $this->getExpectedContributions();
        if ($expectedContributions <= 0) return 0;
        
        return min(100, ($this->contribution_count / $expectedContributions) * 100);
    }

    public function getIsUpToDateAttribute(): bool
    {
        return $this->contribution_count >= $this->getExpectedContributions();
    }

    // Scopes
    public function scopeActive($query)
    {
        return $query->where('status', 'active');
    }

    public function scopeInactive($query)
    {
        return $query->where('status', 'inactive');
    }

    public function scopeRemoved($query)
    {
        return $query->where('status', 'removed');
    }

    public function scopeForUser($query, $userId)
    {
        return $query->where('user_id', $userId);
    }

    public function scopeForGroup($query, $groupId)
    {
        return $query->where('group_id', $groupId);
    }

    public function scopeHasReceivedPayout($query)
    {
        return $query->where('has_received_payout', true);
    }

    public function scopePendingPayout($query)
    {
        return $query->where('has_received_payout', false)
                    ->where('status', 'active');
    }

    public function scopeByPosition($query, $position)
    {
        return $query->where('position', $position);
    }

    public function scopeUpToDate($query)
    {
        return $query->whereRaw('
            (total_contributed / (
                SELECT contribution_amount 
                FROM group_savings 
                WHERE id = group_savings_members.group_id
            )) >= (
                SELECT CASE 
                    WHEN status = "recruiting" THEN 0
                    WHEN status = "active" THEN (
                        SELECT COUNT(*) 
                        FROM group_savings_members gsm2 
                        WHERE gsm2.group_id = group_savings_members.group_id 
                        AND gsm2.has_received_payout = true
                    ) + 1
                    ELSE 0
                END
                FROM group_savings 
                WHERE id = group_savings_members.group_id
            )
        ');
    }

    // Methods
    public function getExpectedContributions(): int
    {
        $group = $this->group;
        
        if ($group->status === 'recruiting') return 0;
        if ($group->status !== 'active') return $group->duration_cycles;
        
        // For active groups, expected contributions = cycles completed + 1
        return $group->cycles_completed + 1;
    }

    public function getContributionDeficit(): float
    {
        $expectedContributions = $this->getExpectedContributions();
        $actualContributions = $this->contribution_count;
        $deficit = max(0, $expectedContributions - $actualContributions);
        
        return $deficit * $this->group->contribution_amount;
    }

    public function canReceivePayout(): bool
    {
        return $this->status === 'active' 
               && !$this->has_received_payout 
               && $this->is_up_to_date;
    }

    public function getPayoutEstimate(): array
    {
        if ($this->has_received_payout) {
            return [
                'already_received' => true,
                'payout_date' => $this->payout_date,
                'amount' => 0
            ];
        }

        $group = $this->group;
        $baseAmount = $group->total_pool_amount;
        $interest = $baseAmount * ($group->interest_rate / 100);
        $totalAmount = $baseAmount + $interest;

        // Calculate position in queue
        $positionInQueue = $group->members()
                               ->where('status', 'active')
                               ->where('has_received_payout', false)
                               ->where('position', '<', $this->position)
                               ->count() + 1;

        $estimatedDate = null;
        if ($group->status === 'active' && $group->next_payout_date) {
            $cyclesUntilPayout = $positionInQueue - 1;
            
            $estimatedDate = match($group->frequency) {
                'daily' => Carbon::parse($group->next_payout_date)->addDays($cyclesUntilPayout),
                'weekly' => Carbon::parse($group->next_payout_date)->addWeeks($cyclesUntilPayout),
                'monthly' => Carbon::parse($group->next_payout_date)->addMonths($cyclesUntilPayout),
                default => null
            };
        }

        return [
            'already_received' => false,
            'estimated_amount' => $totalAmount,
            'base_amount' => $baseAmount,
            'interest' => $interest,
            'position_in_queue' => $positionInQueue,
            'estimated_date' => $estimatedDate?->toDateString(),
            'can_receive' => $this->canReceivePayout()
        ];
    }

    public function getContributionHistory(): array
    {
        $transactions = SavingsTransaction::where('user_id', $this->user_id)
                                         ->where('savings_type', 'group_saving')
                                         ->where('savings_id', $this->group_id)
                                         ->where('type', 'deposit')
                                         ->orderBy('created_at', 'desc')
                                         ->get();

        return [
            'total_contributions' => $transactions->count(),
            'total_amount' => $transactions->sum('amount'),
            'last_contribution' => $transactions->first()?->created_at,
            'transactions' => $transactions->toArray()
        ];
    }

    public function leave($reason = 'Member left voluntarily'): array
    {
        if ($this->has_received_payout) {
            return [
                'success' => false, 
                'message' => 'Cannot leave after receiving payout'
            ];
        }

        $this->status = 'removed';
        $this->save();

        // Update group member count
        $this->group->current_members--;
        $this->group->save();

        return [
            'success' => true,
            'message' => 'Successfully left the group',
            'refund_amount' => $this->total_contributed
        ];
    }

    public function reactivate(): array
    {
        if ($this->group->status !== 'recruiting') {
            return [
                'success' => false,
                'message' => 'Cannot rejoin - group is not recruiting'
            ];
        }

        if ($this->group->is_full) {
            return [
                'success' => false,
                'message' => 'Cannot rejoin - group is full'
            ];
        }

        $this->status = 'active';
        $this->save();

        // Update group member count
        $this->group->current_members++;
        $this->group->save();

        return [
            'success' => true,
            'message' => 'Successfully rejoined the group'
        ];
    }

    public static function getPositionInGroup($groupId, $userId): ?int
    {
        $member = self::where('group_id', $groupId)
                     ->where('user_id', $userId)
                     ->first();
        
        return $member?->position;
    }

    public static function getUserGroupMemberships($userId): \Illuminate\Database\Eloquent\Collection
    {
        return self::where('user_id', $userId)
                  ->with(['group', 'group.creator'])
                  ->orderBy('created_at', 'desc')
                  ->get();
    }
}
