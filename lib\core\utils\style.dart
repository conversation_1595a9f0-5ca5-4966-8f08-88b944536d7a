import 'package:flutter/material.dart';
import 'package:viserpay/core/utils/dimensions.dart';
import 'package:viserpay/core/utils/my_color.dart';

const TextStyle heading = TextStyle(fontFamily: '<PERSON><PERSON><PERSON>', color: MyColor.primaryTextColor, fontWeight: FontWeight.w600, fontSize: Dimensions.fontMediumLarge + 1);
const TextStyle title = TextStyle(fontFamily: '<PERSON><PERSON><PERSON>', color: MyColor.primaryTextColor, fontWeight: FontWeight.w600, fontSize: Dimensions.fontLarge);

//

const TextStyle lightOverSmall = TextStyle(fontFamily: 'Inter', color: MyColor.primaryTextColor, fontWeight: FontWeight.w300, fontSize: Dimensions.fontOverSmall);

const TextStyle lightExtraSmall = TextStyle(fontFamily: 'Inter', color: MyColor.primaryTextColor, fontWeight: FontWeight.w300, fontSize: Dimensions.fontExtraSmall);

const TextStyle lightSmall = TextStyle(fontFamily: 'Inter', color: MyColor.primaryTextColor, fontWeight: FontWeight.w300, fontSize: Dimensions.fontSmall);

const TextStyle lightDefault = TextStyle(fontFamily: 'Inter', color: MyColor.primaryTextColor, fontWeight: FontWeight.w300, fontSize: Dimensions.fontDefault);

const TextStyle lightLarge = TextStyle(fontFamily: 'Inter', color: MyColor.primaryTextColor, fontWeight: FontWeight.w300, fontSize: Dimensions.fontLarge);

const TextStyle lightMediumLarge = TextStyle(fontFamily: 'Inter', color: MyColor.primaryTextColor, fontWeight: FontWeight.w300, fontSize: Dimensions.fontMediumLarge);

const TextStyle lightExtraLarge = TextStyle(fontFamily: 'Inter', color: MyColor.primaryTextColor, fontWeight: FontWeight.w300, fontSize: Dimensions.fontExtraLarge);

const TextStyle lightOverLarge = TextStyle(fontFamily: 'Inter', color: MyColor.primaryTextColor, fontWeight: FontWeight.w300, fontSize: Dimensions.fontOverLarge);

const TextStyle regularOverSmall = TextStyle(fontFamily: 'Inter', color: MyColor.primaryTextColor, fontWeight: FontWeight.w400, fontSize: Dimensions.fontOverSmall);

const TextStyle regularExtraSmall = TextStyle(fontFamily: 'Inter', color: MyColor.primaryTextColor, fontWeight: FontWeight.w400, fontSize: Dimensions.fontExtraSmall);

const TextStyle regularSmall = TextStyle(fontFamily: 'Inter', color: MyColor.primaryTextColor, fontWeight: FontWeight.w400, fontSize: Dimensions.fontSmall);

const TextStyle dateTextStyle = TextStyle(fontFamily: 'Inter', color: MyColor.primaryTextColor, fontWeight: FontWeight.w400, fontStyle: FontStyle.italic, fontSize: Dimensions.fontSmall);

const TextStyle regularDefault = TextStyle(fontFamily: 'Inter', color: MyColor.primaryTextColor, fontWeight: FontWeight.w400, fontSize: Dimensions.fontDefault);

const TextStyle regularLarge = TextStyle(fontFamily: 'Inter', color: MyColor.primaryTextColor, fontWeight: FontWeight.w400, fontSize: Dimensions.fontLarge);

const TextStyle regularMediumLarge = TextStyle(fontFamily: 'Inter', color: MyColor.primaryTextColor, fontWeight: FontWeight.w400, fontSize: Dimensions.fontMediumLarge);

const TextStyle regularExtraLarge = TextStyle(fontFamily: 'Inter', color: MyColor.primaryTextColor, fontWeight: FontWeight.w400, fontSize: Dimensions.fontExtraLarge);

const TextStyle regularOverLarge = TextStyle(fontFamily: 'Inter', color: MyColor.primaryTextColor, fontWeight: FontWeight.w400, fontSize: Dimensions.fontOverLarge);

const TextStyle mediumOverSmall = TextStyle(fontFamily: 'Inter', color: MyColor.primaryTextColor, fontWeight: FontWeight.w500, fontSize: Dimensions.fontOverSmall);

const TextStyle mediumExtraSmall = TextStyle(fontFamily: 'Inter', color: MyColor.primaryTextColor, fontWeight: FontWeight.w500, fontSize: Dimensions.fontExtraSmall);

const TextStyle mediumSmall = TextStyle(fontFamily: 'Inter', color: MyColor.primaryTextColor, fontWeight: FontWeight.w500, fontSize: Dimensions.fontSmall);

const TextStyle mediumDefault = TextStyle(fontFamily: 'Inter', color: MyColor.primaryTextColor, fontWeight: FontWeight.w500, fontSize: Dimensions.fontDefault);

const TextStyle mediumLarge = TextStyle(fontFamily: 'Inter', color: MyColor.primaryTextColor, fontWeight: FontWeight.w500, fontSize: Dimensions.fontLarge);

const TextStyle mediumMediumLarge = TextStyle(fontFamily: 'Inter', color: MyColor.primaryTextColor, fontWeight: FontWeight.w500, fontSize: Dimensions.fontMediumLarge);

const TextStyle mediumExtraLarge = TextStyle(fontFamily: 'Inter', color: MyColor.primaryTextColor, fontWeight: FontWeight.w500, fontSize: Dimensions.fontExtraLarge);

const TextStyle mediumOverLarge = TextStyle(fontFamily: 'Inter', color: MyColor.primaryTextColor, fontWeight: FontWeight.w500, fontSize: Dimensions.fontOverLarge);

const TextStyle semiBoldOverSmall = TextStyle(fontFamily: 'Inter', color: MyColor.primaryTextColor, fontWeight: FontWeight.w600, fontSize: Dimensions.fontOverSmall);

const TextStyle semiBoldExtraSmall = TextStyle(fontFamily: 'Inter', color: MyColor.primaryTextColor, fontWeight: FontWeight.w600, fontSize: Dimensions.fontExtraSmall);

const TextStyle semiBoldSmall = TextStyle(fontFamily: 'Inter', color: MyColor.primaryTextColor, fontWeight: FontWeight.w600, fontSize: Dimensions.fontSmall);

const TextStyle semiBoldDefault = TextStyle(fontFamily: 'Inter', color: MyColor.primaryTextColor, fontWeight: FontWeight.w600, fontSize: Dimensions.fontDefault);

const TextStyle semiBoldLarge = TextStyle(fontFamily: 'Inter', color: MyColor.primaryTextColor, fontWeight: FontWeight.w600, fontSize: Dimensions.fontLarge);

const TextStyle semiBoldMediumLarge = TextStyle(fontFamily: 'Inter', color: MyColor.primaryTextColor, fontWeight: FontWeight.w600, fontSize: Dimensions.fontMediumLarge);

const TextStyle semiBoldExtraLarge = TextStyle(fontFamily: 'Inter', color: MyColor.primaryTextColor, fontWeight: FontWeight.w600, fontSize: Dimensions.fontExtraLarge);

const TextStyle semiBoldOverLarge = TextStyle(fontFamily: 'Inter', color: MyColor.primaryTextColor, fontWeight: FontWeight.w600, fontSize: Dimensions.fontOverLarge);

// semi-bold
const TextStyle boldOverSmall = TextStyle(fontFamily: 'Inter', color: MyColor.primaryTextColor, fontWeight: FontWeight.w700, fontSize: Dimensions.fontOverSmall);

const TextStyle boldExtraSmall = TextStyle(fontFamily: 'Inter', color: MyColor.primaryTextColor, fontWeight: FontWeight.w700, fontSize: Dimensions.fontExtraSmall);

const TextStyle boldSmall = TextStyle(fontFamily: 'Inter', color: MyColor.primaryTextColor, fontWeight: FontWeight.w700, fontSize: Dimensions.fontSmall);

const TextStyle boldDefault = TextStyle(fontFamily: 'Inter', color: MyColor.primaryTextColor, fontWeight: FontWeight.w700, fontSize: Dimensions.fontDefault);

const TextStyle boldLarge = TextStyle(fontFamily: 'Inter', color: MyColor.primaryTextColor, fontWeight: FontWeight.w700, fontSize: Dimensions.fontLarge);

const TextStyle boldMediumLarge = TextStyle(fontFamily: 'Inter', color: MyColor.primaryTextColor, fontWeight: FontWeight.w700, fontSize: Dimensions.fontMediumLarge);

const TextStyle boldExtraLarge = TextStyle(fontFamily: 'Inter', color: MyColor.primaryTextColor, fontWeight: FontWeight.w700, fontSize: Dimensions.fontExtraLarge);

const TextStyle boldOverLarge = TextStyle(fontFamily: 'Inter', color: MyColor.primaryTextColor, fontWeight: FontWeight.w700, fontSize: Dimensions.fontBalance);
