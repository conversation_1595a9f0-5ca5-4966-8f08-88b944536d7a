---
name: Release published

on:
  release:
    types: [ published, edited ]

jobs:
  add-changelog:
    name: Add Changelog
    runs-on: ubuntu-latest

    steps:
      - name: Add Changelog
        uses: nexmo/github-actions/nexmo-changelog@main
        env:
          CHANGELOG_AUTH_TOKEN: ${{ secrets.CHANGELOG_AUTH_TOKEN }}
          CHANGELOG_CATEGORY: Server SDK
          CHANGELOG_RELEASE_TITLE: vonage-php-sdk-core
          CHANGELOG_SUBCATEGORY: php
