import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:viserpay/core/utils/dimensions.dart';
import 'package:viserpay/core/utils/my_color.dart';
import 'package:viserpay/core/utils/my_strings.dart';
import 'package:viserpay/core/utils/style.dart';
import 'package:viserpay/data/controller/insights/insights_controller.dart';
import 'package:viserpay/view/components/insights/financial_overview_card.dart';
import 'package:viserpay/view/components/insights/spending_analysis_card.dart';
import 'package:viserpay/view/components/insights/savings_insights_card.dart';
import 'package:viserpay/view/components/insights/personalized_tips_card.dart';
import 'package:viserpay/view/components/insights/chart_widget.dart';
import 'package:viserpay/view/components/insights/period_selector.dart';

class EnhancedInsightsScreen extends StatefulWidget {
  const EnhancedInsightsScreen({super.key});

  @override
  State<EnhancedInsightsScreen> createState() => _EnhancedInsightsScreenState();
}

class _EnhancedInsightsScreenState extends State<EnhancedInsightsScreen>
    with TickerProviderStateMixin {
  late TabController _tabController;
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 5, vsync: this);
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );

    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeOut,
    ));

    _animationController.forward();
  }

  @override
  void dispose() {
    _tabController.dispose();
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return GetBuilder<InsightsController>(
      builder: (controller) {
        return Scaffold(
          backgroundColor: MyColor.screenBgColor,
          body: SafeArea(
            child: NestedScrollView(
              headerSliverBuilder: (context, innerBoxIsScrolled) {
                return [
                  // Custom App Bar
                  SliverAppBar(
                    expandedHeight: 120,
                    floating: false,
                    pinned: true,
                    backgroundColor: MyColor.screenBgColor,
                    elevation: 0,
                    flexibleSpace: FlexibleSpaceBar(
                      background: _buildAppBarContent(controller),
                    ),
                    bottom: PreferredSize(
                      preferredSize: const Size.fromHeight(50),
                      child: _buildTabBar(),
                    ),
                  ),
                ];
              },
              body: FadeTransition(
                opacity: _fadeAnimation,
                child: TabBarView(
                  controller: _tabController,
                  children: [
                    _buildOverviewTab(controller),
                    _buildSpendingTab(controller),
                    _buildSavingsTab(controller),
                    _buildIncomeTab(controller),
                    _buildGoalsTab(controller),
                  ],
                ),
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildAppBarContent(InsightsController controller) {
    return Padding(
      padding: const EdgeInsets.all(Dimensions.space20),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              GestureDetector(
                onTap: () => Get.back(),
                child: Container(
                  width: 40,
                  height: 40,
                  decoration: BoxDecoration(
                    color: MyColor.colorWhite,
                    borderRadius: BorderRadius.circular(Dimensions.smallRadius),
                    boxShadow: [
                      BoxShadow(
                        color: MyColor.colorGrey.withOpacity(0.1),
                        blurRadius: 10,
                        offset: const Offset(0, 2),
                      ),
                    ],
                  ),
                  child: const Icon(
                    Icons.arrow_back,
                    color: MyColor.colorBlack,
                    size: 20,
                  ),
                ),
              ),
              const SizedBox(width: Dimensions.space15),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Financial Insights',
                      style: semiBoldLarge.copyWith(
                        color: MyColor.colorBlack,
                        fontSize: 20,
                      ),
                    ),
                    Text(
                      'Your financial health at a glance',
                      style: regularDefault.copyWith(
                        color: MyColor.colorGrey,
                        fontSize: 12,
                      ),
                    ),
                  ],
                ),
              ),
              // Financial Health Score
              Container(
                padding: const EdgeInsets.symmetric(
                  horizontal: Dimensions.space12,
                  vertical: Dimensions.space8,
                ),
                decoration: BoxDecoration(
                  gradient: MyColor.primaryGradient,
                  borderRadius: BorderRadius.circular(Dimensions.smallRadius),
                ),
                child: Column(
                  children: [
                    Text(
                      '${controller.calculateFinancialHealthScore()}',
                      style: boldLarge.copyWith(
                        color: MyColor.colorWhite,
                        fontSize: 18,
                      ),
                    ),
                    Text(
                      controller.getFinancialHealthRating(),
                      style: regularSmall.copyWith(
                        color: MyColor.colorWhite,
                        fontSize: 10,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
          const SizedBox(height: Dimensions.space15),
          // Period Selector
          PeriodSelector(
            selectedPeriod: controller.selectedPeriod,
            onPeriodChanged: controller.changePeriod,
          ),
        ],
      ),
    );
  }

  Widget _buildTabBar() {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: Dimensions.space20),
      decoration: BoxDecoration(
        color: MyColor.colorWhite,
        borderRadius: BorderRadius.circular(Dimensions.mediumRadius),
        boxShadow: [
          BoxShadow(
            color: MyColor.colorGrey.withOpacity(0.1),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: TabBar(
        controller: _tabController,
        isScrollable: true,
        indicator: BoxDecoration(
          color: MyColor.primaryColor,
          borderRadius: BorderRadius.circular(Dimensions.mediumRadius),
        ),
        labelColor: MyColor.colorWhite,
        unselectedLabelColor: MyColor.colorGrey,
        labelStyle: semiBoldDefault.copyWith(fontSize: 12),
        unselectedLabelStyle: regularDefault.copyWith(fontSize: 12),
        tabs: const [
          Tab(text: 'Overview'),
          Tab(text: 'Spending'),
          Tab(text: 'Savings'),
          Tab(text: 'Income'),
          Tab(text: 'Goals'),
        ],
      ),
    );
  }

  Widget _buildOverviewTab(InsightsController controller) {
    if (controller.isLoading) {
      return const Center(
        child: CircularProgressIndicator(color: MyColor.primaryColor),
      );
    }

    return RefreshIndicator(
      onRefresh: controller.refreshInsights,
      color: MyColor.primaryColor,
      child: SingleChildScrollView(
        physics: const AlwaysScrollableScrollPhysics(),
        padding: const EdgeInsets.all(Dimensions.space15),
        child: Column(
          children: [
            // Financial Overview Card
            FinancialOverviewCard(
              financialOverview: controller.financialOverview,
            ),
            
            const SizedBox(height: Dimensions.space15),
            
            // Quick Stats Row
            Row(
              children: [
                Expanded(
                  child: _buildQuickStatCard(
                    title: 'Spending',
                    value: controller.spendingAnalysis?.totalSpent ?? '0',
                    trend: controller.getSpendingTrend(),
                    color: MyColor.colorRed,
                    icon: Icons.trending_down,
                  ),
                ),
                const SizedBox(width: Dimensions.space10),
                Expanded(
                  child: _buildQuickStatCard(
                    title: 'Savings',
                    value: controller.savingsInsights?.totalSavings ?? '0',
                    trend: 'up',
                    color: MyColor.colorGreen,
                    icon: Icons.trending_up,
                  ),
                ),
              ],
            ),
            
            const SizedBox(height: Dimensions.space15),
            
            // Chart Widget
            if (controller.chartData.isNotEmpty)
              ChartWidget(
                chartData: controller.chartData.first,
                height: 200,
              ),
            
            const SizedBox(height: Dimensions.space15),
            
            // Personalized Tips
            PersonalizedTipsCard(
              tips: controller.getHighPriorityTips(),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSpendingTab(InsightsController controller) {
    return RefreshIndicator(
      onRefresh: controller.loadSpendingInsights,
      color: MyColor.primaryColor,
      child: SingleChildScrollView(
        physics: const AlwaysScrollableScrollPhysics(),
        padding: const EdgeInsets.all(Dimensions.space15),
        child: Column(
          children: [
            SpendingAnalysisCard(
              spendingAnalysis: controller.spendingAnalysis,
            ),
            
            const SizedBox(height: Dimensions.space15),
            
            // Top Categories Chart
            if (controller.getTopSpendingCategories().isNotEmpty)
              _buildCategoryChart(controller.getTopSpendingCategories()),
            
            const SizedBox(height: Dimensions.space15),
            
            // Monthly Trend
            if (controller.getMonthlySpendingTrend().isNotEmpty)
              _buildMonthlyTrendChart(controller.getMonthlySpendingTrend()),
          ],
        ),
      ),
    );
  }

  Widget _buildSavingsTab(InsightsController controller) {
    return RefreshIndicator(
      onRefresh: controller.loadSavingsInsights,
      color: MyColor.primaryColor,
      child: SingleChildScrollView(
        physics: const AlwaysScrollableScrollPhysics(),
        padding: const EdgeInsets.all(Dimensions.space15),
        child: Column(
          children: [
            SavingsInsightsCard(
              savingsInsights: controller.savingsInsights,
            ),
            
            const SizedBox(height: Dimensions.space15),
            
            // Savings Goals Progress
            if (controller.getSavingsGoals().isNotEmpty)
              _buildSavingsGoalsCard(controller.getSavingsGoals()),
          ],
        ),
      ),
    );
  }

  Widget _buildIncomeTab(InsightsController controller) {
    return RefreshIndicator(
      onRefresh: controller.loadIncomeInsights,
      color: MyColor.primaryColor,
      child: SingleChildScrollView(
        physics: const AlwaysScrollableScrollPhysics(),
        padding: const EdgeInsets.all(Dimensions.space15),
        child: Column(
          children: [
            _buildIncomeAnalysisCard(controller.incomeAnalysis),
          ],
        ),
      ),
    );
  }

  Widget _buildGoalsTab(InsightsController controller) {
    return RefreshIndicator(
      onRefresh: controller.loadGoalsProgress,
      color: MyColor.primaryColor,
      child: SingleChildScrollView(
        physics: const AlwaysScrollableScrollPhysics(),
        padding: const EdgeInsets.all(Dimensions.space15),
        child: Column(
          children: [
            _buildGoalsProgressCard(controller.goalsProgress),
          ],
        ),
      ),
    );
  }

  Widget _buildQuickStatCard({
    required String title,
    required String value,
    required String trend,
    required Color color,
    required IconData icon,
  }) {
    return Container(
      padding: const EdgeInsets.all(Dimensions.space15),
      decoration: BoxDecoration(
        color: MyColor.colorWhite,
        borderRadius: BorderRadius.circular(Dimensions.mediumRadius),
        boxShadow: [
          BoxShadow(
            color: MyColor.colorGrey.withOpacity(0.1),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(icon, color: color, size: 20),
              const Spacer(),
              Container(
                padding: const EdgeInsets.symmetric(
                  horizontal: 6,
                  vertical: 2,
                ),
                decoration: BoxDecoration(
                  color: color.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(10),
                ),
                child: Text(
                  trend,
                  style: regularSmall.copyWith(
                    color: color,
                    fontSize: 10,
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: Dimensions.space10),
          Text(
            title,
            style: regularDefault.copyWith(
              color: MyColor.colorGrey,
              fontSize: 12,
            ),
          ),
          const SizedBox(height: Dimensions.space5),
          Text(
            '₦$value',
            style: semiBoldLarge.copyWith(
              color: MyColor.colorBlack,
              fontSize: 16,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildCategoryChart(List categories) {
    // Placeholder for category chart
    return Container(
      height: 200,
      padding: const EdgeInsets.all(Dimensions.space15),
      decoration: BoxDecoration(
        color: MyColor.colorWhite,
        borderRadius: BorderRadius.circular(Dimensions.mediumRadius),
        boxShadow: [
          BoxShadow(
            color: MyColor.colorGrey.withOpacity(0.1),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Top Spending Categories',
            style: semiBoldDefault.copyWith(
              color: MyColor.colorBlack,
            ),
          ),
          const SizedBox(height: Dimensions.space15),
          Expanded(
            child: Center(
              child: Text(
                'Category Chart Placeholder',
                style: regularDefault.copyWith(
                  color: MyColor.colorGrey,
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildMonthlyTrendChart(List trends) {
    // Placeholder for monthly trend chart
    return Container(
      height: 200,
      padding: const EdgeInsets.all(Dimensions.space15),
      decoration: BoxDecoration(
        color: MyColor.colorWhite,
        borderRadius: BorderRadius.circular(Dimensions.mediumRadius),
        boxShadow: [
          BoxShadow(
            color: MyColor.colorGrey.withOpacity(0.1),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Monthly Spending Trend',
            style: semiBoldDefault.copyWith(
              color: MyColor.colorBlack,
            ),
          ),
          const SizedBox(height: Dimensions.space15),
          Expanded(
            child: Center(
              child: Text(
                'Monthly Trend Chart Placeholder',
                style: regularDefault.copyWith(
                  color: MyColor.colorGrey,
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSavingsGoalsCard(List goals) {
    // Placeholder for savings goals
    return Container(
      padding: const EdgeInsets.all(Dimensions.space15),
      decoration: BoxDecoration(
        color: MyColor.colorWhite,
        borderRadius: BorderRadius.circular(Dimensions.mediumRadius),
        boxShadow: [
          BoxShadow(
            color: MyColor.colorGrey.withOpacity(0.1),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Savings Goals Progress',
            style: semiBoldDefault.copyWith(
              color: MyColor.colorBlack,
            ),
          ),
          const SizedBox(height: Dimensions.space15),
          Text(
            'Savings Goals Placeholder',
            style: regularDefault.copyWith(
              color: MyColor.colorGrey,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildIncomeAnalysisCard(dynamic incomeAnalysis) {
    // Placeholder for income analysis
    return Container(
      padding: const EdgeInsets.all(Dimensions.space15),
      decoration: BoxDecoration(
        color: MyColor.colorWhite,
        borderRadius: BorderRadius.circular(Dimensions.mediumRadius),
        boxShadow: [
          BoxShadow(
            color: MyColor.colorGrey.withOpacity(0.1),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Income Analysis',
            style: semiBoldDefault.copyWith(
              color: MyColor.colorBlack,
            ),
          ),
          const SizedBox(height: Dimensions.space15),
          Text(
            'Income Analysis Placeholder',
            style: regularDefault.copyWith(
              color: MyColor.colorGrey,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildGoalsProgressCard(dynamic goalsProgress) {
    // Placeholder for goals progress
    return Container(
      padding: const EdgeInsets.all(Dimensions.space15),
      decoration: BoxDecoration(
        color: MyColor.colorWhite,
        borderRadius: BorderRadius.circular(Dimensions.mediumRadius),
        boxShadow: [
          BoxShadow(
            color: MyColor.colorGrey.withOpacity(0.1),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Goals Progress',
            style: semiBoldDefault.copyWith(
              color: MyColor.colorBlack,
            ),
          ),
          const SizedBox(height: Dimensions.space15),
          Text(
            'Goals Progress Placeholder',
            style: regularDefault.copyWith(
              color: MyColor.colorGrey,
            ),
          ),
        ],
      ),
    );
  }
}
