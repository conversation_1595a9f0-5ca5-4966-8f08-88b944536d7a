class UrlContainer {
  static const String domainUrl = 'https://script.viserlab.com/viserpay';

  static const String baseUrl = '$domainUrl/api/';

  static const String registrationEndPoint = 'register';
  static const String loginEndPoint = 'login';
  static const String logoutUrl = 'logout';

  static const String forgetPasswordEndPoint = 'password/email';
  static const String passwordVerifyEndPoint = 'password/verify-code';
  static const String resetPasswordEndPoint = 'password/reset';
  static const String verify2FAUrl = 'verify-g2fa';
  static const String otpVerify = 'otp-verify';
  static const String otpResend = 'otp-resend';

  static const String verifyEmailEndPoint = 'verify-email';
  static const String verifySmsEndPoint = 'verify-mobile';
  static const String resendVerifyCodeEndPoint = 'resend-verify/';
  static const String authorizationCodeEndPoint = 'authorization';
  static const String accountDisable = "account/delete";

  static const String dashBoardUrl = 'dashboard';
  static const String transactionEndpoint = 'transactions';
  static const String sendMoneyEndpoint = 'send/money';
  static const String sendMoneyHistoryEndpoint = 'send/money/history';
  static const String cashOutEndpoint = 'cash-out';
  static const String mobilerechargeEndpoint = 'mobile/recharge';

  static const String withdrawHistoryUrl = 'withdraw/history';
  static const String withdrawMoneyUrl = 'withdraw/methods';
  static const String submitWithdrawMoneyUrl = 'withdraw/money';
  static const String withdrawPreviewUrl = 'withdraw/preview';
  static const String withdrawMoneySubmitUrl = 'withdraw/money/submit';
  static const String addWithdrawMethodUrl = 'withdraw/add-method';
  static const String withdrawMethodUrl = 'withdraw/methods';
  static const String withdrawMethodEdit = 'withdraw/edit-method';
  static const String withdrawMethodUpdate = 'withdraw/method/update';

  //kyc
  static const String kycFormUrl = 'kyc-form';
  static const String kycSubmitUrl = 'kyc-submit';

  static const String generalSettingEndPoint = 'general-setting';
  static const String moduleSettingEndPoint = 'module-setting';

  //privacy policy
  static const String privacyPolicyEndPoint = 'policy-pages';

  //profile
  static const String getProfileEndPoint = 'user-info';
  static const String updateProfileEndPoint = 'profile-setting';
  static const String profileCompleteEndPoint = 'user-data-submit';

  //change password
  static const String changePasswordEndPoint = 'change-password';
  static const String countryEndPoint = 'get-countries';

  static const String deviceTokenEndPoint = 'get/device/token';
  static const String languageUrl = 'language/';

  // make payment
  static const String makePaymentCheckMerchantUrl = "merchant/exist";
  static const String makePaymentUrl = "make-payment";
  static const String makePaymentVerifyOtpUrl = "make-payment";

  // donation money
  static const String donationEndPoint = "donation";
  static const String donationHistoryEndPoint = "donation/history";

  // bank transfer money
  static const String addBankEndPoint = "add/bank";
  static const String bankTransferEndPoint = "bank/transfer";
  static const String bankDeleteEndPoint = "delete/bank";
  static const String bankTransferHistoryEndPoint = "$bankTransferEndPoint/history";

  // card management endpoints
  static const String userCardsEndPoint = "user/cards";
  static const String createCardPinEndPoint = "card/pin/create";
  static const String changeCardPinEndPoint = "card/pin/change";
  static const String resetCardPinEndPoint = "card/pin/reset";
  static const String unlockCardPinEndPoint = "card/pin/unlock";
  static const String blockCardEndPoint = "card/block";
  static const String unblockCardEndPoint = "card/unblock";
  static const String updateCardLimitEndPoint = "card/limit/update";
  static const String cardTransactionsEndPoint = "card/transactions";
  static const String toggleCardOnlineEndPoint = "card/toggle/online";
  static const String toggleCardInternationalEndPoint = "card/toggle/international";
  static const String toggleCardAtmEndPoint = "card/toggle/atm";
  static const String cardDetailsEndPoint = "card/details";
  static const String replaceCardEndPoint = "card/replace";
  static const String activateCardEndPoint = "card/activate";
  static const String cardPinAttemptsEndPoint = "card/pin/attempts";
  static const String verifyCardPinEndPoint = "card/pin/verify";
  static const String cardSecuritySettingsEndPoint = "card/security/settings";
  static const String updateCardSecurityEndPoint = "card/security/update";

  // paybill
  static const String paybillEndPoint = "pay/bill";
  static const String paybillHistoryEndPoint = "$paybillEndPoint/history";
  static const String paybillDownLoad = "$paybillEndPoint/download";

  // add money
  static const String addMoneyHistoryEndPoint = "deposit/history";
  static const String addMoneyMethodEndPoint = "deposit/methods";
  static const String addMoneyInsertEndPoint = "deposit/insert";

  // money out
  static const String moneyOutUrl = "money-out";
  static const String submitMoneyOutUrl = "money-out";

  // request money
  static const String requestMoneyEndPoint = "request/money";
  static const String requestMoneySubmitEndPoint = "request/money";
  static const String requestToMeEndPoint = "requests";
  static const String myRequestHistoryEndPoint = "my/requested/history";
  static const String requestRejectUrl = "accept/reject";
  static const String requestAcceptUrl = "accept/request";

  static const String checkAgentUrl = "agent/exist";
  static const String checkMerchantUrl = "merchant/exist";
  static const String checkUserUrl = "user/exist";

  static const String qrCodeEndPoint = "qr-code";
  static const String qrScanEndPoint = "qr-code/scan";
  static const String qrCodeImageDownload = "qr-code/download";

  // Account Management
  static const String accountsEndPoint = "accounts";
  static const String accountTypesEndPoint = "account-types";
  static const String createAccountEndPoint = "accounts/create";
  static const String setDefaultAccountEndPoint = "accounts/set-default";
  static const String accountDetailsEndPoint = "accounts";
  static const String updateAccountEndPoint = "accounts/update";
  static const String closeAccountEndPoint = "accounts/close";
  static const String accountTransferEndPoint = "accounts/transfer";
  static const String accountTransactionsEndPoint = "accounts/transactions";
  static const String accountStatementEndPoint = "accounts/statement";
  static const String accountBalanceEndPoint = "accounts/balance";
  static const String toggleAccountFreezeEndPoint = "accounts/toggle-freeze";

  // E-commerce Endpoints
  static const String productsEndPoint = "products";
  static const String categoriesEndPoint = "categories";
  static const String storesEndPoint = "stores";
  static const String cartEndPoint = "cart";
  static const String addToCartEndPoint = "cart/add";
  static const String updateCartEndPoint = "cart/update";
  static const String removeFromCartEndPoint = "cart/remove";
  static const String clearCartEndPoint = "cart/clear";
  static const String applyCouponEndPoint = "cart/apply-coupon";
  static const String removeCouponEndPoint = "cart/remove-coupon";
  static const String ordersEndPoint = "orders";
  static const String createOrderEndPoint = "orders/create";
  static const String orderDetailsEndPoint = "orders";
  static const String cancelOrderEndPoint = "orders/cancel";
  static const String trackOrderEndPoint = "orders/track";
  static const String orderHistoryEndPoint = "orders/history";

  // Insights Endpoints
  static const String insightsEndPoint = "insights";
  static const String financialOverviewEndPoint = "insights/financial-overview";
  static const String spendingInsightsEndPoint = "insights/spending";
  static const String savingsInsightsEndPoint = "insights/savings";
  static const String incomeInsightsEndPoint = "insights/income";
  static const String goalsProgressEndPoint = "insights/goals-progress";
  static const String personalizedTipsEndPoint = "insights/tips";
  static const String recommendationsEndPoint = "insights/recommendations";
  static const String creditScoreEndPoint = "insights/credit-score";
  static const String chartDataEndPoint = "insights/chart-data";
  static const String comparisonDataEndPoint = "insights/comparison";
  static const String spendingByCategoryEndPoint = "insights/spending-by-category";
  static const String monthlyTrendsEndPoint = "insights/monthly-trends";
  static const String budgetAnalysisEndPoint = "insights/budget-analysis";
  static const String investmentInsightsEndPoint = "insights/investments";
  static const String cashFlowAnalysisEndPoint = "insights/cash-flow";
  static const String financialHealthScoreEndPoint = "insights/health-score";
  static const String expensePredictionsEndPoint = "insights/expense-predictions";
  static const String savingsRecommendationsEndPoint = "insights/savings-recommendations";
  static const String debtAnalysisEndPoint = "insights/debt-analysis";
  static const String merchantInsightsEndPoint = "insights/merchant-insights";
  static const String transactionPatternsEndPoint = "insights/transaction-patterns";
  static const String financialMilestonesEndPoint = "insights/milestones";
  static const String exportInsightsEndPoint = "insights/export";
  static const String insightsPreferencesEndPoint = "insights/preferences";

  static const String limit = "limit-charge";
  static const String faq = "faq";
  static const String pinEndPoint = "pin";
  static const String notificationSettingsEndPoint = "notification/settings";

  static const String twoFactor = "twofactor";
  static const String twoFactorEnable = "twofactor/enable";
  static const String twoFactorDisable = "twofactor/disable";

  static const String pinValidate = "pin/validate";

  static const String countryFlagImageLink = 'https://flagpedia.net/data/flags/h24/{countryCode}.webp';
}
