<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Investment Products Table
        Schema::create('investment_products', function (Blueprint $table) {
            $table->id();
            $table->string('name');
            $table->enum('type', ['treasury_bills', 'mutual_funds', 'corporate_bonds', 'stocks', 'real_estate']);
            $table->text('description')->nullable();
            $table->decimal('minimum_amount', 28, 8);
            $table->decimal('maximum_amount', 28, 8)->nullable();
            $table->decimal('interest_rate_min', 5, 2);
            $table->decimal('interest_rate_max', 5, 2);
            $table->enum('risk_level', ['low', 'medium', 'high']);
            $table->integer('duration_days')->nullable(); // For fixed-term investments
            $table->decimal('management_fee', 5, 2)->default(0); // Annual management fee percentage
            $table->boolean('is_active')->default(true);
            $table->boolean('admin_configurable_rate')->default(true);
            $table->json('features')->nullable(); // Additional product features
            $table->timestamps();
            
            $table->index(['type', 'is_active']);
        });

        // User Investments Table
        Schema::create('user_investments', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('user_id');
            $table->unsignedBigInteger('product_id');
            $table->decimal('amount', 28, 8);
            $table->decimal('units', 16, 8);
            $table->decimal('unit_price', 28, 8);
            $table->decimal('current_value', 28, 8);
            $table->decimal('total_returns', 28, 8)->default(0);
            $table->decimal('unrealized_gains', 28, 8)->default(0);
            $table->decimal('realized_gains', 28, 8)->default(0);
            $table->date('purchase_date');
            $table->date('maturity_date')->nullable();
            $table->enum('status', ['active', 'matured', 'liquidated'])->default('active');
            $table->timestamps();
            
            $table->foreign('user_id')->references('id')->on('users')->onDelete('cascade');
            $table->foreign('product_id')->references('id')->on('investment_products')->onDelete('cascade');
            $table->index(['user_id', 'status']);
            $table->index(['product_id', 'status']);
        });

        // Investment Transactions Table
        Schema::create('investment_transactions', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('user_id');
            $table->unsignedBigInteger('investment_id')->nullable();
            $table->unsignedBigInteger('product_id');
            $table->enum('type', ['buy', 'sell', 'dividend', 'interest', 'fee']);
            $table->decimal('amount', 28, 8);
            $table->decimal('units', 16, 8)->nullable();
            $table->decimal('unit_price', 28, 8)->nullable();
            $table->decimal('fees', 28, 8)->default(0);
            $table->string('description')->nullable();
            $table->string('trx', 40)->unique();
            $table->timestamps();
            
            $table->foreign('user_id')->references('id')->on('users')->onDelete('cascade');
            $table->foreign('investment_id')->references('id')->on('user_investments')->onDelete('cascade');
            $table->foreign('product_id')->references('id')->on('investment_products')->onDelete('cascade');
            $table->index(['user_id', 'type']);
            $table->index('trx');
        });

        // Investment Portfolio Summary Table (for quick access)
        Schema::create('investment_portfolios', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('user_id');
            $table->decimal('total_invested', 28, 8)->default(0);
            $table->decimal('current_value', 28, 8)->default(0);
            $table->decimal('total_returns', 28, 8)->default(0);
            $table->decimal('unrealized_gains', 28, 8)->default(0);
            $table->decimal('realized_gains', 28, 8)->default(0);
            $table->decimal('dividends_earned', 28, 8)->default(0);
            $table->decimal('fees_paid', 28, 8)->default(0);
            $table->decimal('return_percentage', 8, 4)->default(0);
            $table->integer('active_investments')->default(0);
            $table->date('last_updated')->nullable();
            $table->timestamps();
            
            $table->foreign('user_id')->references('id')->on('users')->onDelete('cascade');
            $table->unique('user_id');
        });

        // Market Data Table (for stocks and real-time pricing)
        Schema::create('market_data', function (Blueprint $table) {
            $table->id();
            $table->string('symbol')->unique(); // Stock symbol or product code
            $table->string('name');
            $table->enum('type', ['stock', 'bond', 'fund', 'commodity']);
            $table->decimal('current_price', 28, 8);
            $table->decimal('previous_close', 28, 8)->nullable();
            $table->decimal('day_change', 28, 8)->default(0);
            $table->decimal('day_change_percent', 8, 4)->default(0);
            $table->decimal('volume', 20, 0)->default(0);
            $table->decimal('market_cap', 28, 8)->nullable();
            $table->decimal('pe_ratio', 8, 4)->nullable();
            $table->decimal('dividend_yield', 8, 4)->nullable();
            $table->decimal('week_52_high', 28, 8)->nullable();
            $table->decimal('week_52_low', 28, 8)->nullable();
            $table->timestamp('last_updated')->nullable();
            $table->timestamps();
            
            $table->index(['type', 'symbol']);
        });

        // Investment Performance History Table
        Schema::create('investment_performance_history', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('product_id');
            $table->date('date');
            $table->decimal('unit_price', 28, 8);
            $table->decimal('nav', 28, 8)->nullable(); // Net Asset Value for funds
            $table->decimal('daily_return', 8, 4)->default(0);
            $table->decimal('volume', 20, 0)->default(0);
            $table->timestamps();
            
            $table->foreign('product_id')->references('id')->on('investment_products')->onDelete('cascade');
            $table->unique(['product_id', 'date']);
            $table->index(['product_id', 'date']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('investment_performance_history');
        Schema::dropIfExists('market_data');
        Schema::dropIfExists('investment_portfolios');
        Schema::dropIfExists('investment_transactions');
        Schema::dropIfExists('user_investments');
        Schema::dropIfExists('investment_products');
    }
};
