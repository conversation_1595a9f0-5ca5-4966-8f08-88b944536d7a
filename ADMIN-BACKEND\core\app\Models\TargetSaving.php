<?php

namespace App\Models;

use App\Traits\Searchable;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Carbon\Carbon;

class TargetSaving extends Model
{
    use Searchable;

    protected $fillable = [
        'user_id',
        'title',
        'target_amount',
        'saved_amount',
        'interest_rate',
        'target_date',
        'auto_debit_amount',
        'auto_debit_frequency',
        'next_debit_date',
        'status'
    ];

    protected $casts = [
        'target_amount' => 'decimal:8',
        'saved_amount' => 'decimal:8',
        'interest_rate' => 'decimal:2',
        'auto_debit_amount' => 'decimal:8',
        'target_date' => 'date',
        'next_debit_date' => 'date'
    ];

    protected $appends = [
        'progress_percentage',
        'days_remaining',
        'is_completed',
        'monthly_target',
        'projected_completion_date',
        'total_with_interest'
    ];

    // Relationships
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    public function transactions(): HasMany
    {
        return $this->hasMany(SavingsTransaction::class, 'savings_id')
                    ->where('savings_type', 'target_saving');
    }

    // Accessors
    public function getProgressPercentageAttribute(): float
    {
        if ($this->target_amount <= 0) return 0;
        return min(100, ($this->saved_amount / $this->target_amount) * 100);
    }

    public function getDaysRemainingAttribute(): int
    {
        if ($this->status === 'completed') return 0;
        return max(0, Carbon::now()->diffInDays($this->target_date, false));
    }

    public function getIsCompletedAttribute(): bool
    {
        return $this->saved_amount >= $this->target_amount;
    }

    public function getMonthlyTargetAttribute(): float
    {
        $monthsRemaining = max(1, Carbon::now()->diffInMonths($this->target_date, false));
        $remainingAmount = $this->target_amount - $this->saved_amount;
        return max(0, $remainingAmount / $monthsRemaining);
    }

    public function getProjectedCompletionDateAttribute(): ?Carbon
    {
        if ($this->auto_debit_amount <= 0) return null;
        
        $remainingAmount = $this->target_amount - $this->saved_amount;
        if ($remainingAmount <= 0) return Carbon::now();

        $periodsNeeded = ceil($remainingAmount / $this->auto_debit_amount);
        
        return match($this->auto_debit_frequency) {
            'daily' => Carbon::now()->addDays($periodsNeeded),
            'weekly' => Carbon::now()->addWeeks($periodsNeeded),
            'monthly' => Carbon::now()->addMonths($periodsNeeded),
            default => null
        };
    }

    public function getTotalWithInterestAttribute(): float
    {
        $daysElapsed = Carbon::parse($this->created_at)->diffInDays(Carbon::now());
        $dailyRate = $this->interest_rate / 100 / 365;
        $interest = $this->saved_amount * $dailyRate * $daysElapsed;
        return $this->saved_amount + $interest;
    }

    // Scopes
    public function scopeActive($query)
    {
        return $query->where('status', 'active');
    }

    public function scopeCompleted($query)
    {
        return $query->where('status', 'completed');
    }

    public function scopePaused($query)
    {
        return $query->where('status', 'paused');
    }

    public function scopeForUser($query, $userId)
    {
        return $query->where('user_id', $userId);
    }

    public function scopeDueForAutoDebit($query)
    {
        return $query->where('status', 'active')
                    ->whereNotNull('auto_debit_amount')
                    ->where('auto_debit_amount', '>', 0)
                    ->where('next_debit_date', '<=', Carbon::now()->toDateString());
    }

    public function scopeNearingTarget($query, $percentage = 90)
    {
        return $query->where('status', 'active')
                    ->whereRaw('(saved_amount / target_amount) * 100 >= ?', [$percentage]);
    }

    // Methods
    public function addContribution(float $amount, string $description = 'Manual contribution'): bool
    {
        if ($this->status !== 'active') return false;

        $this->saved_amount += $amount;
        
        // Check if target is reached
        if ($this->saved_amount >= $this->target_amount) {
            $this->status = 'completed';
            $this->saved_amount = $this->target_amount; // Cap at target
        }

        $this->save();

        // Create transaction record
        SavingsTransaction::create([
            'user_id' => $this->user_id,
            'savings_type' => 'target_saving',
            'savings_id' => $this->id,
            'type' => 'deposit',
            'amount' => $amount,
            'balance_before' => $this->saved_amount - $amount,
            'balance_after' => $this->saved_amount,
            'description' => $description,
            'trx' => getTrx()
        ]);

        return true;
    }

    public function processAutoDebit(): array
    {
        if ($this->status !== 'active' || !$this->auto_debit_amount || $this->auto_debit_amount <= 0) {
            return ['success' => false, 'message' => 'Auto-debit not configured'];
        }

        $user = $this->user;
        if ($user->balance < $this->auto_debit_amount) {
            return ['success' => false, 'message' => 'Insufficient balance'];
        }

        // Deduct from user balance
        $user->balance -= $this->auto_debit_amount;
        $user->save();

        // Add to savings
        $this->addContribution($this->auto_debit_amount, 'Auto-debit contribution');

        // Schedule next debit
        $this->scheduleNextDebit();

        return [
            'success' => true,
            'amount' => $this->auto_debit_amount,
            'new_balance' => $this->saved_amount
        ];
    }

    public function scheduleNextDebit(): void
    {
        if (!$this->auto_debit_frequency || $this->status !== 'active') return;

        $nextDate = match($this->auto_debit_frequency) {
            'daily' => Carbon::now()->addDay(),
            'weekly' => Carbon::now()->addWeek(),
            'monthly' => Carbon::now()->addMonth(),
            default => null
        };

        if ($nextDate && $nextDate->lte($this->target_date)) {
            $this->next_debit_date = $nextDate->toDateString();
            $this->save();
        }
    }

    public function pauseAutoDebit(): void
    {
        $this->next_debit_date = null;
        $this->save();
    }

    public function resumeAutoDebit(): void
    {
        if ($this->auto_debit_frequency && $this->status === 'active') {
            $this->scheduleNextDebit();
        }
    }

    public function withdraw(float $amount, string $reason = 'Manual withdrawal'): array
    {
        if ($this->status !== 'active') {
            return ['success' => false, 'message' => 'Savings plan is not active'];
        }

        if ($amount > $this->saved_amount) {
            return ['success' => false, 'message' => 'Insufficient savings balance'];
        }

        $this->saved_amount -= $amount;
        $this->save();

        // Create transaction record
        SavingsTransaction::create([
            'user_id' => $this->user_id,
            'savings_type' => 'target_saving',
            'savings_id' => $this->id,
            'type' => 'withdrawal',
            'amount' => $amount,
            'balance_before' => $this->saved_amount + $amount,
            'balance_after' => $this->saved_amount,
            'description' => $reason,
            'trx' => getTrx()
        ]);

        return [
            'success' => true,
            'amount' => $amount,
            'new_balance' => $this->saved_amount
        ];
    }

    public function calculateInterest(): float
    {
        $daysElapsed = Carbon::parse($this->created_at)->diffInDays(Carbon::now());
        $dailyRate = $this->interest_rate / 100 / 365;
        return $this->saved_amount * $dailyRate * $daysElapsed;
    }

    public function applyInterest(): void
    {
        if ($this->status !== 'active') return;

        $interest = $this->calculateInterest();
        if ($interest > 0) {
            $this->saved_amount += $interest;
            
            // Check if target is reached with interest
            if ($this->saved_amount >= $this->target_amount) {
                $this->status = 'completed';
                $this->saved_amount = $this->target_amount;
            }
            
            $this->save();

            // Create interest transaction
            SavingsTransaction::create([
                'user_id' => $this->user_id,
                'savings_type' => 'target_saving',
                'savings_id' => $this->id,
                'type' => 'interest',
                'amount' => $interest,
                'balance_before' => $this->saved_amount - $interest,
                'balance_after' => $this->saved_amount,
                'description' => 'Interest earned',
                'trx' => getTrx()
            ]);
        }
    }

    public static function getDefaultInterestRate(): float
    {
        return 12.0; // 12% annual interest rate
    }

    public static function getMinimumAmount(): float
    {
        return 1000; // ₦1,000 minimum
    }

    public static function getMaximumAmount(): float
    {
        return 50000000; // ₦50,000,000 maximum
    }
}
