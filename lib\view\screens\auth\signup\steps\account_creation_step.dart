import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:lottie/lottie.dart';
import 'package:viserpay/core/utils/dimensions.dart';
import 'package:viserpay/core/utils/my_color.dart';
import 'package:viserpay/core/utils/style.dart';
import 'package:viserpay/view/screens/auth/signup/signup_controller.dart';

class AccountCreationStep extends StatefulWidget {
  final SignupController controller;

  const AccountCreationStep({
    super.key,
    required this.controller,
  });

  @override
  State<AccountCreationStep> createState() => _AccountCreationStepState();
}

class _AccountCreationStepState extends State<AccountCreationStep>
    with TickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;
  late Animation<Offset> _slideAnimation;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 1500),
      vsync: this,
    );

    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: const Interval(0.0, 0.6, curve: Curves.easeOut),
    ));

    _slideAnimation = Tween<Offset>(
      begin: const Offset(0, 0.3),
      end: Offset.zero,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: const Interval(0.3, 1.0, curve: Curves.easeOut),
    ));

    _animationController.forward();

    // Auto-create account after animation
    Future.delayed(const Duration(seconds: 2), () {
      if (mounted) {
        widget.controller.nextStep();
      }
    });
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Obx(() {
      if (widget.controller.isLoading.value) {
        return _buildLoadingState();
      } else {
        return _buildSuccessState();
      }
    });
  }

  Widget _buildLoadingState() {
    return Column(
      children: [
        // Loading Animation
        SizedBox(
          height: 200,
          child: Lottie.asset(
            'assets/animation/time.json', // Loading animation
            repeat: true,
            reverse: false,
            animate: true,
          ),
        ),
        
        const SizedBox(height: Dimensions.space30),
        
        // Loading Text
        FadeTransition(
          opacity: _fadeAnimation,
          child: Column(
            children: [
              Text(
                'Creating Your Account',
                style: boldExtraLarge.copyWith(
                  color: MyColor.primaryTextColor,
                ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: Dimensions.space15),
              Text(
                'Please wait while we set up your ViserPay account...',
                style: regularDefault.copyWith(
                  color: MyColor.contentTextColor,
                ),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
        
        const SizedBox(height: Dimensions.space40),
        
        // Progress Steps
        SlideTransition(
          position: _slideAnimation,
          child: _buildProgressSteps(),
        ),
        
        const SizedBox(height: Dimensions.space40),
        
        // Account Summary
        _buildAccountSummary(),
      ],
    );
  }

  Widget _buildSuccessState() {
    return Column(
      children: [
        // Success Animation
        SizedBox(
          height: 200,
          child: Lottie.asset(
            'assets/animation/email.json', // Success animation
            repeat: false,
            reverse: false,
            animate: true,
          ),
        ),
        
        const SizedBox(height: Dimensions.space30),
        
        // Success Message
        FadeTransition(
          opacity: _fadeAnimation,
          child: Column(
            children: [
              Text(
                'Account Created Successfully!',
                style: boldExtraLarge.copyWith(
                  color: MyColor.colorGreen,
                ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: Dimensions.space15),
              Text(
                'Welcome to ViserPay! Your account has been created and is ready to use.',
                style: regularDefault.copyWith(
                  color: MyColor.contentTextColor,
                ),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
        
        const SizedBox(height: Dimensions.space40),
        
        // Account Details
        _buildAccountDetails(),
        
        const SizedBox(height: Dimensions.space30),
        
        // Next Steps
        _buildNextSteps(),
      ],
    );
  }

  Widget _buildProgressSteps() {
    final steps = [
      'Validating information',
      'Creating secure wallet',
      'Setting up account',
      'Finalizing setup',
    ];

    return Container(
      padding: const EdgeInsets.all(Dimensions.space20),
      decoration: BoxDecoration(
        color: MyColor.brandWhite,
        borderRadius: BorderRadius.circular(Dimensions.largeRadius),
        boxShadow: [
          BoxShadow(
            offset: const Offset(0, 4),
            blurRadius: 12,
            color: MyColor.brandBlack.withOpacity(0.1),
          ),
        ],
      ),
      child: Column(
        children: steps.asMap().entries.map((entry) {
          final index = entry.key;
          final step = entry.value;
          final isCompleted = index < 2; // Simulate progress
          final isCurrent = index == 2;
          
          return Padding(
            padding: EdgeInsets.only(
              bottom: index < steps.length - 1 ? Dimensions.space15 : 0,
            ),
            child: Row(
              children: [
                Container(
                  width: 24,
                  height: 24,
                  decoration: BoxDecoration(
                    color: isCompleted
                        ? MyColor.colorGreen
                        : isCurrent
                            ? MyColor.primaryColor
                            : MyColor.borderColor,
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Icon(
                    isCompleted
                        ? Icons.check
                        : isCurrent
                            ? Icons.more_horiz
                            : Icons.radio_button_unchecked,
                    color: MyColor.brandWhite,
                    size: 16,
                  ),
                ),
                const SizedBox(width: Dimensions.space15),
                Expanded(
                  child: Text(
                    step,
                    style: regularDefault.copyWith(
                      color: isCompleted || isCurrent
                          ? MyColor.primaryTextColor
                          : MyColor.contentTextColor,
                      fontWeight: isCurrent ? FontWeight.w600 : FontWeight.w400,
                    ),
                  ),
                ),
                if (isCurrent)
                  SizedBox(
                    width: 16,
                    height: 16,
                    child: CircularProgressIndicator(
                      strokeWidth: 2,
                      valueColor: AlwaysStoppedAnimation<Color>(MyColor.primaryColor),
                    ),
                  ),
              ],
            ),
          );
        }).toList(),
      ),
    );
  }

  Widget _buildAccountSummary() {
    return Container(
      padding: const EdgeInsets.all(Dimensions.space20),
      decoration: BoxDecoration(
        color: MyColor.primaryColor.withOpacity(0.05),
        borderRadius: BorderRadius.circular(Dimensions.largeRadius),
        border: Border.all(
          color: MyColor.primaryColor.withOpacity(0.2),
          width: 1,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Account Summary',
            style: semiBoldLarge.copyWith(
              color: MyColor.primaryColor,
            ),
          ),
          const SizedBox(height: Dimensions.space15),
          _buildSummaryItem('Name', '${widget.controller.firstNameController.text} ${widget.controller.lastNameController.text}'),
          _buildSummaryItem('Phone', '+234 ${widget.controller.phoneController.text}'),
          _buildSummaryItem('Email', widget.controller.emailController.text),
          _buildSummaryItem('Account Type', 'Personal Account'),
        ],
      ),
    );
  }

  Widget _buildAccountDetails() {
    return Container(
      padding: const EdgeInsets.all(Dimensions.space20),
      decoration: BoxDecoration(
        color: MyColor.colorGreen.withOpacity(0.05),
        borderRadius: BorderRadius.circular(Dimensions.largeRadius),
        border: Border.all(
          color: MyColor.colorGreen.withOpacity(0.2),
          width: 1,
        ),
      ),
      child: Column(
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(Dimensions.space10),
                decoration: BoxDecoration(
                  color: MyColor.colorGreen.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(Dimensions.mediumRadius),
                ),
                child: Icon(
                  Icons.account_balance_wallet_outlined,
                  color: MyColor.colorGreen,
                  size: 24,
                ),
              ),
              const SizedBox(width: Dimensions.space15),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Your ViserPay Wallet',
                      style: semiBoldLarge.copyWith(
                        color: MyColor.colorGreen,
                      ),
                    ),
                    Text(
                      'Account Number: VP${DateTime.now().millisecondsSinceEpoch.toString().substring(7)}',
                      style: regularDefault.copyWith(
                        color: MyColor.contentTextColor,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
          const SizedBox(height: Dimensions.space15),
          Container(
            padding: const EdgeInsets.all(Dimensions.space15),
            decoration: BoxDecoration(
              color: MyColor.brandWhite,
              borderRadius: BorderRadius.circular(Dimensions.mediumRadius),
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'Current Balance',
                  style: regularDefault.copyWith(
                    color: MyColor.contentTextColor,
                  ),
                ),
                Text(
                  '₦0.00',
                  style: boldLarge.copyWith(
                    color: MyColor.primaryTextColor,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildNextSteps() {
    final nextSteps = [
      'Add money to your wallet',
      'Complete your profile',
      'Explore ViserPay features',
      'Invite friends and earn rewards',
    ];

    return Container(
      padding: const EdgeInsets.all(Dimensions.space20),
      decoration: BoxDecoration(
        color: MyColor.brandWhite,
        borderRadius: BorderRadius.circular(Dimensions.largeRadius),
        boxShadow: [
          BoxShadow(
            offset: const Offset(0, 4),
            blurRadius: 12,
            color: MyColor.brandBlack.withOpacity(0.1),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'What\'s Next?',
            style: semiBoldLarge.copyWith(
              color: MyColor.primaryTextColor,
            ),
          ),
          const SizedBox(height: Dimensions.space15),
          ...nextSteps.map((step) => Padding(
            padding: const EdgeInsets.only(bottom: Dimensions.space10),
            child: Row(
              children: [
                Container(
                  width: 6,
                  height: 6,
                  decoration: BoxDecoration(
                    color: MyColor.primaryColor,
                    borderRadius: BorderRadius.circular(3),
                  ),
                ),
                const SizedBox(width: Dimensions.space10),
                Expanded(
                  child: Text(
                    step,
                    style: regularDefault.copyWith(
                      color: MyColor.contentTextColor,
                    ),
                  ),
                ),
              ],
            ),
          )),
        ],
      ),
    );
  }

  Widget _buildSummaryItem(String label, String value) {
    return Padding(
      padding: const EdgeInsets.only(bottom: Dimensions.space10),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 80,
            child: Text(
              '$label:',
              style: regularDefault.copyWith(
                color: MyColor.contentTextColor,
              ),
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: mediumDefault.copyWith(
                color: MyColor.primaryTextColor,
              ),
            ),
          ),
        ],
      ),
    );
  }
}
