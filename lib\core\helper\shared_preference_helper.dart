class SharedPreferenceHelper {
  static const String fingerprintStatus = 'fingerprintEnabled';
  static const String appOpeningStatus = 'app_opening_status';
  static const String passcode = 'passcode';
  static const String balance = 'blance';

  static const String accessTokenKey = 'access_token';
  static const String accessTokenType = 'access_type';
  static const String resetPassTokenKey = 'reset_pass_token';
  static const String userEmailKey = 'user_email';
  static const String userNameKey = 'user_name';
  static const String userPhoneNumberKey = 'user_phone_number';
  static const String rememberMeKey = 'remember me';
  static const String generalSettingKey = 'general-setting-key';
  static const String moduleSettingKey = 'module-setting-key';
  static const String fcmDeviceKey = 'device-key';
  static const String isPinSet = 'isPinSet';
  static const String quickAmount = 'quick-amount';
  static const String needTwoFactorVerification = 'need-tfa';

  static const String userIdKey = 'user_id';
  static const String hasNewNotificationKey = 'new-notification-key';

  static const String theme = 'theme';
  static const String token = 'token';

  static const String countryCode = 'country_code';
  static const String languageCode = 'language_code';
  static const String languageKey = 'language-key';
  static const String languageListKey = 'language-list-key';

  static const String firstTimeKey = 'first-time-key';
  static const String contactKey = 'contact-key';

  static const String sendMoneyRecentKey = 'send-money-recent-key';
  static const String rechargeRecentKey = 'recharge-recent-key';
  static const String cashoutRecentKey = 'cash-out-recent-key';
  static const String mPaymentRecentKey = 'make-payment-recent-key';
  static const String bankTransferRecentKey = 'bank-transfer-recent-key';

  static const String screenLockPin = 'screen-lock-pin';
}
