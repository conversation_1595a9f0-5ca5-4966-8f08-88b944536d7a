<?php

namespace App\Jobs;

use App\Models\FixedDeposit;
use App\Models\TargetSaving;
use App\Models\FlexSaving;
use App\Models\UserInvestment;
use App\Models\SavingsTransaction;
use App\Models\InvestmentTransaction;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;
use Carbon\Carbon;

class CalculateDailyInterest implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    public $timeout = 300; // 5 minutes timeout

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        Log::info('Starting daily interest calculation job');

        try {
            $this->calculateFixedDepositInterest();
            $this->calculateTargetSavingsInterest();
            $this->calculateFlexSavingsInterest();
            $this->processFixedDepositMaturity();
            $this->updateInvestmentValues();

            Log::info('Daily interest calculation completed successfully');
        } catch (\Exception $e) {
            Log::error('Daily interest calculation failed: ' . $e->getMessage());
            throw $e;
        }
    }

    /**
     * Calculate interest for fixed deposits
     */
    private function calculateFixedDepositInterest(): void
    {
        $fixedDeposits = FixedDeposit::active()->get();
        $processedCount = 0;

        foreach ($fixedDeposits as $deposit) {
            try {
                $deposit->updateAccruedInterest();
                $processedCount++;
            } catch (\Exception $e) {
                Log::error("Failed to calculate interest for fixed deposit {$deposit->id}: " . $e->getMessage());
            }
        }

        Log::info("Processed interest for {$processedCount} fixed deposits");
    }

    /**
     * Calculate interest for target savings
     */
    private function calculateTargetSavingsInterest(): void
    {
        $targetSavings = TargetSaving::active()->where('saved_amount', '>', 0)->get();
        $processedCount = 0;

        foreach ($targetSavings as $saving) {
            try {
                $saving->applyInterest();
                $processedCount++;
            } catch (\Exception $e) {
                Log::error("Failed to calculate interest for target saving {$saving->id}: " . $e->getMessage());
            }
        }

        Log::info("Processed interest for {$processedCount} target savings");
    }

    /**
     * Calculate interest for flex savings
     */
    private function calculateFlexSavingsInterest(): void
    {
        $flexSavings = FlexSaving::dueForInterestCalculation()->get();
        $processedCount = 0;

        foreach ($flexSavings as $saving) {
            try {
                $result = $saving->applyDailyInterest();
                if ($result['success']) {
                    $processedCount++;
                }
            } catch (\Exception $e) {
                Log::error("Failed to calculate interest for flex saving {$saving->id}: " . $e->getMessage());
            }
        }

        Log::info("Processed interest for {$processedCount} flex savings accounts");
    }

    /**
     * Process fixed deposit maturity
     */
    private function processFixedDepositMaturity(): void
    {
        $maturedDeposits = FixedDeposit::dueForMaturity()->get();
        $processedCount = 0;

        foreach ($maturedDeposits as $deposit) {
            try {
                $result = $deposit->mature();
                
                if ($result['success']) {
                    // Update user balance
                    $user = $deposit->user;
                    $user->balance += $result['total_amount'];
                    $user->savings_balance -= $deposit->amount;
                    $user->save();

                    // Handle auto-renewal
                    if ($deposit->auto_renewal) {
                        $newDeposit = $deposit->renew();
                        Log::info("Auto-renewed fixed deposit {$deposit->id} as {$newDeposit->id}");
                    }

                    $processedCount++;
                    Log::info("Matured fixed deposit {$deposit->id} for user {$deposit->user_id}");
                }
            } catch (\Exception $e) {
                Log::error("Failed to process maturity for fixed deposit {$deposit->id}: " . $e->getMessage());
            }
        }

        Log::info("Processed maturity for {$processedCount} fixed deposits");
    }

    /**
     * Update investment values (simplified)
     */
    private function updateInvestmentValues(): void
    {
        $investments = UserInvestment::active()->get();
        $processedCount = 0;

        foreach ($investments as $investment) {
            try {
                // Simulate price changes (in real implementation, this would fetch real market data)
                $priceChange = $this->simulatePriceChange($investment->product->type);
                $newUnitPrice = $investment->unit_price * (1 + $priceChange);
                
                $investment->updateCurrentValue($newUnitPrice);
                $processedCount++;
            } catch (\Exception $e) {
                Log::error("Failed to update investment value for {$investment->id}: " . $e->getMessage());
            }
        }

        Log::info("Updated values for {$processedCount} investments");
    }

    /**
     * Simulate price changes for different investment types
     */
    private function simulatePriceChange(string $productType): float
    {
        // Simulate realistic daily price changes
        return match($productType) {
            'treasury_bills' => mt_rand(-5, 15) / 10000, // -0.05% to +0.15%
            'corporate_bonds' => mt_rand(-10, 20) / 10000, // -0.1% to +0.2%
            'mutual_funds' => mt_rand(-50, 80) / 10000, // -0.5% to +0.8%
            'stocks' => mt_rand(-300, 400) / 10000, // -3% to +4%
            'real_estate' => mt_rand(-20, 30) / 10000, // -0.2% to +0.3%
            default => 0
        };
    }

    /**
     * Handle job failure
     */
    public function failed(\Throwable $exception): void
    {
        Log::error('Daily interest calculation job failed: ' . $exception->getMessage());
        
        // Could send notification to admin here
        // NotificationService::notifyAdmin('Daily Interest Calculation Failed', $exception->getMessage());
    }
}
