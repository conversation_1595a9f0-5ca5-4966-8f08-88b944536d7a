class CardResponseModel {
  String? status;
  String? message;
  CardData? data;

  CardResponseModel({this.status, this.message, this.data});

  CardResponseModel.fromJson(Map<String, dynamic> json) {
    status = json['status'];
    message = json['message'];
    data = json['data'] != null ? CardData.fromJson(json['data']) : null;
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['status'] = status;
    data['message'] = message;
    if (this.data != null) {
      data['data'] = this.data!.toJson();
    }
    return data;
  }
}

class CardData {
  List<UserCard>? personalCards;
  List<UserCard>? businessCards;
  List<CardRequest>? cardRequests;
  String? currentBalance;

  CardData({
    this.personalCards,
    this.businessCards,
    this.cardRequests,
    this.currentBalance,
  });

  CardData.fromJson(Map<String, dynamic> json) {
    if (json['personal_cards'] != null) {
      personalCards = <UserCard>[];
      json['personal_cards'].forEach((v) {
        personalCards!.add(UserCard.fromJson(v));
      });
    }
    if (json['business_cards'] != null) {
      businessCards = <UserCard>[];
      json['business_cards'].forEach((v) {
        businessCards!.add(UserCard.fromJson(v));
      });
    }
    if (json['card_requests'] != null) {
      cardRequests = <CardRequest>[];
      json['card_requests'].forEach((v) {
        cardRequests!.add(CardRequest.fromJson(v));
      });
    }
    currentBalance = json['current_balance']?.toString();
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    if (personalCards != null) {
      data['personal_cards'] = personalCards!.map((v) => v.toJson()).toList();
    }
    if (businessCards != null) {
      data['business_cards'] = businessCards!.map((v) => v.toJson()).toList();
    }
    if (cardRequests != null) {
      data['card_requests'] = cardRequests!.map((v) => v.toJson()).toList();
    }
    data['current_balance'] = currentBalance;
    return data;
  }
}

class UserCard {
  String? id;
  String? userId;
  String? cardType; // physical, virtual
  String? accountType; // personal, business
  String? cardName;
  String? cardNumber;
  String? maskedCardNumber;
  String? expiryDate;
  String? cvv;
  String? status; // active, blocked, expired, pending
  bool? pinSet;
  bool? pinLocked;
  int? failedAttempts;
  double? dailyLimit;
  double? monthlyLimit;
  double? availableBalance;
  CardSecuritySettings? securitySettings;
  String? createdAt;
  String? updatedAt;
  String? activatedAt;
  String? deliveryAddress;
  String? deliveryStatus;
  String? trackingNumber;

  UserCard({
    this.id,
    this.userId,
    this.cardType,
    this.accountType,
    this.cardName,
    this.cardNumber,
    this.maskedCardNumber,
    this.expiryDate,
    this.cvv,
    this.status,
    this.pinSet,
    this.pinLocked,
    this.failedAttempts,
    this.dailyLimit,
    this.monthlyLimit,
    this.availableBalance,
    this.securitySettings,
    this.createdAt,
    this.updatedAt,
    this.activatedAt,
    this.deliveryAddress,
    this.deliveryStatus,
    this.trackingNumber,
  });

  UserCard.fromJson(Map<String, dynamic> json) {
    id = json['id']?.toString();
    userId = json['user_id']?.toString();
    cardType = json['card_type'];
    accountType = json['account_type'];
    cardName = json['card_name'];
    cardNumber = json['card_number'];
    maskedCardNumber = json['masked_card_number'];
    expiryDate = json['expiry_date'];
    cvv = json['cvv'];
    status = json['status'];
    pinSet = json['pin_set'];
    pinLocked = json['pin_locked'];
    failedAttempts = json['failed_attempts'];
    dailyLimit = double.tryParse(json['daily_limit']?.toString() ?? '0');
    monthlyLimit = double.tryParse(json['monthly_limit']?.toString() ?? '0');
    availableBalance = double.tryParse(json['available_balance']?.toString() ?? '0');
    securitySettings = json['security_settings'] != null
        ? CardSecuritySettings.fromJson(json['security_settings'])
        : null;
    createdAt = json['created_at'];
    updatedAt = json['updated_at'];
    activatedAt = json['activated_at'];
    deliveryAddress = json['delivery_address'];
    deliveryStatus = json['delivery_status'];
    trackingNumber = json['tracking_number'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['id'] = id;
    data['user_id'] = userId;
    data['card_type'] = cardType;
    data['account_type'] = accountType;
    data['card_name'] = cardName;
    data['card_number'] = cardNumber;
    data['masked_card_number'] = maskedCardNumber;
    data['expiry_date'] = expiryDate;
    data['cvv'] = cvv;
    data['status'] = status;
    data['pin_set'] = pinSet;
    data['pin_locked'] = pinLocked;
    data['failed_attempts'] = failedAttempts;
    data['daily_limit'] = dailyLimit;
    data['monthly_limit'] = monthlyLimit;
    data['available_balance'] = availableBalance;
    if (securitySettings != null) {
      data['security_settings'] = securitySettings!.toJson();
    }
    data['created_at'] = createdAt;
    data['updated_at'] = updatedAt;
    data['activated_at'] = activatedAt;
    data['delivery_address'] = deliveryAddress;
    data['delivery_status'] = deliveryStatus;
    data['tracking_number'] = trackingNumber;
    return data;
  }
}

class CardSecuritySettings {
  bool? onlineTransactionsEnabled;
  bool? internationalTransactionsEnabled;
  bool? atmWithdrawalsEnabled;
  bool? contactlessEnabled;
  bool? magneticStripeEnabled;
  List<String>? allowedMerchantCategories;
  List<String>? blockedMerchantCategories;
  double? singleTransactionLimit;
  int? dailyTransactionCount;

  CardSecuritySettings({
    this.onlineTransactionsEnabled,
    this.internationalTransactionsEnabled,
    this.atmWithdrawalsEnabled,
    this.contactlessEnabled,
    this.magneticStripeEnabled,
    this.allowedMerchantCategories,
    this.blockedMerchantCategories,
    this.singleTransactionLimit,
    this.dailyTransactionCount,
  });

  CardSecuritySettings.fromJson(Map<String, dynamic> json) {
    onlineTransactionsEnabled = json['online_transactions_enabled'];
    internationalTransactionsEnabled = json['international_transactions_enabled'];
    atmWithdrawalsEnabled = json['atm_withdrawals_enabled'];
    contactlessEnabled = json['contactless_enabled'];
    magneticStripeEnabled = json['magnetic_stripe_enabled'];
    allowedMerchantCategories = json['allowed_merchant_categories']?.cast<String>();
    blockedMerchantCategories = json['blocked_merchant_categories']?.cast<String>();
    singleTransactionLimit = double.tryParse(json['single_transaction_limit']?.toString() ?? '0');
    dailyTransactionCount = json['daily_transaction_count'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['online_transactions_enabled'] = onlineTransactionsEnabled;
    data['international_transactions_enabled'] = internationalTransactionsEnabled;
    data['atm_withdrawals_enabled'] = atmWithdrawalsEnabled;
    data['contactless_enabled'] = contactlessEnabled;
    data['magnetic_stripe_enabled'] = magneticStripeEnabled;
    data['allowed_merchant_categories'] = allowedMerchantCategories;
    data['blocked_merchant_categories'] = blockedMerchantCategories;
    data['single_transaction_limit'] = singleTransactionLimit;
    data['daily_transaction_count'] = dailyTransactionCount;
    return data;
  }
}

class CardRequest {
  String? id;
  String? userId;
  String? cardType; // physical, virtual
  String? accountType; // personal, business
  String? cardCategory; // debit, prepaid
  String? cardName;
  String? deliveryAddress;
  String? deliveryState;
  String? deliveryCity;
  String? phoneNumber;
  double? spendingLimit;
  String? status; // pending, approved, rejected, processing, delivered
  String? rejectionReason;
  String? trackingNumber;
  String? estimatedDelivery;
  String? requestedAt;
  String? processedAt;
  String? deliveredAt;

  CardRequest({
    this.id,
    this.userId,
    this.cardType,
    this.accountType,
    this.cardCategory,
    this.cardName,
    this.deliveryAddress,
    this.deliveryState,
    this.deliveryCity,
    this.phoneNumber,
    this.spendingLimit,
    this.status,
    this.rejectionReason,
    this.trackingNumber,
    this.estimatedDelivery,
    this.requestedAt,
    this.processedAt,
    this.deliveredAt,
  });

  CardRequest.fromJson(Map<String, dynamic> json) {
    id = json['id']?.toString();
    userId = json['user_id']?.toString();
    cardType = json['card_type'];
    accountType = json['account_type'];
    cardCategory = json['card_category'];
    cardName = json['card_name'];
    deliveryAddress = json['delivery_address'];
    deliveryState = json['delivery_state'];
    deliveryCity = json['delivery_city'];
    phoneNumber = json['phone_number'];
    spendingLimit = double.tryParse(json['spending_limit']?.toString() ?? '0');
    status = json['status'];
    rejectionReason = json['rejection_reason'];
    trackingNumber = json['tracking_number'];
    estimatedDelivery = json['estimated_delivery'];
    requestedAt = json['requested_at'];
    processedAt = json['processed_at'];
    deliveredAt = json['delivered_at'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['id'] = id;
    data['user_id'] = userId;
    data['card_type'] = cardType;
    data['account_type'] = accountType;
    data['card_category'] = cardCategory;
    data['card_name'] = cardName;
    data['delivery_address'] = deliveryAddress;
    data['delivery_state'] = deliveryState;
    data['delivery_city'] = deliveryCity;
    data['phone_number'] = phoneNumber;
    data['spending_limit'] = spendingLimit;
    data['status'] = status;
    data['rejection_reason'] = rejectionReason;
    data['tracking_number'] = trackingNumber;
    data['estimated_delivery'] = estimatedDelivery;
    data['requested_at'] = requestedAt;
    data['processed_at'] = processedAt;
    data['delivered_at'] = deliveredAt;
    return data;
  }
}

class CardTransaction {
  String? id;
  String? cardId;
  String? transactionType; // purchase, withdrawal, refund, fee
  String? merchantName;
  String? merchantCategory;
  double? amount;
  double? fee;
  String? currency;
  String? status; // successful, failed, pending, reversed
  String? authorizationCode;
  String? responseCode;
  String? responseMessage;
  String? location;
  String? terminalId;
  String? transactionDate;
  String? createdAt;

  CardTransaction({
    this.id,
    this.cardId,
    this.transactionType,
    this.merchantName,
    this.merchantCategory,
    this.amount,
    this.fee,
    this.currency,
    this.status,
    this.authorizationCode,
    this.responseCode,
    this.responseMessage,
    this.location,
    this.terminalId,
    this.transactionDate,
    this.createdAt,
  });

  CardTransaction.fromJson(Map<String, dynamic> json) {
    id = json['id']?.toString();
    cardId = json['card_id']?.toString();
    transactionType = json['transaction_type'];
    merchantName = json['merchant_name'];
    merchantCategory = json['merchant_category'];
    amount = double.tryParse(json['amount']?.toString() ?? '0');
    fee = double.tryParse(json['fee']?.toString() ?? '0');
    currency = json['currency'];
    status = json['status'];
    authorizationCode = json['authorization_code'];
    responseCode = json['response_code'];
    responseMessage = json['response_message'];
    location = json['location'];
    terminalId = json['terminal_id'];
    transactionDate = json['transaction_date'];
    createdAt = json['created_at'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['id'] = id;
    data['card_id'] = cardId;
    data['transaction_type'] = transactionType;
    data['merchant_name'] = merchantName;
    data['merchant_category'] = merchantCategory;
    data['amount'] = amount;
    data['fee'] = fee;
    data['currency'] = currency;
    data['status'] = status;
    data['authorization_code'] = authorizationCode;
    data['response_code'] = responseCode;
    data['response_message'] = responseMessage;
    data['location'] = location;
    data['terminal_id'] = terminalId;
    data['transaction_date'] = transactionDate;
    data['created_at'] = createdAt;
    return data;
  }
}
