<?php

/*
 * This file is part of the Symfony package.
 *
 * (c) <PERSON><PERSON><PERSON> <<EMAIL>>
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

namespace Symfony\Component\Validator\Mapping\Loader;

/**
 * Loads validation metadata from a list of XML files.
 *
 * <AUTHOR> <<EMAIL>>
 * <AUTHOR> <<EMAIL>>
 *
 * @see FilesLoader
 */
class XmlFilesLoader extends FilesLoader
{
    public function getFileLoaderInstance(string $file): LoaderInterface
    {
        return new XmlFileLoader($file);
    }
}
