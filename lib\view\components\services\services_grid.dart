import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:viserpay/core/utils/dimensions.dart';
import 'package:viserpay/core/utils/my_color.dart';
import 'package:viserpay/core/utils/style.dart';

class ServicesGrid extends StatelessWidget {
  final bool isBusinessAccount;

  const ServicesGrid({
    super.key,
    this.isBusinessAccount = false,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.all(Dimensions.space15),
      padding: const EdgeInsets.all(Dimensions.space20),
      decoration: BoxDecoration(
        color: MyColor.colorWhite,
        borderRadius: BorderRadius.circular(Dimensions.mediumRadius),
        boxShadow: [
          BoxShadow(
            color: MyColor.colorGrey.withOpacity(0.1),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Services',
            style: semiBoldLarge.copyWith(
              color: MyColor.colorBlack,
              fontSize: 18,
            ),
          ),
          const SizedBox(height: Dimensions.space20),
          
          // Main Services Grid
          GridView.count(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            crossAxisCount: 4,
            crossAxisSpacing: Dimensions.space15,
            mainAxisSpacing: Dimensions.space15,
            childAspectRatio: 0.9,
            children: _getMainServices(),
          ),

          if (isBusinessAccount) ...[
            const SizedBox(height: Dimensions.space25),
            Text(
              'Business Services',
              style: semiBoldDefault.copyWith(
                color: MyColor.colorGrey,
                fontSize: 14,
              ),
            ),
            const SizedBox(height: Dimensions.space15),
            
            // Business Services Grid
            GridView.count(
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              crossAxisCount: 4,
              crossAxisSpacing: Dimensions.space15,
              mainAxisSpacing: Dimensions.space15,
              childAspectRatio: 0.9,
              children: _getBusinessServices(),
            ),
          ],

          const SizedBox(height: Dimensions.space25),
          Text(
            'More Services',
            style: semiBoldDefault.copyWith(
              color: MyColor.colorGrey,
              fontSize: 14,
            ),
          ),
          const SizedBox(height: Dimensions.space15),
          
          // Additional Services Grid
          GridView.count(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            crossAxisCount: 4,
            crossAxisSpacing: Dimensions.space15,
            mainAxisSpacing: Dimensions.space15,
            childAspectRatio: 0.9,
            children: _getAdditionalServices(),
          ),
        ],
      ),
    );
  }

  List<Widget> _getMainServices() {
    final services = [
      ServiceItem(
        icon: Icons.send,
        label: 'Transfer',
        color: MyColor.primaryColor,
        onTap: () => Get.toNamed('/transfer'),
      ),
      ServiceItem(
        icon: Icons.phone,
        label: 'Airtime',
        color: MyColor.colorGreen,
        onTap: () => Get.toNamed('/airtime'),
      ),
      ServiceItem(
        icon: Icons.wifi,
        label: 'Data',
        color: MyColor.colorOrange,
        onTap: () => Get.toNamed('/data'),
      ),
      ServiceItem(
        icon: Icons.flash_on,
        label: 'Electricity',
        color: MyColor.colorYellow,
        onTap: () => Get.toNamed('/electricity'),
      ),
      ServiceItem(
        icon: Icons.tv,
        label: 'Cable TV',
        color: MyColor.colorPurple,
        onTap: () => Get.toNamed('/cable-tv'),
      ),
      ServiceItem(
        icon: Icons.account_balance,
        label: 'Withdraw',
        color: MyColor.colorRed,
        onTap: () => Get.toNamed('/withdraw'),
      ),
      ServiceItem(
        icon: Icons.qr_code_scanner,
        label: 'Scan & Pay',
        color: MyColor.colorCyan,
        onTap: () => Get.toNamed('/scan-pay'),
      ),
      ServiceItem(
        icon: Icons.receipt,
        label: 'Bills',
        color: MyColor.colorTeal,
        onTap: () => Get.toNamed('/bills'),
      ),
    ];

    return services;
  }

  List<Widget> _getBusinessServices() {
    final services = [
      ServiceItem(
        icon: Icons.store,
        label: 'My Store',
        color: MyColor.colorCyan,
        onTap: () => Get.toNamed('/my-store'),
      ),
      ServiceItem(
        icon: Icons.inventory,
        label: 'Inventory',
        color: MyColor.colorOrange,
        onTap: () => Get.toNamed('/inventory'),
      ),
      ServiceItem(
        icon: Icons.analytics,
        label: 'Analytics',
        color: MyColor.colorGreen,
        onTap: () => Get.toNamed('/analytics'),
      ),
      ServiceItem(
        icon: Icons.security,
        label: 'Escrow',
        color: MyColor.colorPurple,
        onTap: () => Get.toNamed('/escrow'),
      ),
    ];

    return services;
  }

  List<Widget> _getAdditionalServices() {
    final services = [
      ServiceItem(
        icon: Icons.sports_soccer,
        label: 'Betting',
        color: MyColor.colorGreen,
        onTap: () => Get.toNamed('/betting'),
      ),
      ServiceItem(
        icon: Icons.school,
        label: 'Education',
        color: MyColor.colorBlue,
        onTap: () => Get.toNamed('/education'),
      ),
      ServiceItem(
        icon: Icons.local_taxi,
        label: 'Transport',
        color: MyColor.colorYellow,
        onTap: () => Get.toNamed('/transport'),
      ),
      ServiceItem(
        icon: Icons.shopping_cart,
        label: 'Shopping',
        color: MyColor.colorPink,
        onTap: () => Get.toNamed('/shopping'),
      ),
      ServiceItem(
        icon: Icons.people,
        label: 'Referrals',
        color: MyColor.colorCyan,
        onTap: () => Get.toNamed('/referrals'),
      ),
      ServiceItem(
        icon: Icons.savings,
        label: 'Savings',
        color: MyColor.colorGreen,
        onTap: () => Get.toNamed('/savings'),
      ),
      ServiceItem(
        icon: Icons.trending_up,
        label: 'Invest',
        color: MyColor.colorOrange,
        onTap: () => Get.toNamed('/invest'),
      ),
      ServiceItem(
        icon: Icons.more_horiz,
        label: 'More',
        color: MyColor.colorGrey,
        onTap: () => Get.toNamed('/more-services'),
      ),
    ];

    return services;
  }
}

class ServiceItem extends StatefulWidget {
  final IconData icon;
  final String label;
  final Color color;
  final VoidCallback onTap;
  final bool isNew;
  final bool isComingSoon;

  const ServiceItem({
    super.key,
    required this.icon,
    required this.label,
    required this.color,
    required this.onTap,
    this.isNew = false,
    this.isComingSoon = false,
  });

  @override
  State<ServiceItem> createState() => _ServiceItemState();
}

class _ServiceItemState extends State<ServiceItem>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _scaleAnimation;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 150),
      vsync: this,
    );
    _scaleAnimation = Tween<double>(
      begin: 1.0,
      end: 0.95,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  void _onTapDown(TapDownDetails details) {
    _animationController.forward();
  }

  void _onTapUp(TapUpDetails details) {
    _animationController.reverse();
  }

  void _onTapCancel() {
    _animationController.reverse();
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: widget.isComingSoon ? null : widget.onTap,
      onTapDown: widget.isComingSoon ? null : _onTapDown,
      onTapUp: widget.isComingSoon ? null : _onTapUp,
      onTapCancel: widget.isComingSoon ? null : _onTapCancel,
      child: AnimatedBuilder(
        animation: _scaleAnimation,
        builder: (context, child) {
          return Transform.scale(
            scale: _scaleAnimation.value,
            child: Stack(
              children: [
                Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Container(
                      width: 50,
                      height: 50,
                      decoration: BoxDecoration(
                        color: widget.isComingSoon 
                            ? MyColor.colorGrey.withOpacity(0.3)
                            : widget.color.withOpacity(0.1),
                        borderRadius: BorderRadius.circular(Dimensions.mediumRadius),
                      ),
                      child: Icon(
                        widget.icon,
                        color: widget.isComingSoon 
                            ? MyColor.colorGrey
                            : widget.color,
                        size: 24,
                      ),
                    ),
                    const SizedBox(height: Dimensions.space8),
                    Text(
                      widget.label,
                      style: regularSmall.copyWith(
                        color: widget.isComingSoon 
                            ? MyColor.colorGrey
                            : MyColor.colorBlack,
                        fontSize: 11,
                      ),
                      textAlign: TextAlign.center,
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ],
                ),
                
                // New Badge
                if (widget.isNew)
                  Positioned(
                    top: 0,
                    right: 0,
                    child: Container(
                      padding: const EdgeInsets.symmetric(
                        horizontal: 6,
                        vertical: 2,
                      ),
                      decoration: BoxDecoration(
                        color: MyColor.colorRed,
                        borderRadius: BorderRadius.circular(10),
                      ),
                      child: Text(
                        'NEW',
                        style: regularSmall.copyWith(
                          color: MyColor.colorWhite,
                          fontSize: 8,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                  ),
                
                // Coming Soon Badge
                if (widget.isComingSoon)
                  Positioned(
                    top: 0,
                    right: 0,
                    child: Container(
                      padding: const EdgeInsets.symmetric(
                        horizontal: 4,
                        vertical: 2,
                      ),
                      decoration: BoxDecoration(
                        color: MyColor.colorOrange,
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: Text(
                        'SOON',
                        style: regularSmall.copyWith(
                          color: MyColor.colorWhite,
                          fontSize: 7,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                  ),
              ],
            ),
          );
        },
      ),
    );
  }
}
