# Vonage PHP Complete Package

<img src="https://developer.nexmo.com/assets/images/Vonage_Nexmo.svg" height="48px" alt="Nexmo is now known as Vonage" />

This package is a wrapper for our PHP library, which you can find here: <https://github.com/vonage/vonage-php-sdk-core>

This package exists to separate the Vonage functionality from the HTTP Client. If you can't install this package due to a conflict with the `guzzle6-adapter` package, you can instead install:

* The main package `vonage/client-core`
* Any HTTP client that satisfies `php-http/client-implementation` (see <https://packagist.org/providers/php-http/client-implementation> for options).

## Documentation

For everything you need, visit the Vonage PHP library project: <https://github.com/vonage/vonage-php-sdk-core>
