<?php

namespace App\Models;

use App\Traits\Searchable;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Carbon\Carbon;

class UserInvestment extends Model
{
    use Searchable;

    protected $fillable = [
        'user_id',
        'product_id',
        'amount',
        'units',
        'unit_price',
        'current_value',
        'total_returns',
        'unrealized_gains',
        'realized_gains',
        'purchase_date',
        'maturity_date',
        'status'
    ];

    protected $casts = [
        'amount' => 'decimal:8',
        'units' => 'decimal:8',
        'unit_price' => 'decimal:8',
        'current_value' => 'decimal:8',
        'total_returns' => 'decimal:8',
        'unrealized_gains' => 'decimal:8',
        'realized_gains' => 'decimal:8',
        'purchase_date' => 'date',
        'maturity_date' => 'date'
    ];

    protected $appends = [
        'return_percentage',
        'days_held',
        'is_matured',
        'can_liquidate',
        'annualized_return',
        'profit_loss',
        'performance_status'
    ];

    // Relationships
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    public function product(): BelongsTo
    {
        return $this->belongsTo(InvestmentProduct::class, 'product_id');
    }

    public function transactions(): HasMany
    {
        return $this->hasMany(InvestmentTransaction::class, 'investment_id');
    }

    // Accessors
    public function getReturnPercentageAttribute(): float
    {
        if ($this->amount <= 0) return 0;
        return (($this->current_value - $this->amount) / $this->amount) * 100;
    }

    public function getDaysHeldAttribute(): int
    {
        return Carbon::parse($this->purchase_date)->diffInDays(Carbon::now());
    }

    public function getIsMaturedAttribute(): bool
    {
        return $this->maturity_date && Carbon::now()->gte($this->maturity_date);
    }

    public function getCanLiquidateAttribute(): bool
    {
        return $this->status === 'active' && 
               ($this->product->type !== 'treasury_bills' || $this->is_matured);
    }

    public function getAnnualizedReturnAttribute(): float
    {
        if ($this->days_held <= 0) return 0;
        
        $dailyReturn = $this->return_percentage / $this->days_held;
        return $dailyReturn * 365;
    }

    public function getProfitLossAttribute(): float
    {
        return $this->current_value - $this->amount;
    }

    public function getPerformanceStatusAttribute(): string
    {
        $returnPercentage = $this->return_percentage;
        
        if ($returnPercentage > 10) return 'excellent';
        if ($returnPercentage > 5) return 'good';
        if ($returnPercentage > 0) return 'positive';
        if ($returnPercentage > -5) return 'slight_loss';
        return 'loss';
    }

    // Scopes
    public function scopeActive($query)
    {
        return $query->where('status', 'active');
    }

    public function scopeMatured($query)
    {
        return $query->where('status', 'matured');
    }

    public function scopeLiquidated($query)
    {
        return $query->where('status', 'liquidated');
    }

    public function scopeForUser($query, $userId)
    {
        return $query->where('user_id', $userId);
    }

    public function scopeByProduct($query, $productId)
    {
        return $query->where('product_id', $productId);
    }

    public function scopeByProductType($query, $type)
    {
        return $query->whereHas('product', function($q) use ($type) {
            $q->where('type', $type);
        });
    }

    public function scopeProfitable($query)
    {
        return $query->whereRaw('current_value > amount');
    }

    public function scopeAtLoss($query)
    {
        return $query->whereRaw('current_value < amount');
    }

    public function scopeDueForMaturity($query)
    {
        return $query->where('status', 'active')
                    ->whereNotNull('maturity_date')
                    ->where('maturity_date', '<=', Carbon::now()->toDateString());
    }

    // Methods
    public function updateCurrentValue($newUnitPrice = null): void
    {
        if ($this->status !== 'active') return;

        $unitPrice = $newUnitPrice ?? $this->product->getCurrentUnitPrice();
        $this->current_value = $this->units * $unitPrice;
        $this->unrealized_gains = $this->current_value - $this->amount;
        $this->total_returns = $this->unrealized_gains + $this->realized_gains;
        
        $this->save();
    }

    public function liquidate($unitPrice = null, $reason = 'User liquidation'): array
    {
        if (!$this->can_liquidate) {
            return [
                'success' => false,
                'message' => 'Investment cannot be liquidated at this time'
            ];
        }

        $liquidationPrice = $unitPrice ?? $this->product->getCurrentUnitPrice();
        $liquidationValue = $this->units * $liquidationPrice;
        
        // Calculate fees (if any)
        $managementFee = $liquidationValue * ($this->product->management_fee / 100);
        $netAmount = $liquidationValue - $managementFee;
        
        // Update investment status
        $this->current_value = $liquidationValue;
        $this->realized_gains = $liquidationValue - $this->amount;
        $this->total_returns = $this->realized_gains;
        $this->status = 'liquidated';
        $this->save();

        // Create liquidation transaction
        InvestmentTransaction::create([
            'user_id' => $this->user_id,
            'investment_id' => $this->id,
            'product_id' => $this->product_id,
            'type' => 'sell',
            'amount' => $liquidationValue,
            'units' => $this->units,
            'unit_price' => $liquidationPrice,
            'fees' => $managementFee,
            'description' => $reason,
            'trx' => getTrx()
        ]);

        // Update user balance
        $this->user->investment_balance -= $this->amount;
        $this->user->balance += $netAmount;
        $this->user->save();

        return [
            'success' => true,
            'liquidation_value' => $liquidationValue,
            'management_fee' => $managementFee,
            'net_amount' => $netAmount,
            'profit_loss' => $this->realized_gains
        ];
    }

    public function mature(): array
    {
        if ($this->status !== 'active') {
            return [
                'success' => false,
                'message' => 'Investment is not active'
            ];
        }

        $maturityValue = $this->current_value;
        $this->realized_gains = $maturityValue - $this->amount;
        $this->total_returns = $this->realized_gains;
        $this->status = 'matured';
        $this->save();

        // Create maturity transaction
        InvestmentTransaction::create([
            'user_id' => $this->user_id,
            'investment_id' => $this->id,
            'product_id' => $this->product_id,
            'type' => 'dividend',
            'amount' => $this->realized_gains,
            'description' => 'Investment matured',
            'trx' => getTrx()
        ]);

        // Update user balance
        $this->user->investment_balance -= $this->amount;
        $this->user->balance += $maturityValue;
        $this->user->save();

        return [
            'success' => true,
            'maturity_value' => $maturityValue,
            'principal' => $this->amount,
            'returns' => $this->realized_gains
        ];
    }

    public function processDividend($dividendAmount, $description = 'Dividend payment'): void
    {
        if ($this->status !== 'active') return;

        $this->realized_gains += $dividendAmount;
        $this->total_returns = $this->unrealized_gains + $this->realized_gains;
        $this->save();

        // Create dividend transaction
        InvestmentTransaction::create([
            'user_id' => $this->user_id,
            'investment_id' => $this->id,
            'product_id' => $this->product_id,
            'type' => 'dividend',
            'amount' => $dividendAmount,
            'description' => $description,
            'trx' => getTrx()
        ]);

        // Add to user balance
        $this->user->balance += $dividendAmount;
        $this->user->save();
    }

    public function getPerformanceHistory($days = 30): array
    {
        $startDate = Carbon::now()->subDays($days);
        
        $performance = $this->product->performanceHistory()
                           ->where('date', '>=', $startDate)
                           ->orderBy('date')
                           ->get();

        $history = [];
        foreach ($performance as $record) {
            $value = $this->units * $record->unit_price;
            $return = (($value - $this->amount) / $this->amount) * 100;
            
            $history[] = [
                'date' => $record->date,
                'unit_price' => $record->unit_price,
                'investment_value' => $value,
                'return_percentage' => round($return, 2),
                'daily_return' => $record->daily_return
            ];
        }

        return $history;
    }

    public function getRiskMetrics(): array
    {
        $performance = $this->getPerformanceHistory(365);
        
        if (empty($performance)) {
            return [
                'volatility' => 0,
                'max_drawdown' => 0,
                'sharpe_ratio' => 0,
                'beta' => 1
            ];
        }

        $returns = array_column($performance, 'daily_return');
        $values = array_column($performance, 'investment_value');
        
        // Calculate volatility (standard deviation of returns)
        $meanReturn = array_sum($returns) / count($returns);
        $variance = array_sum(array_map(function($x) use ($meanReturn) {
            return pow($x - $meanReturn, 2);
        }, $returns)) / count($returns);
        $volatility = sqrt($variance) * sqrt(252); // Annualized

        // Calculate maximum drawdown
        $peak = $values[0];
        $maxDrawdown = 0;
        
        foreach ($values as $value) {
            if ($value > $peak) {
                $peak = $value;
            }
            $drawdown = (($peak - $value) / $peak) * 100;
            $maxDrawdown = max($maxDrawdown, $drawdown);
        }

        // Simple Sharpe ratio (assuming risk-free rate of 10%)
        $riskFreeRate = 10; // 10% annual
        $excessReturn = $this->annualized_return - $riskFreeRate;
        $sharpeRatio = $volatility > 0 ? $excessReturn / $volatility : 0;

        return [
            'volatility' => round($volatility, 2),
            'max_drawdown' => round($maxDrawdown, 2),
            'sharpe_ratio' => round($sharpeRatio, 2),
            'beta' => 1.0 // Simplified - would need market data for actual calculation
        ];
    }

    public static function createInvestment($userId, $productId, $amount): array
    {
        $product = InvestmentProduct::find($productId);
        if (!$product || !$product->is_active) {
            return ['success' => false, 'message' => 'Investment product not available'];
        }

        $eligibility = $product->canUserInvest($userId, $amount);
        if (!$eligibility['can_invest']) {
            return ['success' => false, 'message' => $eligibility['reason']];
        }

        $user = User::find($userId);
        $unitPrice = $product->getCurrentUnitPrice();
        $units = $amount / $unitPrice;

        // Create investment
        $investment = self::create([
            'user_id' => $userId,
            'product_id' => $productId,
            'amount' => $amount,
            'units' => $units,
            'unit_price' => $unitPrice,
            'current_value' => $amount,
            'purchase_date' => Carbon::now()->toDateString(),
            'maturity_date' => $product->duration_days ? 
                              Carbon::now()->addDays($product->duration_days)->toDateString() : null,
            'status' => 'active'
        ]);

        // Update user balances
        $user->balance -= $amount;
        $user->investment_balance += $amount;
        $user->save();

        // Create purchase transaction
        InvestmentTransaction::create([
            'user_id' => $userId,
            'investment_id' => $investment->id,
            'product_id' => $productId,
            'type' => 'buy',
            'amount' => $amount,
            'units' => $units,
            'unit_price' => $unitPrice,
            'description' => "Purchased {$product->name}",
            'trx' => getTrx()
        ]);

        return [
            'success' => true,
            'investment' => $investment,
            'units_purchased' => $units,
            'unit_price' => $unitPrice
        ];
    }
}
