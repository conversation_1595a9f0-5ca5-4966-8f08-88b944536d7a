<?php

namespace App\Models;

use App\Traits\Searchable;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Carbon\Carbon;

class GroupSaving extends Model
{
    use Searchable;

    protected $fillable = [
        'creator_id',
        'title',
        'description',
        'contribution_amount',
        'frequency',
        'duration_cycles',
        'max_members',
        'current_members',
        'interest_rate',
        'status',
        'start_date',
        'next_payout_date'
    ];

    protected $casts = [
        'contribution_amount' => 'decimal:8',
        'interest_rate' => 'decimal:2',
        'start_date' => 'date',
        'next_payout_date' => 'date'
    ];

    protected $appends = [
        'total_pool_amount',
        'cycles_completed',
        'cycles_remaining',
        'is_full',
        'can_join',
        'next_recipient',
        'estimated_completion_date'
    ];

    // Relationships
    public function creator(): BelongsTo
    {
        return $this->belongsTo(User::class, 'creator_id');
    }

    public function members(): HasMany
    {
        return $this->hasMany(GroupSavingMember::class, 'group_id');
    }

    public function activeMembers(): HasMany
    {
        return $this->hasMany(GroupSavingMember::class, 'group_id')
                    ->where('status', 'active');
    }

    public function transactions(): HasMany
    {
        return $this->hasMany(SavingsTransaction::class, 'savings_id')
                    ->where('savings_type', 'group_saving');
    }

    // Accessors
    public function getTotalPoolAmountAttribute(): float
    {
        return $this->contribution_amount * $this->current_members;
    }

    public function getCyclesCompletedAttribute(): int
    {
        return $this->members()->where('has_received_payout', true)->count();
    }

    public function getCyclesRemainingAttribute(): int
    {
        return max(0, $this->duration_cycles - $this->cycles_completed);
    }

    public function getIsFullAttribute(): bool
    {
        return $this->current_members >= $this->max_members;
    }

    public function getCanJoinAttribute(): bool
    {
        return $this->status === 'recruiting' && !$this->is_full;
    }

    public function getNextRecipientAttribute(): ?GroupSavingMember
    {
        return $this->members()
                   ->where('status', 'active')
                   ->where('has_received_payout', false)
                   ->orderBy('position')
                   ->first();
    }

    public function getEstimatedCompletionDateAttribute(): ?Carbon
    {
        if ($this->status !== 'active' || !$this->start_date) return null;

        $cyclesRemaining = $this->cycles_remaining;
        
        return match($this->frequency) {
            'daily' => $this->start_date->addDays($cyclesRemaining),
            'weekly' => $this->start_date->addWeeks($cyclesRemaining),
            'monthly' => $this->start_date->addMonths($cyclesRemaining),
            default => null
        };
    }

    // Scopes
    public function scopeRecruiting($query)
    {
        return $query->where('status', 'recruiting');
    }

    public function scopeActive($query)
    {
        return $query->where('status', 'active');
    }

    public function scopeCompleted($query)
    {
        return $query->where('status', 'completed');
    }

    public function scopeCancelled($query)
    {
        return $query->where('status', 'cancelled');
    }

    public function scopeByCreator($query, $creatorId)
    {
        return $query->where('creator_id', $creatorId);
    }

    public function scopeAvailableToJoin($query)
    {
        return $query->where('status', 'recruiting')
                    ->whereRaw('current_members < max_members');
    }

    public function scopeDueForPayout($query)
    {
        return $query->where('status', 'active')
                    ->where('next_payout_date', '<=', Carbon::now()->toDateString());
    }

    // Methods
    public function addMember($userId): array
    {
        if (!$this->can_join) {
            return ['success' => false, 'message' => 'Group is not available for joining'];
        }

        // Check if user is already a member
        if ($this->members()->where('user_id', $userId)->exists()) {
            return ['success' => false, 'message' => 'User is already a member'];
        }

        $position = $this->members()->max('position') + 1;

        $member = GroupSavingMember::create([
            'group_id' => $this->id,
            'user_id' => $userId,
            'position' => $position,
            'status' => 'active',
            'joined_at' => Carbon::now()
        ]);

        $this->current_members++;
        
        // Start the group if it's full
        if ($this->current_members >= $this->max_members) {
            $this->status = 'active';
            $this->start_date = Carbon::now()->toDateString();
            $this->scheduleNextPayout();
        }
        
        $this->save();

        return [
            'success' => true,
            'member' => $member,
            'position' => $position,
            'group_started' => $this->status === 'active'
        ];
    }

    public function removeMember($userId, $reason = 'Member left'): array
    {
        $member = $this->members()->where('user_id', $userId)->first();
        
        if (!$member) {
            return ['success' => false, 'message' => 'User is not a member'];
        }

        if ($this->status === 'active' && $member->has_received_payout) {
            return ['success' => false, 'message' => 'Cannot remove member who has received payout'];
        }

        $member->status = 'removed';
        $member->save();

        $this->current_members--;
        
        // Cancel group if too few members
        if ($this->current_members < 2) {
            $this->status = 'cancelled';
        }
        
        $this->save();

        return [
            'success' => true,
            'message' => 'Member removed successfully',
            'group_cancelled' => $this->status === 'cancelled'
        ];
    }

    public function processContribution($userId, $amount): array
    {
        if ($this->status !== 'active') {
            return ['success' => false, 'message' => 'Group is not active'];
        }

        $member = $this->members()->where('user_id', $userId)->where('status', 'active')->first();
        
        if (!$member) {
            return ['success' => false, 'message' => 'User is not an active member'];
        }

        if ($amount != $this->contribution_amount) {
            return ['success' => false, 'message' => 'Invalid contribution amount'];
        }

        $member->total_contributed += $amount;
        $member->save();

        // Create transaction record
        SavingsTransaction::create([
            'user_id' => $userId,
            'savings_type' => 'group_saving',
            'savings_id' => $this->id,
            'type' => 'deposit',
            'amount' => $amount,
            'balance_before' => $member->total_contributed - $amount,
            'balance_after' => $member->total_contributed,
            'description' => "Group savings contribution - {$this->title}",
            'trx' => getTrx()
        ]);

        return [
            'success' => true,
            'amount' => $amount,
            'total_contributed' => $member->total_contributed
        ];
    }

    public function processPayout(): array
    {
        if ($this->status !== 'active') {
            return ['success' => false, 'message' => 'Group is not active'];
        }

        $recipient = $this->next_recipient;
        if (!$recipient) {
            return ['success' => false, 'message' => 'No recipient found'];
        }

        // Calculate payout amount (total pool + interest)
        $baseAmount = $this->total_pool_amount;
        $interest = $baseAmount * ($this->interest_rate / 100);
        $totalPayout = $baseAmount + $interest;

        // Mark recipient as paid
        $recipient->has_received_payout = true;
        $recipient->payout_date = Carbon::now()->toDateString();
        $recipient->save();

        // Create payout transaction
        SavingsTransaction::create([
            'user_id' => $recipient->user_id,
            'savings_type' => 'group_saving',
            'savings_id' => $this->id,
            'type' => 'withdrawal',
            'amount' => $totalPayout,
            'balance_before' => $totalPayout,
            'balance_after' => 0,
            'description' => "Group savings payout - {$this->title}",
            'trx' => getTrx()
        ]);

        // Check if group is completed
        if ($this->cycles_completed >= $this->duration_cycles) {
            $this->status = 'completed';
        } else {
            $this->scheduleNextPayout();
        }
        
        $this->save();

        return [
            'success' => true,
            'recipient' => $recipient->user,
            'amount' => $totalPayout,
            'base_amount' => $baseAmount,
            'interest' => $interest,
            'group_completed' => $this->status === 'completed'
        ];
    }

    public function scheduleNextPayout(): void
    {
        if ($this->status !== 'active') return;

        $nextDate = match($this->frequency) {
            'daily' => Carbon::now()->addDay(),
            'weekly' => Carbon::now()->addWeek(),
            'monthly' => Carbon::now()->addMonth(),
            default => null
        };

        if ($nextDate) {
            $this->next_payout_date = $nextDate->toDateString();
            $this->save();
        }
    }

    public function cancel($reason = 'Group cancelled'): array
    {
        if ($this->status === 'completed') {
            return ['success' => false, 'message' => 'Cannot cancel completed group'];
        }

        $this->status = 'cancelled';
        $this->save();

        // Mark all members as inactive
        $this->members()->update(['status' => 'inactive']);

        return [
            'success' => true,
            'message' => 'Group cancelled successfully',
            'reason' => $reason
        ];
    }

    public function getContributionStatus(): array
    {
        $members = $this->activeMembers()->with('user')->get();
        $status = [];

        foreach ($members as $member) {
            $expectedContributions = $this->cycles_completed + 1;
            $actualContributions = $member->total_contributed / $this->contribution_amount;
            
            $status[] = [
                'user' => $member->user,
                'position' => $member->position,
                'total_contributed' => $member->total_contributed,
                'expected_contributions' => $expectedContributions,
                'actual_contributions' => $actualContributions,
                'is_up_to_date' => $actualContributions >= $expectedContributions,
                'has_received_payout' => $member->has_received_payout,
                'payout_date' => $member->payout_date
            ];
        }

        return $status;
    }

    public static function getDefaultInterestRate(): float
    {
        return 5.0; // 5% interest on group savings
    }

    public static function getMinimumContribution(): float
    {
        return 1000; // ₦1,000 minimum contribution
    }

    public static function getMaximumContribution(): float
    {
        return 1000000; // ₦1,000,000 maximum contribution
    }

    public static function getMinimumMembers(): int
    {
        return 2;
    }

    public static function getMaximumMembers(): int
    {
        return 50;
    }
}
