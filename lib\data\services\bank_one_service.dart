import 'dart:convert';
import 'package:http/http.dart' as http;
import 'package:viserpay/core/utils/my_strings.dart';

class BankOneService {
  static const String baseUrl = 'https://api.bankone.ng/v1';
  static const String sandboxUrl = 'https://sandbox.bankone.ng/v1';
  
  final String apiKey;
  final String secretKey;
  final bool isProduction;
  
  BankOneService({
    required this.apiKey,
    required this.secretKey,
    this.isProduction = false,
  });

  String get _baseUrl => isProduction ? baseUrl : sandboxUrl;

  Map<String, String> get _headers => {
    'Content-Type': 'application/json',
    'Authorization': 'Bearer $apiKey',
    'X-API-Key': apiKey,
  };

  /// Verify account number and bank code
  Future<BankOneResponse> verifyAccount({
    required String accountNumber,
    required String bankCode,
  }) async {
    try {
      final url = '$_baseUrl/account/verify';
      final body = {
        'account_number': accountNumber,
        'bank_code': bankCode,
      };

      final response = await http.post(
        Uri.parse(url),
        headers: _headers,
        body: jsonEncode(body),
      );

      return BankOneResponse.fromJson(jsonDecode(response.body));
    } catch (e) {
      return BankOneResponse(
        success: false,
        message: 'Account verification failed: ${e.toString()}',
      );
    }
  }

  /// Get list of Nigerian banks
  Future<BankOneResponse> getBanks() async {
    try {
      final url = '$_baseUrl/banks';
      
      final response = await http.get(
        Uri.parse(url),
        headers: _headers,
      );

      return BankOneResponse.fromJson(jsonDecode(response.body));
    } catch (e) {
      return BankOneResponse(
        success: false,
        message: 'Failed to fetch banks: ${e.toString()}',
      );
    }
  }

  /// Transfer funds to another bank account
  Future<BankOneResponse> transferFunds({
    required String recipientAccountNumber,
    required String recipientBankCode,
    required double amount,
    required String narration,
    required String reference,
    String? recipientName,
  }) async {
    try {
      final url = '$_baseUrl/transfer';
      final body = {
        'recipient_account_number': recipientAccountNumber,
        'recipient_bank_code': recipientBankCode,
        'amount': (amount * 100).toInt(), // Convert to kobo
        'narration': narration,
        'reference': reference,
        if (recipientName != null) 'recipient_name': recipientName,
      };

      final response = await http.post(
        Uri.parse(url),
        headers: _headers,
        body: jsonEncode(body),
      );

      return BankOneResponse.fromJson(jsonDecode(response.body));
    } catch (e) {
      return BankOneResponse(
        success: false,
        message: 'Transfer failed: ${e.toString()}',
      );
    }
  }

  /// Check transfer status
  Future<BankOneResponse> checkTransferStatus(String reference) async {
    try {
      final url = '$_baseUrl/transfer/status/$reference';
      
      final response = await http.get(
        Uri.parse(url),
        headers: _headers,
      );

      return BankOneResponse.fromJson(jsonDecode(response.body));
    } catch (e) {
      return BankOneResponse(
        success: false,
        message: 'Failed to check transfer status: ${e.toString()}',
      );
    }
  }

  /// Get account balance
  Future<BankOneResponse> getAccountBalance() async {
    try {
      final url = '$_baseUrl/account/balance';
      
      final response = await http.get(
        Uri.parse(url),
        headers: _headers,
      );

      return BankOneResponse.fromJson(jsonDecode(response.body));
    } catch (e) {
      return BankOneResponse(
        success: false,
        message: 'Failed to get account balance: ${e.toString()}',
      );
    }
  }

  /// Get transaction history
  Future<BankOneResponse> getTransactionHistory({
    int page = 1,
    int limit = 50,
    String? startDate,
    String? endDate,
  }) async {
    try {
      String url = '$_baseUrl/transactions?page=$page&limit=$limit';
      if (startDate != null) url += '&start_date=$startDate';
      if (endDate != null) url += '&end_date=$endDate';
      
      final response = await http.get(
        Uri.parse(url),
        headers: _headers,
      );

      return BankOneResponse.fromJson(jsonDecode(response.body));
    } catch (e) {
      return BankOneResponse(
        success: false,
        message: 'Failed to get transaction history: ${e.toString()}',
      );
    }
  }

  /// Bulk transfer to multiple accounts
  Future<BankOneResponse> bulkTransfer({
    required List<BulkTransferItem> transfers,
    required String batchReference,
  }) async {
    try {
      final url = '$_baseUrl/transfer/bulk';
      final body = {
        'batch_reference': batchReference,
        'transfers': transfers.map((transfer) => transfer.toJson()).toList(),
      };

      final response = await http.post(
        Uri.parse(url),
        headers: _headers,
        body: jsonEncode(body),
      );

      return BankOneResponse.fromJson(jsonDecode(response.body));
    } catch (e) {
      return BankOneResponse(
        success: false,
        message: 'Bulk transfer failed: ${e.toString()}',
      );
    }
  }

  /// Generate virtual account
  Future<BankOneResponse> generateVirtualAccount({
    required String customerName,
    required String customerEmail,
    required String customerPhone,
    String? bvn,
  }) async {
    try {
      final url = '$_baseUrl/virtual-account/create';
      final body = {
        'customer_name': customerName,
        'customer_email': customerEmail,
        'customer_phone': customerPhone,
        if (bvn != null) 'bvn': bvn,
      };

      final response = await http.post(
        Uri.parse(url),
        headers: _headers,
        body: jsonEncode(body),
      );

      return BankOneResponse.fromJson(jsonDecode(response.body));
    } catch (e) {
      return BankOneResponse(
        success: false,
        message: 'Failed to generate virtual account: ${e.toString()}',
      );
    }
  }

  /// Validate BVN
  Future<BankOneResponse> validateBVN({
    required String bvn,
    required String firstName,
    required String lastName,
    required String dateOfBirth,
  }) async {
    try {
      final url = '$_baseUrl/bvn/validate';
      final body = {
        'bvn': bvn,
        'first_name': firstName,
        'last_name': lastName,
        'date_of_birth': dateOfBirth,
      };

      final response = await http.post(
        Uri.parse(url),
        headers: _headers,
        body: jsonEncode(body),
      );

      return BankOneResponse.fromJson(jsonDecode(response.body));
    } catch (e) {
      return BankOneResponse(
        success: false,
        message: 'BVN validation failed: ${e.toString()}',
      );
    }
  }

  /// Get transaction fees
  Future<BankOneResponse> getTransactionFees({
    required double amount,
    required String transactionType,
  }) async {
    try {
      final url = '$_baseUrl/fees?amount=${amount.toInt()}&type=$transactionType';
      
      final response = await http.get(
        Uri.parse(url),
        headers: _headers,
      );

      return BankOneResponse.fromJson(jsonDecode(response.body));
    } catch (e) {
      return BankOneResponse(
        success: false,
        message: 'Failed to get transaction fees: ${e.toString()}',
      );
    }
  }
}

class BankOneResponse {
  final bool success;
  final String? message;
  final Map<String, dynamic>? data;
  final String? error;
  final int? statusCode;

  BankOneResponse({
    required this.success,
    this.message,
    this.data,
    this.error,
    this.statusCode,
  });

  factory BankOneResponse.fromJson(Map<String, dynamic> json) {
    return BankOneResponse(
      success: json['success'] ?? json['status'] == 'success',
      message: json['message'],
      data: json['data'],
      error: json['error'],
      statusCode: json['status_code'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'success': success,
      'message': message,
      'data': data,
      'error': error,
      'status_code': statusCode,
    };
  }
}

class BulkTransferItem {
  final String recipientAccountNumber;
  final String recipientBankCode;
  final double amount;
  final String narration;
  final String reference;
  final String? recipientName;

  BulkTransferItem({
    required this.recipientAccountNumber,
    required this.recipientBankCode,
    required this.amount,
    required this.narration,
    required this.reference,
    this.recipientName,
  });

  Map<String, dynamic> toJson() {
    return {
      'recipient_account_number': recipientAccountNumber,
      'recipient_bank_code': recipientBankCode,
      'amount': (amount * 100).toInt(), // Convert to kobo
      'narration': narration,
      'reference': reference,
      if (recipientName != null) 'recipient_name': recipientName,
    };
  }
}

class BankOneConfig {
  static const String productionApiKey = 'YOUR_PRODUCTION_API_KEY';
  static const String productionSecretKey = 'YOUR_PRODUCTION_SECRET_KEY';
  static const String sandboxApiKey = 'YOUR_SANDBOX_API_KEY';
  static const String sandboxSecretKey = 'YOUR_SANDBOX_SECRET_KEY';
  
  static BankOneService getService({bool isProduction = false}) {
    return BankOneService(
      apiKey: isProduction ? productionApiKey : sandboxApiKey,
      secretKey: isProduction ? productionSecretKey : sandboxSecretKey,
      isProduction: isProduction,
    );
  }
}

// Nigerian Bank Codes for BankOne API
class NigerianBankCodes {
  static const Map<String, String> codes = {
    'ACCESS_BANK': '044',
    'AFRIBANK': '014',
    'CITIBANK': '023',
    'DIAMOND_BANK': '063',
    'ECOBANK': '050',
    'ENTERPRISE_BANK': '084',
    'FIDELITY_BANK': '070',
    'FIRST_BANK': '011',
    'FCMB': '214',
    'GTB': '058',
    'HERITAGE_BANK': '030',
    'JAIZ_BANK': '301',
    'KEYSTONE_BANK': '082',
    'POLARIS_BANK': '076',
    'PROVIDUS_BANK': '101',
    'STANBIC_IBTC': '221',
    'STANDARD_CHARTERED': '068',
    'STERLING_BANK': '232',
    'UNION_BANK': '032',
    'UBA': '033',
    'UNITY_BANK': '215',
    'WEMA_BANK': '035',
    'ZENITH_BANK': '057',
    'SUNTRUST_BANK': '100',
    'KUDA_BANK': '50211',
    'OPAY': '999991',
    'PALMPAY': '999992',
    'MONIEPOINT': '100002',
  };

  static String getCode(String bankName) {
    return codes[bankName.toUpperCase().replaceAll(' ', '_')] ?? '';
  }

  static String getBankName(String code) {
    for (var entry in codes.entries) {
      if (entry.value == code) {
        return entry.key.replaceAll('_', ' ').toLowerCase()
            .split(' ')
            .map((word) => word[0].toUpperCase() + word.substring(1))
            .join(' ');
      }
    }
    return 'Unknown Bank';
  }

  static List<Map<String, String>> getAllBanks() {
    return codes.entries.map((entry) => {
      'name': entry.key.replaceAll('_', ' ').toLowerCase()
          .split(' ')
          .map((word) => word[0].toUpperCase() + word.substring(1))
          .join(' '),
      'code': entry.value,
    }).toList();
  }
}
