<?php

/**
 * This code was generated by
 * \ / _    _  _|   _  _
 * | (_)\/(_)(_|\/| |(/_  v1.0.0
 * /       /
 */

namespace Twilio\Rest\Api\V2010\Account;

use <PERSON><PERSON><PERSON>\Exceptions\TwilioException;
use <PERSON><PERSON><PERSON>\InstanceContext;
use <PERSON><PERSON><PERSON>\Options;
use Twilio\Serialize;
use Twilio\Values;
use Twilio\Version;

class ApplicationContext extends InstanceContext {
    /**
     * Initialize the ApplicationContext
     *
     * @param Version $version Version that contains the resource
     * @param string $accountSid The SID of the Account that created the resource
     *                           to fetch
     * @param string $sid The unique string that identifies the resource
     */
    public function __construct(Version $version, $accountSid, $sid) {
        parent::__construct($version);

        // Path Solution
        $this->solution = ['accountSid' => $accountSid, 'sid' => $sid, ];

        $this->uri = '/Accounts/' . \rawurlencode($accountSid) . '/Applications/' . \rawurlencode($sid) . '.json';
    }

    /**
     * Delete the ApplicationInstance
     *
     * @return bool True if delete succeeds, false otherwise
     * @throws TwilioException When an HTTP error occurs.
     */
    public function delete(): bool {
        return $this->version->delete('DELETE', $this->uri);
    }

    /**
     * Fetch the ApplicationInstance
     *
     * @return ApplicationInstance Fetched ApplicationInstance
     * @throws TwilioException When an HTTP error occurs.
     */
    public function fetch(): ApplicationInstance {
        $payload = $this->version->fetch('GET', $this->uri);

        return new ApplicationInstance(
            $this->version,
            $payload,
            $this->solution['accountSid'],
            $this->solution['sid']
        );
    }

    /**
     * Update the ApplicationInstance
     *
     * @param array|Options $options Optional Arguments
     * @return ApplicationInstance Updated ApplicationInstance
     * @throws TwilioException When an HTTP error occurs.
     */
    public function update(array $options = []): ApplicationInstance {
        $options = new Values($options);

        $data = Values::of([
            'FriendlyName' => $options['friendlyName'],
            'ApiVersion' => $options['apiVersion'],
            'VoiceUrl' => $options['voiceUrl'],
            'VoiceMethod' => $options['voiceMethod'],
            'VoiceFallbackUrl' => $options['voiceFallbackUrl'],
            'VoiceFallbackMethod' => $options['voiceFallbackMethod'],
            'StatusCallback' => $options['statusCallback'],
            'StatusCallbackMethod' => $options['statusCallbackMethod'],
            'VoiceCallerIdLookup' => Serialize::booleanToString($options['voiceCallerIdLookup']),
            'SmsUrl' => $options['smsUrl'],
            'SmsMethod' => $options['smsMethod'],
            'SmsFallbackUrl' => $options['smsFallbackUrl'],
            'SmsFallbackMethod' => $options['smsFallbackMethod'],
            'SmsStatusCallback' => $options['smsStatusCallback'],
            'MessageStatusCallback' => $options['messageStatusCallback'],
            'PublicApplicationConnectEnabled' => Serialize::booleanToString($options['publicApplicationConnectEnabled']),
        ]);

        $payload = $this->version->update('POST', $this->uri, [], $data);

        return new ApplicationInstance(
            $this->version,
            $payload,
            $this->solution['accountSid'],
            $this->solution['sid']
        );
    }

    /**
     * Provide a friendly representation
     *
     * @return string Machine friendly representation
     */
    public function __toString(): string {
        $context = [];
        foreach ($this->solution as $key => $value) {
            $context[] = "$key=$value";
        }
        return '[Twilio.Api.V2010.ApplicationContext ' . \implode(' ', $context) . ']';
    }
}