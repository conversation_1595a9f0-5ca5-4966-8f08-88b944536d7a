class InsightsResponseModel {
  String? status;
  Message? message;
  InsightsData? data;

  InsightsResponseModel({
    this.status,
    this.message,
    this.data,
  });

  factory InsightsResponseModel.fromJson(Map<String, dynamic> json) => InsightsResponseModel(
        status: json["status"],
        message: json["message"] == null ? null : Message.fromJson(json["message"]),
        data: json["data"] == null ? null : InsightsData.fromJson(json["data"]),
      );

  Map<String, dynamic> toJson() => {
        "status": status,
        "message": message?.toJson(),
        "data": data?.toJson(),
      };
}

class Message {
  List<String>? success;
  List<String>? error;

  Message({
    this.success,
    this.error,
  });

  factory Message.fromJson(Map<String, dynamic> json) => Message(
        success: json["success"] == null ? [] : List<String>.from(json["success"]!.map((x) => x)),
        error: json["error"] == null ? [] : List<String>.from(json["error"]!.map((x) => x)),
      );

  Map<String, dynamic> toJson() => {
        "success": success == null ? [] : List<dynamic>.from(success!.map((x) => x)),
        "error": error == null ? [] : List<dynamic>.from(error!.map((x) => x)),
      };
}

class InsightsData {
  FinancialOverview? financialOverview;
  SpendingAnalysis? spendingAnalysis;
  SavingsInsights? savingsInsights;
  IncomeAnalysis? incomeAnalysis;
  GoalsProgress? goalsProgress;
  List<PersonalizedTip>? personalizedTips;
  List<Recommendation>? recommendations;
  CreditScore? creditScore;
  List<ChartData>? chartData;
  ComparisonData? comparisonData;

  InsightsData({
    this.financialOverview,
    this.spendingAnalysis,
    this.savingsInsights,
    this.incomeAnalysis,
    this.goalsProgress,
    this.personalizedTips,
    this.recommendations,
    this.creditScore,
    this.chartData,
    this.comparisonData,
  });

  factory InsightsData.fromJson(Map<String, dynamic> json) => InsightsData(
        financialOverview: json["financial_overview"] == null ? null : FinancialOverview.fromJson(json["financial_overview"]),
        spendingAnalysis: json["spending_analysis"] == null ? null : SpendingAnalysis.fromJson(json["spending_analysis"]),
        savingsInsights: json["savings_insights"] == null ? null : SavingsInsights.fromJson(json["savings_insights"]),
        incomeAnalysis: json["income_analysis"] == null ? null : IncomeAnalysis.fromJson(json["income_analysis"]),
        goalsProgress: json["goals_progress"] == null ? null : GoalsProgress.fromJson(json["goals_progress"]),
        personalizedTips: json["personalized_tips"] == null ? [] : List<PersonalizedTip>.from(json["personalized_tips"]!.map((x) => PersonalizedTip.fromJson(x))),
        recommendations: json["recommendations"] == null ? [] : List<Recommendation>.from(json["recommendations"]!.map((x) => Recommendation.fromJson(x))),
        creditScore: json["credit_score"] == null ? null : CreditScore.fromJson(json["credit_score"]),
        chartData: json["chart_data"] == null ? [] : List<ChartData>.from(json["chart_data"]!.map((x) => ChartData.fromJson(x))),
        comparisonData: json["comparison_data"] == null ? null : ComparisonData.fromJson(json["comparison_data"]),
      );

  Map<String, dynamic> toJson() => {
        "financial_overview": financialOverview?.toJson(),
        "spending_analysis": spendingAnalysis?.toJson(),
        "savings_insights": savingsInsights?.toJson(),
        "income_analysis": incomeAnalysis?.toJson(),
        "goals_progress": goalsProgress?.toJson(),
        "personalized_tips": personalizedTips == null ? [] : List<dynamic>.from(personalizedTips!.map((x) => x.toJson())),
        "recommendations": recommendations == null ? [] : List<dynamic>.from(recommendations!.map((x) => x.toJson())),
        "credit_score": creditScore?.toJson(),
        "chart_data": chartData == null ? [] : List<dynamic>.from(chartData!.map((x) => x.toJson())),
        "comparison_data": comparisonData?.toJson(),
      };
}

class FinancialOverview {
  String? totalBalance;
  String? totalIncome;
  String? totalExpenses;
  String? netWorth;
  String? monthlyChange;
  String? changePercentage;
  String? cashFlow;
  String? savingsRate;
  String? debtToIncomeRatio;

  FinancialOverview({
    this.totalBalance,
    this.totalIncome,
    this.totalExpenses,
    this.netWorth,
    this.monthlyChange,
    this.changePercentage,
    this.cashFlow,
    this.savingsRate,
    this.debtToIncomeRatio,
  });

  factory FinancialOverview.fromJson(Map<String, dynamic> json) => FinancialOverview(
        totalBalance: json["total_balance"],
        totalIncome: json["total_income"],
        totalExpenses: json["total_expenses"],
        netWorth: json["net_worth"],
        monthlyChange: json["monthly_change"],
        changePercentage: json["change_percentage"],
        cashFlow: json["cash_flow"],
        savingsRate: json["savings_rate"],
        debtToIncomeRatio: json["debt_to_income_ratio"],
      );

  Map<String, dynamic> toJson() => {
        "total_balance": totalBalance,
        "total_income": totalIncome,
        "total_expenses": totalExpenses,
        "net_worth": netWorth,
        "monthly_change": monthlyChange,
        "change_percentage": changePercentage,
        "cash_flow": cashFlow,
        "savings_rate": savingsRate,
        "debt_to_income_ratio": debtToIncomeRatio,
      };
}

class SpendingAnalysis {
  String? totalSpent;
  String? averageDaily;
  String? averageMonthly;
  String? largestExpense;
  String? mostFrequentCategory;
  List<CategorySpending>? categoryBreakdown;
  List<MonthlySpending>? monthlyTrend;
  List<String>? topMerchants;
  String? spendingVelocity;

  SpendingAnalysis({
    this.totalSpent,
    this.averageDaily,
    this.averageMonthly,
    this.largestExpense,
    this.mostFrequentCategory,
    this.categoryBreakdown,
    this.monthlyTrend,
    this.topMerchants,
    this.spendingVelocity,
  });

  factory SpendingAnalysis.fromJson(Map<String, dynamic> json) => SpendingAnalysis(
        totalSpent: json["total_spent"],
        averageDaily: json["average_daily"],
        averageMonthly: json["average_monthly"],
        largestExpense: json["largest_expense"],
        mostFrequentCategory: json["most_frequent_category"],
        categoryBreakdown: json["category_breakdown"] == null ? [] : List<CategorySpending>.from(json["category_breakdown"]!.map((x) => CategorySpending.fromJson(x))),
        monthlyTrend: json["monthly_trend"] == null ? [] : List<MonthlySpending>.from(json["monthly_trend"]!.map((x) => MonthlySpending.fromJson(x))),
        topMerchants: json["top_merchants"] == null ? [] : List<String>.from(json["top_merchants"]!.map((x) => x)),
        spendingVelocity: json["spending_velocity"],
      );

  Map<String, dynamic> toJson() => {
        "total_spent": totalSpent,
        "average_daily": averageDaily,
        "average_monthly": averageMonthly,
        "largest_expense": largestExpense,
        "most_frequent_category": mostFrequentCategory,
        "category_breakdown": categoryBreakdown == null ? [] : List<dynamic>.from(categoryBreakdown!.map((x) => x.toJson())),
        "monthly_trend": monthlyTrend == null ? [] : List<dynamic>.from(monthlyTrend!.map((x) => x.toJson())),
        "top_merchants": topMerchants == null ? [] : List<dynamic>.from(topMerchants!.map((x) => x)),
        "spending_velocity": spendingVelocity,
      };
}

class CategorySpending {
  String? category;
  String? amount;
  String? percentage;
  int? transactionCount;
  String? averageAmount;
  String? trend; // 'up', 'down', 'stable'

  CategorySpending({
    this.category,
    this.amount,
    this.percentage,
    this.transactionCount,
    this.averageAmount,
    this.trend,
  });

  factory CategorySpending.fromJson(Map<String, dynamic> json) => CategorySpending(
        category: json["category"],
        amount: json["amount"],
        percentage: json["percentage"],
        transactionCount: json["transaction_count"],
        averageAmount: json["average_amount"],
        trend: json["trend"],
      );

  Map<String, dynamic> toJson() => {
        "category": category,
        "amount": amount,
        "percentage": percentage,
        "transaction_count": transactionCount,
        "average_amount": averageAmount,
        "trend": trend,
      };
}

class MonthlySpending {
  String? month;
  String? amount;
  int? transactionCount;
  String? changeFromPrevious;

  MonthlySpending({
    this.month,
    this.amount,
    this.transactionCount,
    this.changeFromPrevious,
  });

  factory MonthlySpending.fromJson(Map<String, dynamic> json) => MonthlySpending(
        month: json["month"],
        amount: json["amount"],
        transactionCount: json["transaction_count"],
        changeFromPrevious: json["change_from_previous"],
      );

  Map<String, dynamic> toJson() => {
        "month": month,
        "amount": amount,
        "transaction_count": transactionCount,
        "change_from_previous": changeFromPrevious,
      };
}

class SavingsInsights {
  String? totalSavings;
  String? monthlySavings;
  String? savingsGoal;
  String? goalProgress;
  String? projectedCompletion;
  String? interestEarned;
  String? savingsRate;
  List<SavingsGoal>? goals;

  SavingsInsights({
    this.totalSavings,
    this.monthlySavings,
    this.savingsGoal,
    this.goalProgress,
    this.projectedCompletion,
    this.interestEarned,
    this.savingsRate,
    this.goals,
  });

  factory SavingsInsights.fromJson(Map<String, dynamic> json) => SavingsInsights(
        totalSavings: json["total_savings"],
        monthlySavings: json["monthly_savings"],
        savingsGoal: json["savings_goal"],
        goalProgress: json["goal_progress"],
        projectedCompletion: json["projected_completion"],
        interestEarned: json["interest_earned"],
        savingsRate: json["savings_rate"],
        goals: json["goals"] == null ? [] : List<SavingsGoal>.from(json["goals"]!.map((x) => SavingsGoal.fromJson(x))),
      );

  Map<String, dynamic> toJson() => {
        "total_savings": totalSavings,
        "monthly_savings": monthlySavings,
        "savings_goal": savingsGoal,
        "goal_progress": goalProgress,
        "projected_completion": projectedCompletion,
        "interest_earned": interestEarned,
        "savings_rate": savingsRate,
        "goals": goals == null ? [] : List<dynamic>.from(goals!.map((x) => x.toJson())),
      };
}

class SavingsGoal {
  String? name;
  String? targetAmount;
  String? currentAmount;
  String? progress;
  String? deadline;
  String? monthlyRequired;

  SavingsGoal({
    this.name,
    this.targetAmount,
    this.currentAmount,
    this.progress,
    this.deadline,
    this.monthlyRequired,
  });

  factory SavingsGoal.fromJson(Map<String, dynamic> json) => SavingsGoal(
        name: json["name"],
        targetAmount: json["target_amount"],
        currentAmount: json["current_amount"],
        progress: json["progress"],
        deadline: json["deadline"],
        monthlyRequired: json["monthly_required"],
      );

  Map<String, dynamic> toJson() => {
        "name": name,
        "target_amount": targetAmount,
        "current_amount": currentAmount,
        "progress": progress,
        "deadline": deadline,
        "monthly_required": monthlyRequired,
      };
}

class IncomeAnalysis {
  String? totalIncome;
  String? averageMonthly;
  String? primarySource;
  String? incomeGrowth;
  List<IncomeSource>? sources;
  String? incomeStability;

  IncomeAnalysis({
    this.totalIncome,
    this.averageMonthly,
    this.primarySource,
    this.incomeGrowth,
    this.sources,
    this.incomeStability,
  });

  factory IncomeAnalysis.fromJson(Map<String, dynamic> json) => IncomeAnalysis(
        totalIncome: json["total_income"],
        averageMonthly: json["average_monthly"],
        primarySource: json["primary_source"],
        incomeGrowth: json["income_growth"],
        sources: json["sources"] == null ? [] : List<IncomeSource>.from(json["sources"]!.map((x) => IncomeSource.fromJson(x))),
        incomeStability: json["income_stability"],
      );

  Map<String, dynamic> toJson() => {
        "total_income": totalIncome,
        "average_monthly": averageMonthly,
        "primary_source": primarySource,
        "income_growth": incomeGrowth,
        "sources": sources == null ? [] : List<dynamic>.from(sources!.map((x) => x.toJson())),
        "income_stability": incomeStability,
      };
}

class IncomeSource {
  String? source;
  String? amount;
  String? percentage;
  String? frequency;

  IncomeSource({
    this.source,
    this.amount,
    this.percentage,
    this.frequency,
  });

  factory IncomeSource.fromJson(Map<String, dynamic> json) => IncomeSource(
        source: json["source"],
        amount: json["amount"],
        percentage: json["percentage"],
        frequency: json["frequency"],
      );

  Map<String, dynamic> toJson() => {
        "source": source,
        "amount": amount,
        "percentage": percentage,
        "frequency": frequency,
      };
}

class GoalsProgress {
  int? totalGoals;
  int? completedGoals;
  int? activeGoals;
  String? completionRate;
  List<Goal>? recentGoals;

  GoalsProgress({
    this.totalGoals,
    this.completedGoals,
    this.activeGoals,
    this.completionRate,
    this.recentGoals,
  });

  factory GoalsProgress.fromJson(Map<String, dynamic> json) => GoalsProgress(
        totalGoals: json["total_goals"],
        completedGoals: json["completed_goals"],
        activeGoals: json["active_goals"],
        completionRate: json["completion_rate"],
        recentGoals: json["recent_goals"] == null ? [] : List<Goal>.from(json["recent_goals"]!.map((x) => Goal.fromJson(x))),
      );

  Map<String, dynamic> toJson() => {
        "total_goals": totalGoals,
        "completed_goals": completedGoals,
        "active_goals": activeGoals,
        "completion_rate": completionRate,
        "recent_goals": recentGoals == null ? [] : List<dynamic>.from(recentGoals!.map((x) => x.toJson())),
      };
}

class Goal {
  String? name;
  String? type;
  String? progress;
  String? status;
  String? deadline;

  Goal({
    this.name,
    this.type,
    this.progress,
    this.status,
    this.deadline,
  });

  factory Goal.fromJson(Map<String, dynamic> json) => Goal(
        name: json["name"],
        type: json["type"],
        progress: json["progress"],
        status: json["status"],
        deadline: json["deadline"],
      );

  Map<String, dynamic> toJson() => {
        "name": name,
        "type": type,
        "progress": progress,
        "status": status,
        "deadline": deadline,
      };
}

class PersonalizedTip {
  String? title;
  String? description;
  String? category;
  String? priority; // 'high', 'medium', 'low'
  String? actionText;
  String? actionUrl;
  String? icon;

  PersonalizedTip({
    this.title,
    this.description,
    this.category,
    this.priority,
    this.actionText,
    this.actionUrl,
    this.icon,
  });

  factory PersonalizedTip.fromJson(Map<String, dynamic> json) => PersonalizedTip(
        title: json["title"],
        description: json["description"],
        category: json["category"],
        priority: json["priority"],
        actionText: json["action_text"],
        actionUrl: json["action_url"],
        icon: json["icon"],
      );

  Map<String, dynamic> toJson() => {
        "title": title,
        "description": description,
        "category": category,
        "priority": priority,
        "action_text": actionText,
        "action_url": actionUrl,
        "icon": icon,
      };
}

class Recommendation {
  String? type;
  String? title;
  String? description;
  String? potentialSavings;
  String? confidence;
  String? category;

  Recommendation({
    this.type,
    this.title,
    this.description,
    this.potentialSavings,
    this.confidence,
    this.category,
  });

  factory Recommendation.fromJson(Map<String, dynamic> json) => Recommendation(
        type: json["type"],
        title: json["title"],
        description: json["description"],
        potentialSavings: json["potential_savings"],
        confidence: json["confidence"],
        category: json["category"],
      );

  Map<String, dynamic> toJson() => {
        "type": type,
        "title": title,
        "description": description,
        "potential_savings": potentialSavings,
        "confidence": confidence,
        "category": category,
      };
}

class CreditScore {
  int? score;
  String? rating;
  String? lastUpdated;
  List<CreditFactor>? factors;

  CreditScore({
    this.score,
    this.rating,
    this.lastUpdated,
    this.factors,
  });

  factory CreditScore.fromJson(Map<String, dynamic> json) => CreditScore(
        score: json["score"],
        rating: json["rating"],
        lastUpdated: json["last_updated"],
        factors: json["factors"] == null ? [] : List<CreditFactor>.from(json["factors"]!.map((x) => CreditFactor.fromJson(x))),
      );

  Map<String, dynamic> toJson() => {
        "score": score,
        "rating": rating,
        "last_updated": lastUpdated,
        "factors": factors == null ? [] : List<dynamic>.from(factors!.map((x) => x.toJson())),
      };
}

class CreditFactor {
  String? factor;
  String? impact;
  String? description;

  CreditFactor({
    this.factor,
    this.impact,
    this.description,
  });

  factory CreditFactor.fromJson(Map<String, dynamic> json) => CreditFactor(
        factor: json["factor"],
        impact: json["impact"],
        description: json["description"],
      );

  Map<String, dynamic> toJson() => {
        "factor": factor,
        "impact": impact,
        "description": description,
      };
}

class ChartData {
  String? type; // 'line', 'bar', 'pie', 'doughnut'
  String? title;
  List<DataPoint>? dataPoints;
  String? period;

  ChartData({
    this.type,
    this.title,
    this.dataPoints,
    this.period,
  });

  factory ChartData.fromJson(Map<String, dynamic> json) => ChartData(
        type: json["type"],
        title: json["title"],
        dataPoints: json["data_points"] == null ? [] : List<DataPoint>.from(json["data_points"]!.map((x) => DataPoint.fromJson(x))),
        period: json["period"],
      );

  Map<String, dynamic> toJson() => {
        "type": type,
        "title": title,
        "data_points": dataPoints == null ? [] : List<dynamic>.from(dataPoints!.map((x) => x.toJson())),
        "period": period,
      };
}

class DataPoint {
  String? label;
  double? value;
  String? color;

  DataPoint({
    this.label,
    this.value,
    this.color,
  });

  factory DataPoint.fromJson(Map<String, dynamic> json) => DataPoint(
        label: json["label"],
        value: json["value"]?.toDouble(),
        color: json["color"],
      );

  Map<String, dynamic> toJson() => {
        "label": label,
        "value": value,
        "color": color,
      };
}

class ComparisonData {
  String? userAge;
  String? userLocation;
  String? averageSpending;
  String? averageSavings;
  String? userRanking;

  ComparisonData({
    this.userAge,
    this.userLocation,
    this.averageSpending,
    this.averageSavings,
    this.userRanking,
  });

  factory ComparisonData.fromJson(Map<String, dynamic> json) => ComparisonData(
        userAge: json["user_age"],
        userLocation: json["user_location"],
        averageSpending: json["average_spending"],
        averageSavings: json["average_savings"],
        userRanking: json["user_ranking"],
      );

  Map<String, dynamic> toJson() => {
        "user_age": userAge,
        "user_location": userLocation,
        "average_spending": averageSpending,
        "average_savings": averageSavings,
        "user_ranking": userRanking,
      };
}
