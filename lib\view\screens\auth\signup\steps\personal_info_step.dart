import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:viserpay/core/utils/dimensions.dart';
import 'package:viserpay/core/utils/my_color.dart';
import 'package:viserpay/view/components/text-form-field/enhanced_text_field.dart';
import 'package:viserpay/view/screens/auth/signup/signup_controller.dart';

class PersonalInfoStep extends StatelessWidget {
  final SignupController controller;

  const PersonalInfoStep({
    super.key,
    required this.controller,
  });

  @override
  Widget build(BuildContext context) {
    return Form(
      key: controller.personalInfoFormKey,
      child: Column(
        children: [
          // First Name
          EnhancedTextField(
            label: 'First Name',
            hint: 'Enter your first name',
            controller: controller.firstNameController,
            keyboardType: TextInputType.name,
            textInputAction: TextInputAction.next,
            inputFormatters: [
              FilteringTextInputFormatter.allow(RegExp(r'[a-zA-Z\s]')),
            ],
            validator: (value) {
              if (value == null || value.trim().isEmpty) {
                return 'First name is required';
              }
              if (value.trim().length < 2) {
                return 'First name must be at least 2 characters';
              }
              return null;
            },
            prefixIcon: const Icon(Icons.person_outline),
          ),
          
          const SizedBox(height: Dimensions.space20),
          
          // Last Name
          EnhancedTextField(
            label: 'Last Name',
            hint: 'Enter your last name',
            controller: controller.lastNameController,
            keyboardType: TextInputType.name,
            textInputAction: TextInputAction.next,
            inputFormatters: [
              FilteringTextInputFormatter.allow(RegExp(r'[a-zA-Z\s]')),
            ],
            validator: (value) {
              if (value == null || value.trim().isEmpty) {
                return 'Last name is required';
              }
              if (value.trim().length < 2) {
                return 'Last name must be at least 2 characters';
              }
              return null;
            },
            prefixIcon: const Icon(Icons.person_outline),
          ),
          
          const SizedBox(height: Dimensions.space20),
          
          // Phone Number
          EnhancedTextField(
            label: 'Phone Number',
            hint: 'Enter your phone number',
            controller: controller.phoneController,
            keyboardType: TextInputType.phone,
            textInputAction: TextInputAction.next,
            inputFormatters: [
              FilteringTextInputFormatter.digitsOnly,
              LengthLimitingTextInputFormatter(11),
            ],
            validator: (value) {
              if (value == null || value.trim().isEmpty) {
                return 'Phone number is required';
              }
              if (value.trim().length < 10) {
                return 'Phone number must be at least 10 digits';
              }
              if (value.trim().length > 11) {
                return 'Phone number must not exceed 11 digits';
              }
              if (!RegExp(r'^[0-9]+$').hasMatch(value.trim())) {
                return 'Phone number must contain only digits';
              }
              return null;
            },
            prefixIcon: const Icon(Icons.phone_outlined),
            prefixText: '+234 ',
          ),
          
          const SizedBox(height: Dimensions.space20),
          
          // Email Address
          EnhancedTextField(
            label: 'Email Address',
            hint: 'Enter your email address',
            controller: controller.emailController,
            keyboardType: TextInputType.emailAddress,
            textInputAction: TextInputAction.done,
            validator: (value) {
              if (value == null || value.trim().isEmpty) {
                return 'Email address is required';
              }
              if (!RegExp(r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$')
                  .hasMatch(value.trim())) {
                return 'Please enter a valid email address';
              }
              return null;
            },
            prefixIcon: const Icon(Icons.email_outlined),
          ),
          
          const SizedBox(height: Dimensions.space30),
          
          // Terms and Privacy Notice
          Container(
            padding: const EdgeInsets.all(Dimensions.space15),
            decoration: BoxDecoration(
              color: MyColor.primaryColor.withOpacity(0.05),
              borderRadius: BorderRadius.circular(Dimensions.mediumRadius),
              border: Border.all(
                color: MyColor.primaryColor.withOpacity(0.2),
                width: 1,
              ),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Icon(
                      Icons.info_outline,
                      color: MyColor.primaryColor,
                      size: 20,
                    ),
                    const SizedBox(width: Dimensions.space8),
                    Text(
                      'Important Information',
                      style: const TextStyle(
                        fontSize: Dimensions.fontDefault,
                        fontWeight: FontWeight.w600,
                        color: MyColor.primaryColor,
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: Dimensions.space10),
                Text(
                  'By continuing, you agree to our Terms of Service and Privacy Policy. Your information will be securely stored and used only for account verification and service provision.',
                  style: const TextStyle(
                    fontSize: Dimensions.fontSmall,
                    color: MyColor.contentTextColor,
                    height: 1.4,
                  ),
                ),
                const SizedBox(height: Dimensions.space10),
                Row(
                  children: [
                    GestureDetector(
                      onTap: () {
                        // Navigate to Terms of Service
                      },
                      child: Text(
                        'Terms of Service',
                        style: const TextStyle(
                          fontSize: Dimensions.fontSmall,
                          color: MyColor.primaryColor,
                          decoration: TextDecoration.underline,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ),
                    const SizedBox(width: Dimensions.space15),
                    GestureDetector(
                      onTap: () {
                        // Navigate to Privacy Policy
                      },
                      child: Text(
                        'Privacy Policy',
                        style: const TextStyle(
                          fontSize: Dimensions.fontSmall,
                          color: MyColor.primaryColor,
                          decoration: TextDecoration.underline,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
