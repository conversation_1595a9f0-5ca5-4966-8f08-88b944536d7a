import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:viserpay/core/utils/my_strings.dart';
import 'package:viserpay/core/utils/nigerian_currency_utils.dart';
import 'package:viserpay/data/model/insights/insights_model.dart';
import 'package:viserpay/data/model/global/response_model/response_model.dart';
import 'package:viserpay/data/repo/insights/insights_repo.dart';
import 'package:viserpay/view/components/snack_bar/show_custom_snackbar.dart';

class InsightsController extends GetxController {
  InsightsRepo insightsRepo;
  
  InsightsController({required this.insightsRepo});

  bool isLoading = false;
  bool isRefreshing = false;
  
  // Insights Data
  FinancialOverview? financialOverview;
  SpendingAnalysis? spendingAnalysis;
  SavingsInsights? savingsInsights;
  IncomeAnalysis? incomeAnalysis;
  GoalsProgress? goalsProgress;
  List<PersonalizedTip> personalizedTips = [];
  List<Recommendation> recommendations = [];
  CreditScore? creditScore;
  List<ChartData> chartData = [];
  ComparisonData? comparisonData;

  // Filter and Period Selection
  String selectedPeriod = '30_days'; // 7_days, 30_days, 90_days, 1_year
  String selectedInsightType = 'overview'; // overview, spending, savings, income, goals
  
  // Chart and Display Options
  bool showComparison = true;
  bool showPredictions = true;
  String chartType = 'line'; // line, bar, pie

  @override
  void onInit() {
    super.onInit();
    loadInsights();
  }

  // Load all insights data
  Future<void> loadInsights({bool isRefresh = false}) async {
    if (isRefresh) {
      isRefreshing = true;
    } else {
      isLoading = true;
    }
    update();

    try {
      ResponseModel response = await insightsRepo.getInsights(
        period: selectedPeriod,
        includeComparison: showComparison,
        includePredictions: showPredictions,
      );

      if (response.statusCode == 200) {
        InsightsResponseModel model = InsightsResponseModel.fromJson(
          jsonDecode(response.responseJson)
        );
        
        if (model.status?.toLowerCase() == MyStrings.success.toLowerCase()) {
          _updateInsightsData(model.data);
        } else {
          CustomSnackBar.error(
            errorList: model.message?.error ?? [MyStrings.requestFail]
          );
        }
      } else {
        CustomSnackBar.error(errorList: [response.message]);
      }
    } catch (e) {
      CustomSnackBar.error(errorList: [MyStrings.somethingWentWrong]);
    }

    isLoading = false;
    isRefreshing = false;
    update();
  }

  // Update insights data from API response
  void _updateInsightsData(InsightsData? data) {
    if (data != null) {
      financialOverview = data.financialOverview;
      spendingAnalysis = data.spendingAnalysis;
      savingsInsights = data.savingsInsights;
      incomeAnalysis = data.incomeAnalysis;
      goalsProgress = data.goalsProgress;
      personalizedTips = data.personalizedTips ?? [];
      recommendations = data.recommendations ?? [];
      creditScore = data.creditScore;
      chartData = data.chartData ?? [];
      comparisonData = data.comparisonData;
    }
  }

  // Load spending insights
  Future<void> loadSpendingInsights() async {
    isLoading = true;
    update();

    try {
      ResponseModel response = await insightsRepo.getSpendingInsights(
        period: selectedPeriod,
      );

      if (response.statusCode == 200) {
        InsightsResponseModel model = InsightsResponseModel.fromJson(
          jsonDecode(response.responseJson)
        );
        
        if (model.status?.toLowerCase() == MyStrings.success.toLowerCase()) {
          spendingAnalysis = model.data?.spendingAnalysis;
        }
      }
    } catch (e) {
      CustomSnackBar.error(errorList: [MyStrings.somethingWentWrong]);
    }

    isLoading = false;
    update();
  }

  // Load savings insights
  Future<void> loadSavingsInsights() async {
    isLoading = true;
    update();

    try {
      ResponseModel response = await insightsRepo.getSavingsInsights(
        period: selectedPeriod,
      );

      if (response.statusCode == 200) {
        InsightsResponseModel model = InsightsResponseModel.fromJson(
          jsonDecode(response.responseJson)
        );
        
        if (model.status?.toLowerCase() == MyStrings.success.toLowerCase()) {
          savingsInsights = model.data?.savingsInsights;
        }
      }
    } catch (e) {
      CustomSnackBar.error(errorList: [MyStrings.somethingWentWrong]);
    }

    isLoading = false;
    update();
  }

  // Load income insights
  Future<void> loadIncomeInsights() async {
    isLoading = true;
    update();

    try {
      ResponseModel response = await insightsRepo.getIncomeInsights(
        period: selectedPeriod,
      );

      if (response.statusCode == 200) {
        InsightsResponseModel model = InsightsResponseModel.fromJson(
          jsonDecode(response.responseJson)
        );
        
        if (model.status?.toLowerCase() == MyStrings.success.toLowerCase()) {
          incomeAnalysis = model.data?.incomeAnalysis;
        }
      }
    } catch (e) {
      CustomSnackBar.error(errorList: [MyStrings.somethingWentWrong]);
    }

    isLoading = false;
    update();
  }

  // Load goals progress
  Future<void> loadGoalsProgress() async {
    isLoading = true;
    update();

    try {
      ResponseModel response = await insightsRepo.getGoalsProgress();

      if (response.statusCode == 200) {
        InsightsResponseModel model = InsightsResponseModel.fromJson(
          jsonDecode(response.responseJson)
        );
        
        if (model.status?.toLowerCase() == MyStrings.success.toLowerCase()) {
          goalsProgress = model.data?.goalsProgress;
        }
      }
    } catch (e) {
      CustomSnackBar.error(errorList: [MyStrings.somethingWentWrong]);
    }

    isLoading = false;
    update();
  }

  // Change period filter
  void changePeriod(String period) {
    selectedPeriod = period;
    loadInsights();
  }

  // Change insight type
  void changeInsightType(String type) {
    selectedInsightType = type;
    update();
    
    switch (type) {
      case 'spending':
        loadSpendingInsights();
        break;
      case 'savings':
        loadSavingsInsights();
        break;
      case 'income':
        loadIncomeInsights();
        break;
      case 'goals':
        loadGoalsProgress();
        break;
      default:
        loadInsights();
    }
  }

  // Toggle comparison data
  void toggleComparison() {
    showComparison = !showComparison;
    loadInsights();
  }

  // Toggle predictions
  void togglePredictions() {
    showPredictions = !showPredictions;
    loadInsights();
  }

  // Change chart type
  void changeChartType(String type) {
    chartType = type;
    update();
  }

  // Get formatted financial overview
  Map<String, String> getFormattedFinancialOverview() {
    if (financialOverview == null) return {};
    
    return {
      'totalBalance': NigerianCurrencyUtils.formatNaira(financialOverview!.totalBalance),
      'totalIncome': NigerianCurrencyUtils.formatNaira(financialOverview!.totalIncome),
      'totalExpenses': NigerianCurrencyUtils.formatNaira(financialOverview!.totalExpenses),
      'netWorth': NigerianCurrencyUtils.formatNaira(financialOverview!.netWorth),
      'monthlyChange': NigerianCurrencyUtils.formatNaira(financialOverview!.monthlyChange),
      'changePercentage': '${financialOverview!.changePercentage ?? '0'}%',
      'cashFlow': NigerianCurrencyUtils.formatNaira(financialOverview!.cashFlow),
      'savingsRate': '${financialOverview!.savingsRate ?? '0'}%',
      'debtToIncomeRatio': '${financialOverview!.debtToIncomeRatio ?? '0'}%',
    };
  }

  // Get spending trend
  String getSpendingTrend() {
    if (spendingAnalysis?.spendingVelocity == null) return 'stable';
    
    double velocity = double.tryParse(spendingAnalysis!.spendingVelocity!) ?? 0;
    if (velocity > 10) return 'increasing';
    if (velocity < -10) return 'decreasing';
    return 'stable';
  }

  // Get savings progress percentage
  double getSavingsProgressPercentage() {
    if (savingsInsights?.goalProgress == null) return 0.0;
    return double.tryParse(savingsInsights!.goalProgress!) ?? 0.0;
  }

  // Get credit score color
  Color getCreditScoreColor() {
    if (creditScore?.score == null) return Colors.grey;
    
    int score = creditScore!.score!;
    if (score >= 750) return Colors.green;
    if (score >= 650) return Colors.orange;
    return Colors.red;
  }

  // Get high priority tips
  List<PersonalizedTip> getHighPriorityTips() {
    return personalizedTips
        .where((tip) => tip.priority == 'high')
        .take(3)
        .toList();
  }

  // Get top spending categories
  List<CategorySpending> getTopSpendingCategories() {
    if (spendingAnalysis?.categoryBreakdown == null) return [];
    
    List<CategorySpending> categories = List.from(spendingAnalysis!.categoryBreakdown!);
    categories.sort((a, b) {
      double amountA = double.tryParse(a.amount ?? '0') ?? 0;
      double amountB = double.tryParse(b.amount ?? '0') ?? 0;
      return amountB.compareTo(amountA);
    });
    
    return categories.take(5).toList();
  }

  // Get monthly spending trend
  List<MonthlySpending> getMonthlySpendingTrend() {
    return spendingAnalysis?.monthlyTrend ?? [];
  }

  // Get savings goals progress
  List<SavingsGoal> getSavingsGoals() {
    return savingsInsights?.goals ?? [];
  }

  // Get chart data by type
  List<ChartData> getChartDataByType(String type) {
    return chartData.where((chart) => chart.type == type).toList();
  }

  // Calculate financial health score
  int calculateFinancialHealthScore() {
    int score = 0;
    
    // Savings rate (30 points)
    if (financialOverview?.savingsRate != null) {
      double savingsRate = double.tryParse(financialOverview!.savingsRate!) ?? 0;
      if (savingsRate >= 20) score += 30;
      else if (savingsRate >= 10) score += 20;
      else if (savingsRate >= 5) score += 10;
    }
    
    // Debt to income ratio (25 points)
    if (financialOverview?.debtToIncomeRatio != null) {
      double debtRatio = double.tryParse(financialOverview!.debtToIncomeRatio!) ?? 0;
      if (debtRatio <= 20) score += 25;
      else if (debtRatio <= 30) score += 15;
      else if (debtRatio <= 40) score += 5;
    }
    
    // Emergency fund (20 points)
    if (savingsInsights?.totalSavings != null) {
      double savings = double.tryParse(savingsInsights!.totalSavings!) ?? 0;
      double monthlyExpenses = double.tryParse(financialOverview?.totalExpenses ?? '0') ?? 0;
      if (monthlyExpenses > 0) {
        double months = savings / monthlyExpenses;
        if (months >= 6) score += 20;
        else if (months >= 3) score += 15;
        else if (months >= 1) score += 10;
      }
    }
    
    // Credit score (15 points)
    if (creditScore?.score != null) {
      int credit = creditScore!.score!;
      if (credit >= 750) score += 15;
      else if (credit >= 650) score += 10;
      else if (credit >= 550) score += 5;
    }
    
    // Goals completion (10 points)
    if (goalsProgress?.completionRate != null) {
      double completion = double.tryParse(goalsProgress!.completionRate!) ?? 0;
      if (completion >= 80) score += 10;
      else if (completion >= 60) score += 7;
      else if (completion >= 40) score += 5;
    }
    
    return score;
  }

  // Get financial health rating
  String getFinancialHealthRating() {
    int score = calculateFinancialHealthScore();
    
    if (score >= 80) return 'Excellent';
    if (score >= 60) return 'Good';
    if (score >= 40) return 'Fair';
    return 'Needs Improvement';
  }

  // Get period display name
  String getPeriodDisplayName(String period) {
    switch (period) {
      case '7_days':
        return 'Last 7 Days';
      case '30_days':
        return 'Last 30 Days';
      case '90_days':
        return 'Last 3 Months';
      case '1_year':
        return 'Last Year';
      default:
        return 'Last 30 Days';
    }
  }

  // Refresh all data
  Future<void> refreshInsights() async {
    await loadInsights(isRefresh: true);
  }
}
