class NigerianBanksResponseModel {
  String? status;
  Message? message;
  BankingData? data;

  NigerianBanksResponseModel({
    this.status,
    this.message,
    this.data,
  });

  factory NigerianBanksResponseModel.fromJson(Map<String, dynamic> json) => NigerianBanksResponseModel(
        status: json["status"],
        message: json["message"] == null ? null : Message.fromJson(json["message"]),
        data: json["data"] == null ? null : BankingData.fromJson(json["data"]),
      );

  Map<String, dynamic> toJson() => {
        "status": status,
        "message": message?.toJson(),
        "data": data?.toJson(),
      };
}

class Message {
  List<String>? success;
  List<String>? error;

  Message({
    this.success,
    this.error,
  });

  factory Message.fromJson(Map<String, dynamic> json) => Message(
        success: json["success"] == null ? [] : List<String>.from(json["success"]!.map((x) => x)),
        error: json["error"] == null ? [] : List<String>.from(json["error"]!.map((x) => x)),
      );

  Map<String, dynamic> toJson() => {
        "success": success == null ? [] : List<dynamic>.from(success!.map((x) => x)),
        "error": error == null ? [] : List<dynamic>.from(error!.map((x) => x)),
      };
}

class BankingData {
  List<NigerianBank>? banks;
  AccountVerification? accountVerification;
  BankTransfer? bankTransfer;
  List<BankTransfer>? transfers;
  BankingStats? stats;

  BankingData({
    this.banks,
    this.accountVerification,
    this.bankTransfer,
    this.transfers,
    this.stats,
  });

  factory BankingData.fromJson(Map<String, dynamic> json) => BankingData(
        banks: json["banks"] == null ? [] : List<NigerianBank>.from(json["banks"]!.map((x) => NigerianBank.fromJson(x))),
        accountVerification: json["account_verification"] == null ? null : AccountVerification.fromJson(json["account_verification"]),
        bankTransfer: json["bank_transfer"] == null ? null : BankTransfer.fromJson(json["bank_transfer"]),
        transfers: json["transfers"] == null ? [] : List<BankTransfer>.from(json["transfers"]!.map((x) => BankTransfer.fromJson(x))),
        stats: json["stats"] == null ? null : BankingStats.fromJson(json["stats"]),
      );

  Map<String, dynamic> toJson() => {
        "banks": banks == null ? [] : List<dynamic>.from(banks!.map((x) => x.toJson())),
        "account_verification": accountVerification?.toJson(),
        "bank_transfer": bankTransfer?.toJson(),
        "transfers": transfers == null ? [] : List<dynamic>.from(transfers!.map((x) => x.toJson())),
        "stats": stats?.toJson(),
      };
}

// Nigerian Banks with official CBN codes
class NigerianBank {
  int? id;
  String? name;
  String? code;
  String? slug;
  String? logo;
  bool? isActive;
  String? ussdCode;
  String? sortCode;
  String? swiftCode;
  String? category; // 'commercial', 'microfinance', 'merchant', 'development'
  bool? supportsBankOne;
  bool? supportsNip;
  bool? supportsUssd;

  NigerianBank({
    this.id,
    this.name,
    this.code,
    this.slug,
    this.logo,
    this.isActive,
    this.ussdCode,
    this.sortCode,
    this.swiftCode,
    this.category,
    this.supportsBankOne,
    this.supportsNip,
    this.supportsUssd,
  });

  factory NigerianBank.fromJson(Map<String, dynamic> json) => NigerianBank(
        id: json["id"],
        name: json["name"],
        code: json["code"],
        slug: json["slug"],
        logo: json["logo"],
        isActive: json["is_active"] == 1,
        ussdCode: json["ussd_code"],
        sortCode: json["sort_code"],
        swiftCode: json["swift_code"],
        category: json["category"],
        supportsBankOne: json["supports_bank_one"] == 1,
        supportsNip: json["supports_nip"] == 1,
        supportsUssd: json["supports_ussd"] == 1,
      );

  Map<String, dynamic> toJson() => {
        "id": id,
        "name": name,
        "code": code,
        "slug": slug,
        "logo": logo,
        "is_active": isActive == true ? 1 : 0,
        "ussd_code": ussdCode,
        "sort_code": sortCode,
        "swift_code": swiftCode,
        "category": category,
        "supports_bank_one": supportsBankOne == true ? 1 : 0,
        "supports_nip": supportsNip == true ? 1 : 0,
        "supports_ussd": supportsUssd == true ? 1 : 0,
      };
}

class AccountVerification {
  String? accountNumber;
  String? accountName;
  String? bankCode;
  String? bankName;
  bool? isValid;
  String? bvn;
  String? phoneNumber;
  String? email;
  String? accountType;
  String? accountStatus;
  String? currency;
  String? balance;

  AccountVerification({
    this.accountNumber,
    this.accountName,
    this.bankCode,
    this.bankName,
    this.isValid,
    this.bvn,
    this.phoneNumber,
    this.email,
    this.accountType,
    this.accountStatus,
    this.currency,
    this.balance,
  });

  factory AccountVerification.fromJson(Map<String, dynamic> json) => AccountVerification(
        accountNumber: json["account_number"],
        accountName: json["account_name"],
        bankCode: json["bank_code"],
        bankName: json["bank_name"],
        isValid: json["is_valid"] == true,
        bvn: json["bvn"],
        phoneNumber: json["phone_number"],
        email: json["email"],
        accountType: json["account_type"],
        accountStatus: json["account_status"],
        currency: json["currency"],
        balance: json["balance"],
      );

  Map<String, dynamic> toJson() => {
        "account_number": accountNumber,
        "account_name": accountName,
        "bank_code": bankCode,
        "bank_name": bankName,
        "is_valid": isValid,
        "bvn": bvn,
        "phone_number": phoneNumber,
        "email": email,
        "account_type": accountType,
        "account_status": accountStatus,
        "currency": currency,
        "balance": balance,
      };
}

class BankTransfer {
  int? id;
  int? userId;
  String? transactionId;
  String? reference;
  String? sessionId;
  String? recipientName;
  String? recipientAccountNumber;
  String? recipientBankCode;
  String? recipientBankName;
  String? amount;
  String? fee;
  String? totalAmount;
  String? currency;
  String? narration;
  String? status; // 'pending', 'processing', 'successful', 'failed', 'reversed'
  String? paymentMethod; // 'bank_one', 'nip', 'rtgs'
  String? bankOneReference;
  String? nipReference;
  String? failureReason;
  String? processingTime;
  String? completedAt;
  String? createdAt;
  String? updatedAt;

  BankTransfer({
    this.id,
    this.userId,
    this.transactionId,
    this.reference,
    this.sessionId,
    this.recipientName,
    this.recipientAccountNumber,
    this.recipientBankCode,
    this.recipientBankName,
    this.amount,
    this.fee,
    this.totalAmount,
    this.currency,
    this.narration,
    this.status,
    this.paymentMethod,
    this.bankOneReference,
    this.nipReference,
    this.failureReason,
    this.processingTime,
    this.completedAt,
    this.createdAt,
    this.updatedAt,
  });

  factory BankTransfer.fromJson(Map<String, dynamic> json) => BankTransfer(
        id: json["id"],
        userId: json["user_id"],
        transactionId: json["transaction_id"],
        reference: json["reference"],
        sessionId: json["session_id"],
        recipientName: json["recipient_name"],
        recipientAccountNumber: json["recipient_account_number"],
        recipientBankCode: json["recipient_bank_code"],
        recipientBankName: json["recipient_bank_name"],
        amount: json["amount"],
        fee: json["fee"],
        totalAmount: json["total_amount"],
        currency: json["currency"],
        narration: json["narration"],
        status: json["status"],
        paymentMethod: json["payment_method"],
        bankOneReference: json["bank_one_reference"],
        nipReference: json["nip_reference"],
        failureReason: json["failure_reason"],
        processingTime: json["processing_time"],
        completedAt: json["completed_at"],
        createdAt: json["created_at"],
        updatedAt: json["updated_at"],
      );

  Map<String, dynamic> toJson() => {
        "id": id,
        "user_id": userId,
        "transaction_id": transactionId,
        "reference": reference,
        "session_id": sessionId,
        "recipient_name": recipientName,
        "recipient_account_number": recipientAccountNumber,
        "recipient_bank_code": recipientBankCode,
        "recipient_bank_name": recipientBankName,
        "amount": amount,
        "fee": fee,
        "total_amount": totalAmount,
        "currency": currency,
        "narration": narration,
        "status": status,
        "payment_method": paymentMethod,
        "bank_one_reference": bankOneReference,
        "nip_reference": nipReference,
        "failure_reason": failureReason,
        "processing_time": processingTime,
        "completed_at": completedAt,
        "created_at": createdAt,
        "updated_at": updatedAt,
      };
}

class BankingStats {
  String? totalTransfers;
  int? totalCount;
  String? thisMonth;
  String? lastMonth;
  String? averageTransfer;
  String? largestTransfer;
  int? successfulTransfers;
  int? failedTransfers;
  String? successRate;

  BankingStats({
    this.totalTransfers,
    this.totalCount,
    this.thisMonth,
    this.lastMonth,
    this.averageTransfer,
    this.largestTransfer,
    this.successfulTransfers,
    this.failedTransfers,
    this.successRate,
  });

  factory BankingStats.fromJson(Map<String, dynamic> json) => BankingStats(
        totalTransfers: json["total_transfers"],
        totalCount: json["total_count"],
        thisMonth: json["this_month"],
        lastMonth: json["last_month"],
        averageTransfer: json["average_transfer"],
        largestTransfer: json["largest_transfer"],
        successfulTransfers: json["successful_transfers"],
        failedTransfers: json["failed_transfers"],
        successRate: json["success_rate"],
      );

  Map<String, dynamic> toJson() => {
        "total_transfers": totalTransfers,
        "total_count": totalCount,
        "this_month": thisMonth,
        "last_month": lastMonth,
        "average_transfer": averageTransfer,
        "largest_transfer": largestTransfer,
        "successful_transfers": successfulTransfers,
        "failed_transfers": failedTransfers,
        "success_rate": successRate,
      };
}

// Request Models
class VerifyAccountRequest {
  String accountNumber;
  String bankCode;

  VerifyAccountRequest({
    required this.accountNumber,
    required this.bankCode,
  });

  Map<String, dynamic> toJson() => {
        "account_number": accountNumber,
        "bank_code": bankCode,
      };
}

class BankTransferRequest {
  String recipientAccountNumber;
  String recipientBankCode;
  String amount;
  String narration;
  String pin;
  String? paymentMethod; // 'bank_one', 'nip'

  BankTransferRequest({
    required this.recipientAccountNumber,
    required this.recipientBankCode,
    required this.amount,
    required this.narration,
    required this.pin,
    this.paymentMethod,
  });

  Map<String, dynamic> toJson() => {
        "recipient_account_number": recipientAccountNumber,
        "recipient_bank_code": recipientBankCode,
        "amount": amount,
        "narration": narration,
        "pin": pin,
        if (paymentMethod != null) "payment_method": paymentMethod,
      };
}

// Nigerian Bank Codes (CBN Official)
class NigerianBankCodes {
  static const Map<String, String> bankCodes = {
    "044": "Access Bank",
    "014": "Afribank Nigeria Plc",
    "023": "Citibank Nigeria Limited",
    "063": "Diamond Bank",
    "050": "Ecobank Nigeria Plc",
    "084": "Enterprise Bank Limited",
    "070": "Fidelity Bank",
    "011": "First Bank of Nigeria",
    "214": "First City Monument Bank",
    "058": "Guaranty Trust Bank",
    "030": "Heritage Bank",
    "301": "Jaiz Bank",
    "082": "Keystone Bank",
    "526": "Parallex Bank",
    "076": "Polaris Bank",
    "101": "Providus Bank",
    "221": "Stanbic IBTC Bank",
    "068": "Standard Chartered Bank",
    "232": "Sterling Bank",
    "032": "Union Bank of Nigeria",
    "033": "United Bank For Africa",
    "215": "Unity Bank",
    "035": "Wema Bank",
    "057": "Zenith Bank",
    "100": "SunTrust Bank",
    "502": "Rand Merchant Bank",
    "304": "TAJ Bank",
    "090": "Bowen Microfinance Bank",
    "50211": "Kuda Bank",
    "999992": "PalmPay",
    "999991": "OPay",
    "100002": "Moniepoint",
    "999999": "NIP Virtual Bank",
  };

  static String getBankName(String code) {
    return bankCodes[code] ?? "Unknown Bank";
  }

  static List<Map<String, String>> getAllBanks() {
    return bankCodes.entries
        .map((entry) => {"code": entry.key, "name": entry.value})
        .toList();
  }
}
