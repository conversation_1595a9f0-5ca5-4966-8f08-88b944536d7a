import 'dart:convert';
import 'package:viserpay/core/utils/method.dart';
import 'package:viserpay/core/utils/url_container.dart';
import 'package:viserpay/data/model/global/response_model/response_model.dart';
import 'package:viserpay/data/services/api_service.dart';

class CardPinRepo {
  ApiClient apiClient;
  CardPinRepo({required this.apiClient});

  /// Get user cards with PIN status
  Future<ResponseModel> getUserCards() async {
    String url = '${UrlContainer.baseUrl}${UrlContainer.userCardsEndPoint}';
    ResponseModel responseModel = await apiClient.request(
      url,
      Method.getMethod,
      null,
      passHeader: true,
    );
    return responseModel;
  }

  /// Create PIN for a card
  Future<ResponseModel> createCardPin({
    required String cardId,
    required String newPin,
    required String confirmPin,
    required String transactionPin,
  }) async {
    String url = '${UrlContainer.baseUrl}${UrlContainer.createCardPinEndPoint}';
    Map<String, String> params = {
      'card_id': cardId,
      'new_pin': newPin,
      'confirm_pin': confirmPin,
      'transaction_pin': transactionPin,
    };
    ResponseModel responseModel = await apiClient.request(
      url,
      Method.postMethod,
      params,
      passHeader: true,
    );
    return responseModel;
  }

  /// Change existing card PIN
  Future<ResponseModel> changeCardPin({
    required String cardId,
    required String currentPin,
    required String newPin,
    required String confirmPin,
    required String transactionPin,
  }) async {
    String url = '${UrlContainer.baseUrl}${UrlContainer.changeCardPinEndPoint}';
    Map<String, String> params = {
      'card_id': cardId,
      'current_pin': currentPin,
      'new_pin': newPin,
      'confirm_pin': confirmPin,
      'transaction_pin': transactionPin,
    };
    ResponseModel responseModel = await apiClient.request(
      url,
      Method.postMethod,
      params,
      passHeader: true,
    );
    return responseModel;
  }

  /// Reset card PIN using transaction PIN
  Future<ResponseModel> resetCardPin({
    required String cardId,
    required String newPin,
    required String confirmPin,
    required String transactionPin,
  }) async {
    String url = '${UrlContainer.baseUrl}${UrlContainer.resetCardPinEndPoint}';
    Map<String, String> params = {
      'card_id': cardId,
      'new_pin': newPin,
      'confirm_pin': confirmPin,
      'transaction_pin': transactionPin,
    };
    ResponseModel responseModel = await apiClient.request(
      url,
      Method.postMethod,
      params,
      passHeader: true,
    );
    return responseModel;
  }

  /// Unlock locked card PIN
  Future<ResponseModel> unlockCardPin({
    required String cardId,
    required String transactionPin,
  }) async {
    String url = '${UrlContainer.baseUrl}${UrlContainer.unlockCardPinEndPoint}';
    Map<String, String> params = {
      'card_id': cardId,
      'transaction_pin': transactionPin,
    };
    ResponseModel responseModel = await apiClient.request(
      url,
      Method.postMethod,
      params,
      passHeader: true,
    );
    return responseModel;
  }

  /// Block/Freeze card
  Future<ResponseModel> blockCard({
    required String cardId,
    required String reason,
    required String transactionPin,
  }) async {
    String url = '${UrlContainer.baseUrl}${UrlContainer.blockCardEndPoint}';
    Map<String, String> params = {
      'card_id': cardId,
      'reason': reason,
      'transaction_pin': transactionPin,
    };
    ResponseModel responseModel = await apiClient.request(
      url,
      Method.postMethod,
      params,
      passHeader: true,
    );
    return responseModel;
  }

  /// Unblock/Unfreeze card
  Future<ResponseModel> unblockCard({
    required String cardId,
    required String transactionPin,
  }) async {
    String url = '${UrlContainer.baseUrl}${UrlContainer.unblockCardEndPoint}';
    Map<String, String> params = {
      'card_id': cardId,
      'transaction_pin': transactionPin,
    };
    ResponseModel responseModel = await apiClient.request(
      url,
      Method.postMethod,
      params,
      passHeader: true,
    );
    return responseModel;
  }

  /// Update card spending limit
  Future<ResponseModel> updateSpendingLimit({
    required String cardId,
    required String dailyLimit,
    required String monthlyLimit,
    required String transactionPin,
  }) async {
    String url = '${UrlContainer.baseUrl}${UrlContainer.updateCardLimitEndPoint}';
    Map<String, String> params = {
      'card_id': cardId,
      'daily_limit': dailyLimit,
      'monthly_limit': monthlyLimit,
      'transaction_pin': transactionPin,
    };
    ResponseModel responseModel = await apiClient.request(
      url,
      Method.postMethod,
      params,
      passHeader: true,
    );
    return responseModel;
  }

  /// Get card transaction history
  Future<ResponseModel> getCardTransactions({
    required String cardId,
    int page = 1,
    int limit = 20,
    String? startDate,
    String? endDate,
  }) async {
    String url = '${UrlContainer.baseUrl}${UrlContainer.cardTransactionsEndPoint}/$cardId';
    Map<String, String> params = {
      'page': page.toString(),
      'limit': limit.toString(),
    };
    
    if (startDate != null) params['start_date'] = startDate;
    if (endDate != null) params['end_date'] = endDate;

    ResponseModel responseModel = await apiClient.request(
      url,
      Method.getMethod,
      params,
      passHeader: true,
    );
    return responseModel;
  }

  /// Enable/Disable card for online transactions
  Future<ResponseModel> toggleOnlineTransactions({
    required String cardId,
    required bool enable,
    required String transactionPin,
  }) async {
    String url = '${UrlContainer.baseUrl}${UrlContainer.toggleCardOnlineEndPoint}';
    Map<String, String> params = {
      'card_id': cardId,
      'enable': enable.toString(),
      'transaction_pin': transactionPin,
    };
    ResponseModel responseModel = await apiClient.request(
      url,
      Method.postMethod,
      params,
      passHeader: true,
    );
    return responseModel;
  }

  /// Enable/Disable card for international transactions
  Future<ResponseModel> toggleInternationalTransactions({
    required String cardId,
    required bool enable,
    required String transactionPin,
  }) async {
    String url = '${UrlContainer.baseUrl}${UrlContainer.toggleCardInternationalEndPoint}';
    Map<String, String> params = {
      'card_id': cardId,
      'enable': enable.toString(),
      'transaction_pin': transactionPin,
    };
    ResponseModel responseModel = await apiClient.request(
      url,
      Method.postMethod,
      params,
      passHeader: true,
    );
    return responseModel;
  }

  /// Enable/Disable card for ATM withdrawals
  Future<ResponseModel> toggleAtmWithdrawals({
    required String cardId,
    required bool enable,
    required String transactionPin,
  }) async {
    String url = '${UrlContainer.baseUrl}${UrlContainer.toggleCardAtmEndPoint}';
    Map<String, String> params = {
      'card_id': cardId,
      'enable': enable.toString(),
      'transaction_pin': transactionPin,
    };
    ResponseModel responseModel = await apiClient.request(
      url,
      Method.postMethod,
      params,
      passHeader: true,
    );
    return responseModel;
  }

  /// Get card details including PIN status
  Future<ResponseModel> getCardDetails(String cardId) async {
    String url = '${UrlContainer.baseUrl}${UrlContainer.cardDetailsEndPoint}/$cardId';
    ResponseModel responseModel = await apiClient.request(
      url,
      Method.getMethod,
      null,
      passHeader: true,
    );
    return responseModel;
  }

  /// Request card replacement
  Future<ResponseModel> requestCardReplacement({
    required String cardId,
    required String reason,
    required String deliveryAddress,
    required String transactionPin,
  }) async {
    String url = '${UrlContainer.baseUrl}${UrlContainer.replaceCardEndPoint}';
    Map<String, String> params = {
      'card_id': cardId,
      'reason': reason,
      'delivery_address': deliveryAddress,
      'transaction_pin': transactionPin,
    };
    ResponseModel responseModel = await apiClient.request(
      url,
      Method.postMethod,
      params,
      passHeader: true,
    );
    return responseModel;
  }

  /// Activate new card
  Future<ResponseModel> activateCard({
    required String cardId,
    required String activationCode,
    required String newPin,
    required String confirmPin,
  }) async {
    String url = '${UrlContainer.baseUrl}${UrlContainer.activateCardEndPoint}';
    Map<String, String> params = {
      'card_id': cardId,
      'activation_code': activationCode,
      'new_pin': newPin,
      'confirm_pin': confirmPin,
    };
    ResponseModel responseModel = await apiClient.request(
      url,
      Method.postMethod,
      params,
      passHeader: true,
    );
    return responseModel;
  }

  /// Get card PIN attempts remaining
  Future<ResponseModel> getPinAttempts(String cardId) async {
    String url = '${UrlContainer.baseUrl}${UrlContainer.cardPinAttemptsEndPoint}/$cardId';
    ResponseModel responseModel = await apiClient.request(
      url,
      Method.getMethod,
      null,
      passHeader: true,
    );
    return responseModel;
  }

  /// Verify card PIN (for testing purposes)
  Future<ResponseModel> verifyCardPin({
    required String cardId,
    required String pin,
  }) async {
    String url = '${UrlContainer.baseUrl}${UrlContainer.verifyCardPinEndPoint}';
    Map<String, String> params = {
      'card_id': cardId,
      'pin': pin,
    };
    ResponseModel responseModel = await apiClient.request(
      url,
      Method.postMethod,
      params,
      passHeader: true,
    );
    return responseModel;
  }

  /// Get card security settings
  Future<ResponseModel> getCardSecuritySettings(String cardId) async {
    String url = '${UrlContainer.baseUrl}${UrlContainer.cardSecuritySettingsEndPoint}/$cardId';
    ResponseModel responseModel = await apiClient.request(
      url,
      Method.getMethod,
      null,
      passHeader: true,
    );
    return responseModel;
  }

  /// Update card security settings
  Future<ResponseModel> updateCardSecuritySettings({
    required String cardId,
    required Map<String, dynamic> settings,
    required String transactionPin,
  }) async {
    String url = '${UrlContainer.baseUrl}${UrlContainer.updateCardSecurityEndPoint}';
    Map<String, String> params = {
      'card_id': cardId,
      'settings': jsonEncode(settings),
      'transaction_pin': transactionPin,
    };
    ResponseModel responseModel = await apiClient.request(
      url,
      Method.postMethod,
      params,
      passHeader: true,
    );
    return responseModel;
  }
}
