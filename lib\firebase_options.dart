// File generated by FlutterFire CLI.
// ignore_for_file: lines_longer_than_80_chars, avoid_classes_with_only_static_members
import 'package:firebase_core/firebase_core.dart' show FirebaseOptions;
import 'package:flutter/foundation.dart'
    show defaultTargetPlatform, kIsWeb, TargetPlatform;

/// Default [FirebaseOptions] for use with your Firebase apps.
///
/// Example:
/// ```dart
/// import 'firebase_options.dart';
/// // ...
/// await Firebase.initializeApp(
///   options: DefaultFirebaseOptions.currentPlatform,
/// );
/// ```
class DefaultFirebaseOptions {
  static FirebaseOptions get currentPlatform {
    if (kIsWeb) {
      return web;
    }
    switch (defaultTargetPlatform) {
      case TargetPlatform.android:
        return android;
      case TargetPlatform.iOS:
        return ios;
      case TargetPlatform.macOS:
        return macos;
      case TargetPlatform.windows:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for windows - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      case TargetPlatform.linux:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for linux - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      default:
        throw UnsupportedError(
          'DefaultFirebaseOptions are not supported for this platform.',
        );
    }
  }

  static const FirebaseOptions web = FirebaseOptions(
    apiKey: 'AIzaSyCXoliA0OAjrgLegekyfj3VQcyPxpmcsa8',
    appId: '1:492997799966:web:366281b6b0e21e8cf1be0f',
    messagingSenderId: '492997799966',
    projectId: 'viserpay',
    authDomain: 'viserpay.firebaseapp.com',
    storageBucket: 'viserpay.appspot.com',
    measurementId: 'G-LWQ0V837RY',
  );

  static const FirebaseOptions android = FirebaseOptions(
    apiKey: 'AIzaSyA5hfLPLfSoKi23nTIAUGpdicGOZ0XF4Vs',
    appId: '1:492997799966:android:7502dd650a44db8cf1be0f',
    messagingSenderId: '492997799966',
    projectId: 'viserpay',
    storageBucket: 'viserpay.appspot.com',
  );

  static const FirebaseOptions ios = FirebaseOptions(
    apiKey: 'AIzaSyAGir4DgV6xVd2IFt6JYkfuVQTmzYRs5ac',
    appId: '1:492997799966:ios:46e35ba21b8cdd5bf1be0f',
    messagingSenderId: '492997799966',
    projectId: 'viserpay',
    storageBucket: 'viserpay.appspot.com',
    iosBundleId: 'dev.vlab.viserpay',
  );

  static const FirebaseOptions macos = FirebaseOptions(
    apiKey: 'AIzaSyAGir4DgV6xVd2IFt6JYkfuVQTmzYRs5ac',
    appId: '1:492997799966:ios:d3befc2f117c6669f1be0f',
    messagingSenderId: '492997799966',
    projectId: 'viserpay',
    storageBucket: 'viserpay.appspot.com',
    iosBundleId: 'dev.vlab.viserpay.RunnerTests',
  );
}
