import 'package:flutter/material.dart';
import 'package:viserpay/core/utils/dimensions.dart';
import 'package:viserpay/core/utils/my_color.dart';
import 'package:viserpay/core/utils/style.dart';
import 'package:viserpay/core/utils/nigerian_currency_utils.dart';
import 'package:viserpay/data/model/insights/insights_model.dart';

class SpendingAnalysisCard extends StatefulWidget {
  final SpendingAnalysis? spendingAnalysis;

  const SpendingAnalysisCard({
    super.key,
    this.spendingAnalysis,
  });

  @override
  State<SpendingAnalysisCard> createState() => _SpendingAnalysisCardState();
}

class _SpendingAnalysisCardState extends State<SpendingAnalysisCard>
    with TickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _slideAnimation;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 600),
      vsync: this,
    );

    _slideAnimation = Tween<double>(
      begin: 50.0,
      end: 0.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeOut,
    ));

    _animationController.forward();
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: _slideAnimation,
      builder: (context, child) {
        return Transform.translate(
          offset: Offset(0, _slideAnimation.value),
          child: Container(
            padding: const EdgeInsets.all(Dimensions.space20),
            decoration: BoxDecoration(
              color: MyColor.colorWhite,
              borderRadius: BorderRadius.circular(Dimensions.cardRadius),
              boxShadow: [
                BoxShadow(
                  color: MyColor.colorGrey.withOpacity(0.1),
                  blurRadius: 15,
                  offset: const Offset(0, 5),
                ),
              ],
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Header
                Row(
                  children: [
                    Container(
                      padding: const EdgeInsets.all(Dimensions.space10),
                      decoration: BoxDecoration(
                        color: MyColor.colorRed.withOpacity(0.1),
                        borderRadius: BorderRadius.circular(Dimensions.smallRadius),
                      ),
                      child: const Icon(
                        Icons.trending_down,
                        color: MyColor.colorRed,
                        size: 24,
                      ),
                    ),
                    const SizedBox(width: Dimensions.space15),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            'Spending Analysis',
                            style: semiBoldLarge.copyWith(
                              color: MyColor.colorBlack,
                              fontSize: 18,
                            ),
                          ),
                          Text(
                            'Your spending patterns and trends',
                            style: regularDefault.copyWith(
                              color: MyColor.colorGrey,
                              fontSize: 12,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),

                const SizedBox(height: Dimensions.space25),

                // Total Spent
                _buildTotalSpent(),

                const SizedBox(height: Dimensions.space20),

                // Spending Metrics
                Row(
                  children: [
                    Expanded(
                      child: _buildMetricCard(
                        label: 'Daily Average',
                        value: widget.spendingAnalysis?.averageDaily ?? '0',
                        icon: Icons.today,
                        color: MyColor.colorOrange,
                      ),
                    ),
                    const SizedBox(width: Dimensions.space10),
                    Expanded(
                      child: _buildMetricCard(
                        label: 'Monthly Average',
                        value: widget.spendingAnalysis?.averageMonthly ?? '0',
                        icon: Icons.calendar_month,
                        color: MyColor.colorBlue,
                      ),
                    ),
                  ],
                ),

                const SizedBox(height: Dimensions.space20),

                // Top Categories
                if (widget.spendingAnalysis?.categoryBreakdown != null &&
                    widget.spendingAnalysis!.categoryBreakdown!.isNotEmpty)
                  _buildTopCategories(),

                const SizedBox(height: Dimensions.space20),

                // Spending Insights
                _buildSpendingInsights(),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildTotalSpent() {
    String totalSpent = widget.spendingAnalysis?.totalSpent ?? '0';
    String velocity = widget.spendingAnalysis?.spendingVelocity ?? '0';
    double velocityValue = double.tryParse(velocity) ?? 0;
    bool isIncreasing = velocityValue > 0;

    return Container(
      padding: const EdgeInsets.all(Dimensions.space15),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            MyColor.colorRed.withOpacity(0.1),
            MyColor.colorRed.withOpacity(0.05),
          ],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(Dimensions.smallRadius),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Total Spent This Period',
            style: regularDefault.copyWith(
              color: MyColor.colorGrey,
              fontSize: 14,
            ),
          ),
          const SizedBox(height: Dimensions.space8),
          Text(
            NigerianCurrencyUtils.formatNaira(totalSpent),
            style: boldExtraLarge.copyWith(
              color: MyColor.colorBlack,
              fontSize: 24,
            ),
          ),
          const SizedBox(height: Dimensions.space8),
          Row(
            children: [
              Icon(
                isIncreasing ? Icons.trending_up : Icons.trending_down,
                color: isIncreasing ? MyColor.colorRed : MyColor.colorGreen,
                size: 16,
              ),
              const SizedBox(width: Dimensions.space5),
              Text(
                '${velocityValue.abs().toStringAsFixed(1)}% vs last period',
                style: semiBoldDefault.copyWith(
                  color: isIncreasing ? MyColor.colorRed : MyColor.colorGreen,
                  fontSize: 12,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildMetricCard({
    required String label,
    required String value,
    required IconData icon,
    required Color color,
  }) {
    return Container(
      padding: const EdgeInsets.all(Dimensions.space15),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(Dimensions.smallRadius),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Icon(
            icon,
            color: color,
            size: 20,
          ),
          const SizedBox(height: Dimensions.space10),
          Text(
            label,
            style: regularSmall.copyWith(
              color: MyColor.colorGrey,
              fontSize: 11,
            ),
          ),
          const SizedBox(height: Dimensions.space5),
          Text(
            NigerianCurrencyUtils.formatNaira(value, useShortForm: true),
            style: semiBoldDefault.copyWith(
              color: MyColor.colorBlack,
              fontSize: 14,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTopCategories() {
    List<CategorySpending> categories = widget.spendingAnalysis!.categoryBreakdown!
        .take(3)
        .toList();

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Top Spending Categories',
          style: semiBoldDefault.copyWith(
            color: MyColor.colorBlack,
            fontSize: 16,
          ),
        ),
        const SizedBox(height: Dimensions.space15),
        ...categories.map((category) => _buildCategoryItem(category)),
      ],
    );
  }

  Widget _buildCategoryItem(CategorySpending category) {
    Color categoryColor = _getCategoryColor(category.category ?? '');
    double percentage = double.tryParse(category.percentage ?? '0') ?? 0;

    return Container(
      margin: const EdgeInsets.only(bottom: Dimensions.space10),
      padding: const EdgeInsets.all(Dimensions.space12),
      decoration: BoxDecoration(
        color: categoryColor.withOpacity(0.1),
        borderRadius: BorderRadius.circular(Dimensions.smallRadius),
      ),
      child: Row(
        children: [
          Container(
            width: 40,
            height: 40,
            decoration: BoxDecoration(
              color: categoryColor.withOpacity(0.2),
              borderRadius: BorderRadius.circular(Dimensions.smallRadius),
            ),
            child: Icon(
              _getCategoryIcon(category.category ?? ''),
              color: categoryColor,
              size: 20,
            ),
          ),
          const SizedBox(width: Dimensions.space12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  category.category ?? 'Unknown',
                  style: semiBoldDefault.copyWith(
                    color: MyColor.colorBlack,
                    fontSize: 14,
                  ),
                ),
                const SizedBox(height: Dimensions.space2),
                Text(
                  '${category.transactionCount ?? 0} transactions',
                  style: regularSmall.copyWith(
                    color: MyColor.colorGrey,
                    fontSize: 11,
                  ),
                ),
              ],
            ),
          ),
          Column(
            crossAxisAlignment: CrossAxisAlignment.end,
            children: [
              Text(
                NigerianCurrencyUtils.formatNaira(category.amount ?? '0', useShortForm: true),
                style: semiBoldDefault.copyWith(
                  color: MyColor.colorBlack,
                  fontSize: 14,
                ),
              ),
              const SizedBox(height: Dimensions.space2),
              Text(
                '${percentage.toStringAsFixed(1)}%',
                style: regularSmall.copyWith(
                  color: categoryColor,
                  fontSize: 11,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildSpendingInsights() {
    String mostFrequentCategory = widget.spendingAnalysis?.mostFrequentCategory ?? 'Unknown';
    String largestExpense = widget.spendingAnalysis?.largestExpense ?? '0';

    return Container(
      padding: const EdgeInsets.all(Dimensions.space15),
      decoration: BoxDecoration(
        color: MyColor.colorGrey.withOpacity(0.05),
        borderRadius: BorderRadius.circular(Dimensions.smallRadius),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Quick Insights',
            style: semiBoldDefault.copyWith(
              color: MyColor.colorBlack,
              fontSize: 14,
            ),
          ),
          const SizedBox(height: Dimensions.space10),
          _buildInsightItem(
            icon: Icons.category,
            text: 'Most frequent category: $mostFrequentCategory',
          ),
          const SizedBox(height: Dimensions.space8),
          _buildInsightItem(
            icon: Icons.trending_up,
            text: 'Largest expense: ${NigerianCurrencyUtils.formatNaira(largestExpense)}',
          ),
        ],
      ),
    );
  }

  Widget _buildInsightItem({
    required IconData icon,
    required String text,
  }) {
    return Row(
      children: [
        Icon(
          icon,
          color: MyColor.primaryColor,
          size: 16,
        ),
        const SizedBox(width: Dimensions.space8),
        Expanded(
          child: Text(
            text,
            style: regularDefault.copyWith(
              color: MyColor.colorGrey,
              fontSize: 12,
            ),
          ),
        ),
      ],
    );
  }

  Color _getCategoryColor(String category) {
    switch (category.toLowerCase()) {
      case 'food':
      case 'groceries':
        return MyColor.colorOrange;
      case 'transport':
      case 'fuel':
        return MyColor.colorBlue;
      case 'entertainment':
      case 'movies':
        return MyColor.colorPurple;
      case 'shopping':
      case 'clothes':
        return MyColor.colorPink;
      case 'bills':
      case 'utilities':
        return MyColor.colorYellow;
      case 'health':
      case 'medical':
        return MyColor.colorGreen;
      default:
        return MyColor.primaryColor;
    }
  }

  IconData _getCategoryIcon(String category) {
    switch (category.toLowerCase()) {
      case 'food':
      case 'groceries':
        return Icons.restaurant;
      case 'transport':
      case 'fuel':
        return Icons.directions_car;
      case 'entertainment':
      case 'movies':
        return Icons.movie;
      case 'shopping':
      case 'clothes':
        return Icons.shopping_bag;
      case 'bills':
      case 'utilities':
        return Icons.receipt;
      case 'health':
      case 'medical':
        return Icons.local_hospital;
      default:
        return Icons.category;
    }
  }
}
