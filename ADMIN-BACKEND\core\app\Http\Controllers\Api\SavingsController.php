<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\FixedDeposit;
use App\Models\TargetSaving;
use App\Models\FlexSaving;
use App\Models\GroupSaving;
use App\Models\AdminRate;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Validator;
use Carbon\Carbon;

class SavingsController extends Controller
{
    public function __construct()
    {
        $this->middleware('auth:api');
    }

    /**
     * Get savings overview for user
     */
    public function overview(): JsonResponse
    {
        $user = Auth::user();
        
        // Get all savings data
        $fixedDeposits = FixedDeposit::forUser($user->id)->active()->get();
        $targetSavings = TargetSaving::forUser($user->id)->active()->get();
        $flexSaving = FlexSaving::forUser($user->id)->active()->first();
        $groupSavings = GroupSaving::whereHas('members', function($q) use ($user) {
            $q->where('user_id', $user->id)->where('status', 'active');
        })->get();

        // Calculate totals
        $totalSavings = $user->savings_balance;
        $totalInterestEarned = $fixedDeposits->sum('accrued_interest') + 
                              ($flexSaving ? $flexSaving->total_interest_earned : 0);
        
        $activePlans = $fixedDeposits->count() + $targetSavings->count() + 
                      ($flexSaving ? 1 : 0) + $groupSavings->count();

        return response()->json([
            'status' => 'success',
            'data' => [
                'total_savings' => $totalSavings,
                'total_interest_earned' => $totalInterestEarned,
                'active_plans' => $activePlans,
                'savings_breakdown' => [
                    'fixed_deposits' => $fixedDeposits->sum('amount'),
                    'target_savings' => $targetSavings->sum('saved_amount'),
                    'flex_savings' => $flexSaving ? $flexSaving->balance : 0,
                    'group_savings' => $groupSavings->sum('contribution_amount')
                ],
                'recent_transactions' => $this->getRecentTransactions($user->id)
            ]
        ]);
    }

    /**
     * Get savings products with current rates
     */
    public function products(): JsonResponse
    {
        $rates = AdminRate::getSavingsRates();
        
        $products = [
            [
                'type' => 'fixed_deposit',
                'name' => 'Fixed Deposits',
                'description' => 'Lock your money for higher returns',
                'rates' => [
                    '30_days' => $rates['fixed_deposit_30_days'],
                    '60_days' => $rates['fixed_deposit_60_days'],
                    '90_days' => $rates['fixed_deposit_90_days'],
                    '180_days' => $rates['fixed_deposit_180_days'],
                    '365_days' => $rates['fixed_deposit_365_days']
                ],
                'minimum_amount' => FixedDeposit::getMinimumAmount(),
                'maximum_amount' => FixedDeposit::getMaximumAmount(),
                'features' => ['Guaranteed returns', 'Auto-renewal option', 'Early break with penalty']
            ],
            [
                'type' => 'target_saving',
                'name' => 'Target Savings',
                'description' => 'Save towards specific goals',
                'rate' => $rates['target_savings'],
                'minimum_amount' => TargetSaving::getMinimumAmount(),
                'maximum_amount' => TargetSaving::getMaximumAmount(),
                'features' => ['Goal-based saving', 'Auto-debit option', 'Progress tracking']
            ],
            [
                'type' => 'flex_saving',
                'name' => 'Flex Savings',
                'description' => 'Save and withdraw anytime',
                'rate' => $rates['flex_savings'],
                'minimum_amount' => FlexSaving::getMinimumBalance(),
                'maximum_amount' => FlexSaving::getMaximumBalance(),
                'features' => ['Daily interest', 'Instant withdrawals', 'No lock period']
            ],
            [
                'type' => 'group_saving',
                'name' => 'Group Savings (Ajo)',
                'description' => 'Save with friends and family',
                'rate' => $rates['group_savings'],
                'minimum_amount' => GroupSaving::getMinimumContribution(),
                'maximum_amount' => GroupSaving::getMaximumContribution(),
                'features' => ['Collaborative saving', 'Payout rotation', 'Social accountability']
            ]
        ];

        return response()->json([
            'status' => 'success',
            'data' => $products
        ]);
    }

    /**
     * Create fixed deposit
     */
    public function createFixedDeposit(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'amount' => 'required|numeric|min:' . FixedDeposit::getMinimumAmount(),
            'duration_days' => 'required|integer|in:30,60,90,180,365',
            'auto_renewal' => 'boolean'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'status' => 'error',
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], 422);
        }

        $user = Auth::user();
        $amount = $request->amount;
        $durationDays = $request->duration_days;

        // Check user balance
        if ($user->balance < $amount) {
            return response()->json([
                'status' => 'error',
                'message' => 'Insufficient balance'
            ], 400);
        }

        // Get interest rate based on duration
        $rates = AdminRate::getSavingsRates();
        $interestRate = match($durationDays) {
            30 => $rates['fixed_deposit_30_days'],
            60 => $rates['fixed_deposit_60_days'],
            90 => $rates['fixed_deposit_90_days'],
            180 => $rates['fixed_deposit_180_days'],
            365 => $rates['fixed_deposit_365_days'],
            default => 15.0
        };

        // Create fixed deposit
        $fixedDeposit = FixedDeposit::create([
            'user_id' => $user->id,
            'amount' => $amount,
            'duration_days' => $durationDays,
            'interest_rate' => $interestRate,
            'start_date' => Carbon::now()->toDateString(),
            'maturity_date' => Carbon::now()->addDays($durationDays)->toDateString(),
            'auto_renewal' => $request->auto_renewal ?? false,
            'penalty_rate' => 25.0 // 25% penalty for early withdrawal
        ]);

        // Update user balance
        $user->balance -= $amount;
        $user->savings_balance += $amount;
        $user->save();

        // Create transaction record
        $fixedDeposit->transactions()->create([
            'user_id' => $user->id,
            'savings_type' => 'fixed_deposit',
            'savings_id' => $fixedDeposit->id,
            'type' => 'deposit',
            'amount' => $amount,
            'balance_before' => 0,
            'balance_after' => $amount,
            'description' => "Fixed deposit created for {$durationDays} days",
            'trx' => getTrx()
        ]);

        return response()->json([
            'status' => 'success',
            'message' => 'Fixed deposit created successfully',
            'data' => $fixedDeposit->load('transactions')
        ]);
    }

    /**
     * Create target savings
     */
    public function createTargetSaving(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'title' => 'required|string|max:255',
            'target_amount' => 'required|numeric|min:' . TargetSaving::getMinimumAmount(),
            'target_date' => 'required|date|after:today',
            'auto_debit_amount' => 'nullable|numeric|min:0',
            'auto_debit_frequency' => 'nullable|in:daily,weekly,monthly',
            'initial_deposit' => 'nullable|numeric|min:0'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'status' => 'error',
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], 422);
        }

        $user = Auth::user();
        $initialDeposit = $request->initial_deposit ?? 0;

        // Check user balance for initial deposit
        if ($initialDeposit > 0 && $user->balance < $initialDeposit) {
            return response()->json([
                'status' => 'error',
                'message' => 'Insufficient balance for initial deposit'
            ], 400);
        }

        // Get interest rate
        $interestRate = AdminRate::getRate('savings', 'target_saving', 'interest_rate') ?? 12.0;

        // Create target saving
        $targetSaving = TargetSaving::create([
            'user_id' => $user->id,
            'title' => $request->title,
            'target_amount' => $request->target_amount,
            'saved_amount' => $initialDeposit,
            'interest_rate' => $interestRate,
            'target_date' => $request->target_date,
            'auto_debit_amount' => $request->auto_debit_amount,
            'auto_debit_frequency' => $request->auto_debit_frequency
        ]);

        // Schedule first auto-debit if configured
        if ($request->auto_debit_amount && $request->auto_debit_frequency) {
            $targetSaving->scheduleNextDebit();
        }

        // Process initial deposit
        if ($initialDeposit > 0) {
            $user->balance -= $initialDeposit;
            $user->savings_balance += $initialDeposit;
            $user->save();

            $targetSaving->transactions()->create([
                'user_id' => $user->id,
                'savings_type' => 'target_saving',
                'savings_id' => $targetSaving->id,
                'type' => 'deposit',
                'amount' => $initialDeposit,
                'balance_before' => 0,
                'balance_after' => $initialDeposit,
                'description' => 'Initial deposit for target savings',
                'trx' => getTrx()
            ]);
        }

        return response()->json([
            'status' => 'success',
            'message' => 'Target savings created successfully',
            'data' => $targetSaving->load('transactions')
        ]);
    }

    /**
     * Get user's savings plans
     */
    public function getUserSavings(): JsonResponse
    {
        $user = Auth::user();

        $data = [
            'fixed_deposits' => FixedDeposit::forUser($user->id)->with('transactions')->get(),
            'target_savings' => TargetSaving::forUser($user->id)->with('transactions')->get(),
            'flex_saving' => FlexSaving::forUser($user->id)->with('transactions')->first(),
            'group_savings' => GroupSaving::whereHas('members', function($q) use ($user) {
                $q->where('user_id', $user->id);
            })->with(['members', 'creator'])->get()
        ];

        return response()->json([
            'status' => 'success',
            'data' => $data
        ]);
    }

    /**
     * Calculate interest for savings plan
     */
    public function calculateInterest(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'type' => 'required|in:fixed_deposit,target_saving,flex_saving',
            'amount' => 'required|numeric|min:1',
            'duration_days' => 'required_if:type,fixed_deposit|integer|min:1'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'status' => 'error',
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], 422);
        }

        $type = $request->type;
        $amount = $request->amount;
        $durationDays = $request->duration_days ?? 365;

        $rates = AdminRate::getSavingsRates();
        
        $interestRate = match($type) {
            'fixed_deposit' => match($durationDays) {
                30 => $rates['fixed_deposit_30_days'],
                60 => $rates['fixed_deposit_60_days'],
                90 => $rates['fixed_deposit_90_days'],
                180 => $rates['fixed_deposit_180_days'],
                365 => $rates['fixed_deposit_365_days'],
                default => 15.0
            },
            'target_saving' => $rates['target_savings'],
            'flex_saving' => $rates['flex_savings'],
            default => 10.0
        };

        $dailyRate = $interestRate / 100 / 365;
        $interest = $amount * $dailyRate * $durationDays;
        $totalAmount = $amount + $interest;

        return response()->json([
            'status' => 'success',
            'data' => [
                'principal' => $amount,
                'interest_rate' => $interestRate,
                'duration_days' => $durationDays,
                'interest_earned' => round($interest, 2),
                'total_amount' => round($totalAmount, 2),
                'daily_interest' => round($amount * $dailyRate, 2)
            ]
        ]);
    }

    private function getRecentTransactions($userId, $limit = 10)
    {
        return \App\Models\SavingsTransaction::forUser($userId)
                                           ->with('user')
                                           ->orderBy('created_at', 'desc')
                                           ->limit($limit)
                                           ->get();
    }
}
