<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\FixedDeposit;
use App\Models\TargetSaving;
use App\Models\FlexSaving;
use App\Models\GroupSaving;
use App\Models\SavingsTransaction;
use App\Models\User;
use Illuminate\Http\Request;
use Carbon\Carbon;

class SavingsManagementController extends Controller
{
    public function __construct()
    {
        $this->middleware('admin');
    }

    /**
     * Savings overview dashboard
     */
    public function index()
    {
        $pageTitle = 'Savings Management';
        
        // Get overview statistics
        $stats = [
            'total_savings' => User::sum('savings_balance'),
            'fixed_deposits' => [
                'count' => FixedDeposit::count(),
                'amount' => FixedDeposit::sum('amount'),
                'active' => FixedDeposit::active()->count(),
                'matured' => FixedDeposit::matured()->count()
            ],
            'target_savings' => [
                'count' => TargetSaving::count(),
                'amount' => TargetSaving::sum('saved_amount'),
                'active' => TargetSaving::active()->count(),
                'completed' => TargetSaving::completed()->count()
            ],
            'flex_savings' => [
                'count' => FlexSaving::count(),
                'amount' => FlexSaving::sum('balance'),
                'active' => FlexSaving::active()->count()
            ],
            'group_savings' => [
                'count' => GroupSaving::count(),
                'amount' => GroupSaving::sum('contribution_amount'),
                'active' => GroupSaving::active()->count()
            ]
        ];

        // Recent transactions
        $recentTransactions = SavingsTransaction::with(['user'])
                                               ->orderBy('created_at', 'desc')
                                               ->limit(10)
                                               ->get();

        // Monthly trend
        $monthlyTrend = $this->getMonthlyTrend();

        return view('admin.savings.index', compact('pageTitle', 'stats', 'recentTransactions', 'monthlyTrend'));
    }

    /**
     * Fixed deposits management
     */
    public function fixedDeposits()
    {
        $pageTitle = 'Fixed Deposits Management';
        
        $deposits = FixedDeposit::with(['user', 'transactions'])
                               ->orderBy('created_at', 'desc')
                               ->paginate(20);

        return view('admin.savings.fixed_deposits', compact('pageTitle', 'deposits'));
    }

    /**
     * Target savings management
     */
    public function targetSavings()
    {
        $pageTitle = 'Target Savings Management';
        
        $savings = TargetSaving::with(['user', 'transactions'])
                              ->orderBy('created_at', 'desc')
                              ->paginate(20);

        return view('admin.savings.target_savings', compact('pageTitle', 'savings'));
    }

    /**
     * Flex savings management
     */
    public function flexSavings()
    {
        $pageTitle = 'Flex Savings Management';
        
        $savings = FlexSaving::with(['user', 'transactions'])
                            ->orderBy('created_at', 'desc')
                            ->paginate(20);

        return view('admin.savings.flex_savings', compact('pageTitle', 'savings'));
    }

    /**
     * Group savings management
     */
    public function groupSavings()
    {
        $pageTitle = 'Group Savings Management';
        
        $groups = GroupSaving::with(['creator', 'members', 'transactions'])
                            ->orderBy('created_at', 'desc')
                            ->paginate(20);

        return view('admin.savings.group_savings', compact('pageTitle', 'groups'));
    }

    /**
     * Approve fixed deposit break
     */
    public function approveFixedDepositBreak(Request $request, $id)
    {
        $deposit = FixedDeposit::findOrFail($id);
        
        if ($deposit->status !== 'break_requested') {
            $notify[] = ['error', 'Deposit is not in break requested status'];
            return back()->withNotify($notify);
        }

        $result = $deposit->approveBreak();

        if ($result['success']) {
            $notify[] = ['success', $result['message']];
        } else {
            $notify[] = ['error', $result['message']];
        }

        return back()->withNotify($notify);
    }

    /**
     * Reject fixed deposit break
     */
    public function rejectFixedDepositBreak(Request $request, $id)
    {
        $request->validate([
            'reason' => 'required|string|max:500'
        ]);

        $deposit = FixedDeposit::findOrFail($id);
        
        if ($deposit->status !== 'break_requested') {
            $notify[] = ['error', 'Deposit is not in break requested status'];
            return back()->withNotify($notify);
        }

        $result = $deposit->rejectBreak($request->reason);

        if ($result['success']) {
            $notify[] = ['success', $result['message']];
        } else {
            $notify[] = ['error', $result['message']];
        }

        return back()->withNotify($notify);
    }

    /**
     * Force mature fixed deposit
     */
    public function forceMatureFixedDeposit($id)
    {
        $deposit = FixedDeposit::findOrFail($id);
        
        if ($deposit->status !== 'active') {
            $notify[] = ['error', 'Deposit is not active'];
            return back()->withNotify($notify);
        }

        $result = $deposit->mature();

        if ($result['success']) {
            $notify[] = ['success', 'Fixed deposit matured successfully'];
        } else {
            $notify[] = ['error', $result['message']];
        }

        return back()->withNotify($notify);
    }

    /**
     * Pause target saving
     */
    public function pauseTargetSaving($id)
    {
        $saving = TargetSaving::findOrFail($id);
        
        $result = $saving->pause('Admin action');

        if ($result['success']) {
            $notify[] = ['success', $result['message']];
        } else {
            $notify[] = ['error', $result['message']];
        }

        return back()->withNotify($notify);
    }

    /**
     * Resume target saving
     */
    public function resumeTargetSaving($id)
    {
        $saving = TargetSaving::findOrFail($id);
        
        $result = $saving->resume();

        if ($result['success']) {
            $notify[] = ['success', $result['message']];
        } else {
            $notify[] = ['error', $result['message']];
        }

        return back()->withNotify($notify);
    }

    /**
     * Complete target saving
     */
    public function completeTargetSaving($id)
    {
        $saving = TargetSaving::findOrFail($id);
        
        $result = $saving->complete();

        if ($result['success']) {
            $notify[] = ['success', $result['message']];
        } else {
            $notify[] = ['error', $result['message']];
        }

        return back()->withNotify($notify);
    }

    /**
     * Freeze flex saving
     */
    public function freezeFlexSaving($id)
    {
        $saving = FlexSaving::findOrFail($id);
        
        $result = $saving->freeze('Admin action');

        if ($result['success']) {
            $notify[] = ['success', $result['message']];
        } else {
            $notify[] = ['error', $result['message']];
        }

        return back()->withNotify($notify);
    }

    /**
     * Unfreeze flex saving
     */
    public function unfreezeFlexSaving($id)
    {
        $saving = FlexSaving::findOrFail($id);
        
        $result = $saving->unfreeze();

        if ($result['success']) {
            $notify[] = ['success', $result['message']];
        } else {
            $notify[] = ['error', $result['message']];
        }

        return back()->withNotify($notify);
    }

    /**
     * Close group saving
     */
    public function closeGroupSaving($id)
    {
        $group = GroupSaving::findOrFail($id);
        
        $result = $group->close('Admin action');

        if ($result['success']) {
            $notify[] = ['success', $result['message']];
        } else {
            $notify[] = ['error', $result['message']];
        }

        return back()->withNotify($notify);
    }

    /**
     * Get savings analytics
     */
    public function analytics()
    {
        $pageTitle = 'Savings Analytics';
        
        $analytics = [
            'overview' => $this->getOverviewAnalytics(),
            'performance' => $this->getPerformanceAnalytics(),
            'user_behavior' => $this->getUserBehaviorAnalytics(),
            'product_comparison' => $this->getProductComparisonAnalytics()
        ];

        return view('admin.savings.analytics', compact('pageTitle', 'analytics'));
    }

    /**
     * Export savings data
     */
    public function export(Request $request)
    {
        $type = $request->get('type', 'all');
        $format = $request->get('format', 'csv');
        
        // Implementation for data export
        // This would generate CSV/Excel files with savings data
        
        $notify[] = ['success', 'Export initiated. Download will start shortly.'];
        return back()->withNotify($notify);
    }

    // Private helper methods
    private function getMonthlyTrend(): array
    {
        $months = [];
        for ($i = 11; $i >= 0; $i--) {
            $date = Carbon::now()->subMonths($i);
            
            $monthData = SavingsTransaction::whereMonth('created_at', $date->month)
                                          ->whereYear('created_at', $date->year)
                                          ->selectRaw('
                                              SUM(CASE WHEN type = "deposit" THEN amount ELSE 0 END) as deposits,
                                              SUM(CASE WHEN type = "withdrawal" THEN amount ELSE 0 END) as withdrawals,
                                              COUNT(*) as transactions
                                          ')
                                          ->first();

            $months[] = [
                'month' => $date->format('M Y'),
                'deposits' => $monthData->deposits ?? 0,
                'withdrawals' => $monthData->withdrawals ?? 0,
                'net_flow' => ($monthData->deposits ?? 0) - ($monthData->withdrawals ?? 0),
                'transactions' => $monthData->transactions ?? 0
            ];
        }
        
        return $months;
    }

    private function getOverviewAnalytics(): array
    {
        return [
            'total_users_with_savings' => User::where('savings_balance', '>', 0)->count(),
            'average_savings_per_user' => User::where('savings_balance', '>', 0)->avg('savings_balance'),
            'total_interest_paid' => SavingsTransaction::where('type', 'interest')->sum('amount'),
            'savings_growth_rate' => $this->calculateSavingsGrowthRate()
        ];
    }

    private function getPerformanceAnalytics(): array
    {
        return [
            'fixed_deposits' => [
                'average_duration' => FixedDeposit::avg('duration_days'),
                'early_break_rate' => $this->calculateEarlyBreakRate(),
                'renewal_rate' => $this->calculateRenewalRate()
            ],
            'target_savings' => [
                'completion_rate' => $this->calculateTargetCompletionRate(),
                'average_time_to_complete' => $this->calculateAverageCompletionTime()
            ]
        ];
    }

    private function getUserBehaviorAnalytics(): array
    {
        return [
            'most_popular_product' => $this->getMostPopularSavingsProduct(),
            'average_deposit_amount' => SavingsTransaction::where('type', 'deposit')->avg('amount'),
            'deposit_frequency' => $this->calculateDepositFrequency(),
            'user_retention_rate' => $this->calculateUserRetentionRate()
        ];
    }

    private function getProductComparisonAnalytics(): array
    {
        return [
            'fixed_deposits' => [
                'total_amount' => FixedDeposit::sum('amount'),
                'user_count' => FixedDeposit::distinct('user_id')->count(),
                'average_amount' => FixedDeposit::avg('amount')
            ],
            'target_savings' => [
                'total_amount' => TargetSaving::sum('saved_amount'),
                'user_count' => TargetSaving::distinct('user_id')->count(),
                'average_amount' => TargetSaving::avg('saved_amount')
            ],
            'flex_savings' => [
                'total_amount' => FlexSaving::sum('balance'),
                'user_count' => FlexSaving::distinct('user_id')->count(),
                'average_amount' => FlexSaving::avg('balance')
            ]
        ];
    }

    // Additional helper methods for calculations
    private function calculateSavingsGrowthRate(): float
    {
        $currentMonth = User::sum('savings_balance');
        $lastMonth = $this->getLastMonthSavingsTotal();
        
        return $lastMonth > 0 ? (($currentMonth - $lastMonth) / $lastMonth) * 100 : 0;
    }

    private function calculateEarlyBreakRate(): float
    {
        $totalDeposits = FixedDeposit::count();
        $earlyBreaks = FixedDeposit::where('status', 'broken')->count();
        
        return $totalDeposits > 0 ? ($earlyBreaks / $totalDeposits) * 100 : 0;
    }

    private function calculateRenewalRate(): float
    {
        $maturedDeposits = FixedDeposit::where('status', 'matured')->count();
        $renewedDeposits = FixedDeposit::where('is_renewal', true)->count();
        
        return $maturedDeposits > 0 ? ($renewedDeposits / $maturedDeposits) * 100 : 0;
    }

    private function calculateTargetCompletionRate(): float
    {
        $totalTargets = TargetSaving::count();
        $completedTargets = TargetSaving::where('status', 'completed')->count();
        
        return $totalTargets > 0 ? ($completedTargets / $totalTargets) * 100 : 0;
    }

    private function calculateAverageCompletionTime(): float
    {
        $completedTargets = TargetSaving::where('status', 'completed')
                                       ->whereNotNull('completion_date')
                                       ->get();

        if ($completedTargets->isEmpty()) {
            return 0;
        }

        $totalDays = $completedTargets->sum(function ($target) {
            return Carbon::parse($target->created_at)->diffInDays($target->completion_date);
        });

        return $totalDays / $completedTargets->count();
    }

    private function getMostPopularSavingsProduct(): string
    {
        $products = [
            'fixed_deposits' => FixedDeposit::distinct('user_id')->count(),
            'target_savings' => TargetSaving::distinct('user_id')->count(),
            'flex_savings' => FlexSaving::distinct('user_id')->count(),
            'group_savings' => GroupSaving::distinct('creator_id')->count()
        ];

        return array_keys($products, max($products))[0];
    }

    private function calculateDepositFrequency(): float
    {
        $totalUsers = User::where('savings_balance', '>', 0)->count();
        $totalDeposits = SavingsTransaction::where('type', 'deposit')->count();
        
        return $totalUsers > 0 ? $totalDeposits / $totalUsers : 0;
    }

    private function calculateUserRetentionRate(): float
    {
        // Users who made deposits in both current and previous month
        $currentMonth = Carbon::now();
        $previousMonth = Carbon::now()->subMonth();
        
        $currentMonthUsers = SavingsTransaction::where('type', 'deposit')
                                              ->whereMonth('created_at', $currentMonth->month)
                                              ->whereYear('created_at', $currentMonth->year)
                                              ->distinct('user_id')
                                              ->pluck('user_id');

        $previousMonthUsers = SavingsTransaction::where('type', 'deposit')
                                               ->whereMonth('created_at', $previousMonth->month)
                                               ->whereYear('created_at', $previousMonth->year)
                                               ->distinct('user_id')
                                               ->pluck('user_id');

        $retainedUsers = $currentMonthUsers->intersect($previousMonthUsers)->count();
        
        return $previousMonthUsers->count() > 0 ? ($retainedUsers / $previousMonthUsers->count()) * 100 : 0;
    }

    private function getLastMonthSavingsTotal(): float
    {
        $lastMonth = Carbon::now()->subMonth();
        
        return SavingsTransaction::where('type', 'deposit')
                                ->whereMonth('created_at', $lastMonth->month)
                                ->whereYear('created_at', $lastMonth->year)
                                ->sum('amount');
    }
}
