import 'dart:math';

class NigerianCurrencyUtils {
  static const String currencySymbol = '₦';
  static const String currencyCode = 'NGN';
  static const String currencyName = 'Nigerian Naira';

  /// Format amount to Nigerian Naira with proper formatting
  static String formatNaira(dynamic amount, {
    bool showSymbol = true,
    bool showDecimals = true,
    bool useShortForm = false,
  }) {
    if (amount == null) return showSymbol ? '₦0.00' : '0.00';
    
    double value;
    if (amount is String) {
      value = double.tryParse(amount) ?? 0.0;
    } else if (amount is num) {
      value = amount.toDouble();
    } else {
      value = 0.0;
    }

    String formattedAmount;
    
    if (useShortForm) {
      formattedAmount = _formatShortForm(value, showDecimals);
    } else {
      formattedAmount = _formatFullAmount(value, showDecimals);
    }

    return showSymbol ? '₦$formattedAmount' : formattedAmount;
  }

  /// Format amount in short form (K, M, B)
  static String _formatShortForm(double value, bool showDecimals) {
    if (value >= 1000000000) {
      double billions = value / 1000000000;
      return showDecimals 
          ? '${billions.toStringAsFixed(1)}B'
          : '${billions.toInt()}B';
    } else if (value >= 1000000) {
      double millions = value / 1000000;
      return showDecimals 
          ? '${millions.toStringAsFixed(1)}M'
          : '${millions.toInt()}M';
    } else if (value >= 1000) {
      double thousands = value / 1000;
      return showDecimals 
          ? '${thousands.toStringAsFixed(1)}K'
          : '${thousands.toInt()}K';
    } else {
      return showDecimals 
          ? value.toStringAsFixed(2)
          : value.toInt().toString();
    }
  }

  /// Format amount with comma separators
  static String _formatFullAmount(double value, bool showDecimals) {
    String valueStr = showDecimals 
        ? value.toStringAsFixed(2)
        : value.toInt().toString();
    
    return _addCommasSeparator(valueStr);
  }

  /// Add commas as thousands separator
  static String _addCommasSeparator(String value) {
    List<String> parts = value.split('.');
    String integerPart = parts[0];
    String decimalPart = parts.length > 1 ? '.${parts[1]}' : '';
    
    // Add commas to integer part
    String formattedInteger = '';
    for (int i = 0; i < integerPart.length; i++) {
      if (i > 0 && (integerPart.length - i) % 3 == 0) {
        formattedInteger += ',';
      }
      formattedInteger += integerPart[i];
    }
    
    return formattedInteger + decimalPart;
  }

  /// Parse Naira string to double
  static double parseNaira(String nairaString) {
    // Remove currency symbol and commas
    String cleanString = nairaString
        .replaceAll('₦', '')
        .replaceAll(',', '')
        .trim();
    
    return double.tryParse(cleanString) ?? 0.0;
  }

  /// Convert kobo to naira
  static double koboToNaira(int kobo) {
    return kobo / 100.0;
  }

  /// Convert naira to kobo
  static int nairaToKobo(double naira) {
    return (naira * 100).round();
  }

  /// Validate Nigerian amount format
  static bool isValidNairaAmount(String amount) {
    // Remove currency symbol and commas
    String cleanAmount = amount
        .replaceAll('₦', '')
        .replaceAll(',', '')
        .trim();
    
    // Check if it's a valid number
    double? value = double.tryParse(cleanAmount);
    if (value == null) return false;
    
    // Check if it's positive and has at most 2 decimal places
    if (value < 0) return false;
    
    List<String> parts = cleanAmount.split('.');
    if (parts.length > 2) return false;
    if (parts.length == 2 && parts[1].length > 2) return false;
    
    return true;
  }

  /// Get minimum transaction amount
  static double getMinimumAmount() {
    return 1.00; // ₦1.00
  }

  /// Get maximum transaction amount
  static double getMaximumAmount() {
    return 5000000.00; // ₦5,000,000.00
  }

  /// Check if amount is within transaction limits
  static bool isWithinLimits(double amount) {
    return amount >= getMinimumAmount() && amount <= getMaximumAmount();
  }

  /// Format transaction fee
  static String formatTransactionFee(double amount, double feePercentage, {double? fixedFee}) {
    double calculatedFee = (amount * feePercentage / 100);
    if (fixedFee != null) {
      calculatedFee += fixedFee;
    }
    
    // Minimum fee of ₦10
    calculatedFee = max(calculatedFee, 10.0);
    
    return formatNaira(calculatedFee);
  }

  /// Calculate transaction fee
  static double calculateTransactionFee(double amount, double feePercentage, {double? fixedFee}) {
    double calculatedFee = (amount * feePercentage / 100);
    if (fixedFee != null) {
      calculatedFee += fixedFee;
    }
    
    // Minimum fee of ₦10
    return max(calculatedFee, 10.0);
  }

  /// Format account number with proper spacing
  static String formatAccountNumber(String accountNumber) {
    if (accountNumber.length != 10) return accountNumber;
    
    return '${accountNumber.substring(0, 3)} ${accountNumber.substring(3, 6)} ${accountNumber.substring(6)}';
  }

  /// Validate Nigerian account number
  static bool isValidAccountNumber(String accountNumber) {
    // Remove spaces and check if it's exactly 10 digits
    String cleanNumber = accountNumber.replaceAll(' ', '');
    return RegExp(r'^\d{10}$').hasMatch(cleanNumber);
  }

  /// Format Nigerian phone number
  static String formatPhoneNumber(String phoneNumber) {
    // Remove all non-digits
    String cleanNumber = phoneNumber.replaceAll(RegExp(r'\D'), '');
    
    // Handle different formats
    if (cleanNumber.startsWith('234')) {
      cleanNumber = cleanNumber.substring(3);
    } else if (cleanNumber.startsWith('0')) {
      cleanNumber = cleanNumber.substring(1);
    }
    
    // Ensure it's 10 digits
    if (cleanNumber.length != 10) return phoneNumber;
    
    return '+234 ${cleanNumber.substring(0, 3)} ${cleanNumber.substring(3, 6)} ${cleanNumber.substring(6)}';
  }

  /// Validate Nigerian phone number
  static bool isValidPhoneNumber(String phoneNumber) {
    String cleanNumber = phoneNumber.replaceAll(RegExp(r'\D'), '');
    
    // Handle different formats
    if (cleanNumber.startsWith('234')) {
      cleanNumber = cleanNumber.substring(3);
    } else if (cleanNumber.startsWith('0')) {
      cleanNumber = cleanNumber.substring(1);
    }
    
    // Check if it's exactly 10 digits and starts with valid prefixes
    if (cleanNumber.length != 10) return false;
    
    List<String> validPrefixes = [
      '701', '702', '703', '704', '705', '706', '707', '708', '709', // 9mobile
      '802', '803', '804', '805', '806', '807', '808', '809', '810', '811', '812', '813', '814', '815', '816', '817', '818', '819', // MTN
      '701', '708', '802', '808', '812', '901', '902', '903', '904', '905', '906', '907', // Airtel
      '805', '807', '811', '815', '905', '915', // Glo
    ];
    
    String prefix = cleanNumber.substring(0, 3);
    return validPrefixes.contains(prefix);
  }

  /// Get Nigerian network provider from phone number
  static String getNetworkProvider(String phoneNumber) {
    String cleanNumber = phoneNumber.replaceAll(RegExp(r'\D'), '');
    
    if (cleanNumber.startsWith('234')) {
      cleanNumber = cleanNumber.substring(3);
    } else if (cleanNumber.startsWith('0')) {
      cleanNumber = cleanNumber.substring(1);
    }
    
    if (cleanNumber.length != 10) return 'Unknown';
    
    String prefix = cleanNumber.substring(0, 3);
    
    // MTN prefixes
    List<String> mtnPrefixes = ['803', '806', '813', '816', '810', '814', '903', '906'];
    if (mtnPrefixes.contains(prefix)) return 'MTN';
    
    // Airtel prefixes
    List<String> airtelPrefixes = ['802', '808', '812', '901', '902', '904', '907'];
    if (airtelPrefixes.contains(prefix)) return 'Airtel';
    
    // Glo prefixes
    List<String> gloPrefixes = ['805', '807', '811', '815', '905', '915'];
    if (gloPrefixes.contains(prefix)) return 'Glo';
    
    // 9mobile prefixes
    List<String> nineMobilePrefixes = ['809', '817', '818', '819', '909', '908'];
    if (nineMobilePrefixes.contains(prefix)) return '9mobile';
    
    return 'Unknown';
  }

  /// Format BVN with proper spacing
  static String formatBVN(String bvn) {
    if (bvn.length != 11) return bvn;
    
    return '${bvn.substring(0, 3)} ${bvn.substring(3, 6)} ${bvn.substring(6, 9)} ${bvn.substring(9)}';
  }

  /// Validate BVN
  static bool isValidBVN(String bvn) {
    String cleanBVN = bvn.replaceAll(' ', '');
    return RegExp(r'^\d{11}$').hasMatch(cleanBVN);
  }

  /// Get transaction limits for different account types
  static Map<String, double> getTransactionLimits(String accountType) {
    switch (accountType.toLowerCase()) {
      case 'tier_1':
        return {
          'daily_limit': 50000.0,
          'monthly_limit': 200000.0,
          'single_transaction': 50000.0,
        };
      case 'tier_2':
        return {
          'daily_limit': 200000.0,
          'monthly_limit': 500000.0,
          'single_transaction': 200000.0,
        };
      case 'tier_3':
        return {
          'daily_limit': 1000000.0,
          'monthly_limit': 5000000.0,
          'single_transaction': 1000000.0,
        };
      case 'business':
        return {
          'daily_limit': 5000000.0,
          'monthly_limit': ********.0,
          'single_transaction': 5000000.0,
        };
      default:
        return {
          'daily_limit': 50000.0,
          'monthly_limit': 200000.0,
          'single_transaction': 50000.0,
        };
    }
  }

  /// Check if amount exceeds daily limit
  static bool exceedsDailyLimit(double amount, double currentDailySpent, String accountType) {
    Map<String, double> limits = getTransactionLimits(accountType);
    return (currentDailySpent + amount) > limits['daily_limit']!;
  }

  /// Check if amount exceeds monthly limit
  static bool exceedsMonthlyLimit(double amount, double currentMonthlySpent, String accountType) {
    Map<String, double> limits = getTransactionLimits(accountType);
    return (currentMonthlySpent + amount) > limits['monthly_limit']!;
  }

  /// Check if amount exceeds single transaction limit
  static bool exceedsSingleTransactionLimit(double amount, String accountType) {
    Map<String, double> limits = getTransactionLimits(accountType);
    return amount > limits['single_transaction']!;
  }
}
