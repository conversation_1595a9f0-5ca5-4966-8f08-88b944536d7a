<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Fixed Deposits Table
        Schema::create('fixed_deposits', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('user_id');
            $table->decimal('amount', 28, 8);
            $table->integer('duration_days');
            $table->decimal('interest_rate', 5, 2);
            $table->date('start_date');
            $table->date('maturity_date');
            $table->decimal('accrued_interest', 28, 8)->default(0);
            $table->enum('status', ['active', 'matured', 'broken'])->default('active');
            $table->decimal('penalty_rate', 5, 2)->default(0);
            $table->boolean('auto_renewal')->default(false);
            $table->timestamps();
            
            $table->foreign('user_id')->references('id')->on('users')->onDelete('cascade');
            $table->index(['user_id', 'status']);
        });

        // Target Savings Table
        Schema::create('target_savings', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('user_id');
            $table->string('title');
            $table->decimal('target_amount', 28, 8);
            $table->decimal('saved_amount', 28, 8)->default(0);
            $table->decimal('interest_rate', 5, 2);
            $table->date('target_date');
            $table->decimal('auto_debit_amount', 28, 8)->default(0);
            $table->enum('auto_debit_frequency', ['daily', 'weekly', 'monthly'])->nullable();
            $table->date('next_debit_date')->nullable();
            $table->enum('status', ['active', 'completed', 'paused'])->default('active');
            $table->timestamps();
            
            $table->foreign('user_id')->references('id')->on('users')->onDelete('cascade');
            $table->index(['user_id', 'status']);
        });

        // Flex Savings Table
        Schema::create('flex_savings', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('user_id');
            $table->decimal('balance', 28, 8)->default(0);
            $table->decimal('daily_interest_rate', 8, 6);
            $table->decimal('total_interest_earned', 28, 8)->default(0);
            $table->date('last_interest_calculation')->nullable();
            $table->boolean('is_active')->default(true);
            $table->timestamps();
            
            $table->foreign('user_id')->references('id')->on('users')->onDelete('cascade');
            $table->unique('user_id'); // One flex savings account per user
        });

        // Group Savings Table
        Schema::create('group_savings', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('creator_id');
            $table->string('title');
            $table->text('description')->nullable();
            $table->decimal('contribution_amount', 28, 8);
            $table->enum('frequency', ['daily', 'weekly', 'monthly']);
            $table->integer('duration_cycles');
            $table->integer('max_members');
            $table->integer('current_members')->default(0);
            $table->decimal('interest_rate', 5, 2)->default(0);
            $table->enum('status', ['recruiting', 'active', 'completed', 'cancelled'])->default('recruiting');
            $table->date('start_date')->nullable();
            $table->date('next_payout_date')->nullable();
            $table->timestamps();
            
            $table->foreign('creator_id')->references('id')->on('users')->onDelete('cascade');
            $table->index(['status', 'creator_id']);
        });

        // Group Savings Members Table
        Schema::create('group_savings_members', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('group_id');
            $table->unsignedBigInteger('user_id');
            $table->integer('position');
            $table->decimal('total_contributed', 28, 8)->default(0);
            $table->boolean('has_received_payout')->default(false);
            $table->date('payout_date')->nullable();
            $table->enum('status', ['active', 'inactive', 'removed'])->default('active');
            $table->timestamp('joined_at')->nullable();
            $table->timestamps();
            
            $table->foreign('group_id')->references('id')->on('group_savings')->onDelete('cascade');
            $table->foreign('user_id')->references('id')->on('users')->onDelete('cascade');
            $table->unique(['group_id', 'user_id']);
            $table->index(['group_id', 'status']);
        });

        // Savings Transactions Table
        Schema::create('savings_transactions', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('user_id');
            $table->string('savings_type'); // fixed_deposit, target_saving, flex_saving, group_saving
            $table->unsignedBigInteger('savings_id');
            $table->enum('type', ['deposit', 'withdrawal', 'interest', 'penalty']);
            $table->decimal('amount', 28, 8);
            $table->decimal('balance_before', 28, 8);
            $table->decimal('balance_after', 28, 8);
            $table->string('description')->nullable();
            $table->string('trx', 40)->unique();
            $table->timestamps();
            
            $table->foreign('user_id')->references('id')->on('users')->onDelete('cascade');
            $table->index(['user_id', 'savings_type', 'savings_id']);
            $table->index('trx');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('savings_transactions');
        Schema::dropIfExists('group_savings_members');
        Schema::dropIfExists('group_savings');
        Schema::dropIfExists('flex_savings');
        Schema::dropIfExists('target_savings');
        Schema::dropIfExists('fixed_deposits');
    }
};
