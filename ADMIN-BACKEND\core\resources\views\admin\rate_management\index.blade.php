@extends('admin.layouts.app')

@section('panel')
<div class="row">
    <div class="col-lg-12">
        <div class="card b-radius--10">
            <div class="card-header">
                <div class="d-flex justify-content-between align-items-center">
                    <h5 class="card-title mb-0">@lang('Rate Management Dashboard')</h5>
                    <div class="btn-group">
                        <button type="button" class="btn btn-sm btn-outline--primary dropdown-toggle" data-bs-toggle="dropdown">
                            @lang('Quick Actions')
                        </button>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="#" data-bs-toggle="modal" data-bs-target="#createRateModal">@lang('Create New Rate')</a></li>
                            <li><a class="dropdown-item" href="#" onclick="exportRates()">@lang('Export Rates')</a></li>
                            <li><a class="dropdown-item" href="#" data-bs-toggle="modal" data-bs-target="#importRatesModal">@lang('Import Rates')</a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item text-danger" href="#" onclick="resetToDefault()">@lang('Reset to Default')</a></li>
                        </ul>
                    </div>
                </div>
            </div>
            <div class="card-body p-0">
                <div class="table-responsive--md table-responsive">
                    <table class="table table--light style--two">
                        <thead>
                            <tr>
                                <th>@lang('Category')</th>
                                <th>@lang('Active Rates')</th>
                                <th>@lang('Last Updated')</th>
                                <th>@lang('Action')</th>
                            </tr>
                        </thead>
                        <tbody>
                            @foreach($categories as $categoryKey => $categoryName)
                                @php
                                    $categoryRates = $rates[$categoryKey] ?? collect();
                                    $activeCount = $categoryRates->where('is_active', true)->count();
                                    $lastUpdated = $categoryRates->max('updated_at');
                                @endphp
                                <tr>
                                    <td>
                                        <span class="fw-bold text-primary">{{ $categoryName }}</span>
                                        <br>
                                        <small class="text-muted">{{ $categoryRates->count() }} total rates</small>
                                    </td>
                                    <td>
                                        <span class="badge badge--success">{{ $activeCount }} Active</span>
                                        @if($categoryRates->where('is_active', false)->count() > 0)
                                            <span class="badge badge--warning">{{ $categoryRates->where('is_active', false)->count() }} Inactive</span>
                                        @endif
                                    </td>
                                    <td>
                                        @if($lastUpdated)
                                            <span class="d-block">{{ showDateTime($lastUpdated) }}</span>
                                            <small class="text-muted">{{ $lastUpdated->diffForHumans() }}</small>
                                        @else
                                            <span class="text-muted">@lang('Never')</span>
                                        @endif
                                    </td>
                                    <td>
                                        <div class="btn-group">
                                            <a href="{{ route('admin.rate.management.' . $categoryKey) }}" class="btn btn-sm btn-outline--primary">
                                                <i class="la la-edit"></i> @lang('Manage')
                                            </a>
                                            <button type="button" class="btn btn-sm btn-outline--info" onclick="viewCategoryRates('{{ $categoryKey }}')">
                                                <i class="la la-eye"></i> @lang('View')
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                            @endforeach
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Rate Overview Cards -->
<div class="row gy-4 mt-4">
    <div class="col-xxl-3 col-sm-6">
        <div class="widget-two style--two box--shadow2 b-radius--5 bg--success">
            <div class="widget-two__icon b-radius--5 bg--success">
                <i class="las la-percentage"></i>
            </div>
            <div class="widget-two__content">
                <h3 class="text-white">{{ $rates->flatten()->where('category', 'savings')->where('is_active', true)->count() }}</h3>
                <p class="text-white">@lang('Active Savings Rates')</p>
            </div>
        </div>
    </div>

    <div class="col-xxl-3 col-sm-6">
        <div class="widget-two style--two box--shadow2 b-radius--5 bg--primary">
            <div class="widget-two__icon b-radius--5 bg--primary">
                <i class="las la-chart-line"></i>
            </div>
            <div class="widget-two__content">
                <h3 class="text-white">{{ $rates->flatten()->where('category', 'investment')->where('is_active', true)->count() }}</h3>
                <p class="text-white">@lang('Active Investment Rates')</p>
            </div>
        </div>
    </div>

    <div class="col-xxl-3 col-sm-6">
        <div class="widget-two style--two box--shadow2 b-radius--5 bg--warning">
            <div class="widget-two__icon b-radius--5 bg--warning">
                <i class="las la-hand-holding-usd"></i>
            </div>
            <div class="widget-two__content">
                <h3 class="text-white">{{ $rates->flatten()->where('category', 'loan')->where('is_active', true)->count() }}</h3>
                <p class="text-white">@lang('Active Loan Rates')</p>
            </div>
        </div>
    </div>

    <div class="col-xxl-3 col-sm-6">
        <div class="widget-two style--two box--shadow2 b-radius--5 bg--info">
            <div class="widget-two__icon b-radius--5 bg--info">
                <i class="las la-credit-card"></i>
            </div>
            <div class="widget-two__content">
                <h3 class="text-white">{{ $rates->flatten()->whereIn('category', ['transaction', 'card'])->where('is_active', true)->count() }}</h3>
                <p class="text-white">@lang('Active Fee Rates')</p>
            </div>
        </div>
    </div>
</div>

<!-- Create Rate Modal -->
<div class="modal fade" id="createRateModal" tabindex="-1" aria-labelledby="createRateModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="createRateModalLabel">@lang('Create New Rate')</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form action="{{ route('admin.rate.management.create') }}" method="POST">
                @csrf
                <div class="modal-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label>@lang('Category')</label>
                                <select name="category" class="form-control" required>
                                    <option value="">@lang('Select Category')</option>
                                    @foreach($categories as $key => $name)
                                        <option value="{{ $key }}">{{ $name }}</option>
                                    @endforeach
                                </select>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label>@lang('Type')</label>
                                <input type="text" name="type" class="form-control" required>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label>@lang('Name')</label>
                                <input type="text" name="name" class="form-control" required>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label>@lang('Rate')</label>
                                <input type="number" name="rate" class="form-control" step="0.01" min="0" required>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label>@lang('Unit')</label>
                                <select name="unit" class="form-control" required>
                                    <option value="percent">@lang('Percentage (%)')</option>
                                    <option value="naira">@lang('Naira (₦)')</option>
                                    <option value="fixed">@lang('Fixed Amount')</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label>@lang('Is Percentage')</label>
                                <select name="is_percentage" class="form-control">
                                    <option value="1">@lang('Yes')</option>
                                    <option value="0">@lang('No')</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label>@lang('Minimum Value')</label>
                                <input type="number" name="min_value" class="form-control" step="0.01" min="0">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label>@lang('Maximum Value')</label>
                                <input type="number" name="max_value" class="form-control" step="0.01" min="0">
                            </div>
                        </div>
                        <div class="col-12">
                            <div class="form-group">
                                <label>@lang('Description')</label>
                                <textarea name="description" class="form-control" rows="3"></textarea>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">@lang('Close')</button>
                    <button type="submit" class="btn btn--primary">@lang('Create Rate')</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Import Rates Modal -->
<div class="modal fade" id="importRatesModal" tabindex="-1" aria-labelledby="importRatesModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="importRatesModalLabel">@lang('Import Rates')</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form action="{{ route('admin.rate.management.import') }}" method="POST" enctype="multipart/form-data">
                @csrf
                <div class="modal-body">
                    <div class="form-group">
                        <label>@lang('Select JSON File')</label>
                        <input type="file" name="rates_file" class="form-control" accept=".json" required>
                        <small class="text-muted">@lang('Upload a JSON file containing rate configurations')</small>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">@lang('Close')</button>
                    <button type="submit" class="btn btn--primary">@lang('Import Rates')</button>
                </div>
            </form>
        </div>
    </div>
</div>

@endsection

@push('script')
<script>
    function exportRates() {
        window.location.href = "{{ route('admin.rate.management.export') }}";
    }

    function resetToDefault() {
        if (confirm('@lang("Are you sure you want to reset all rates to default values? This action cannot be undone.")')) {
            $.post("{{ route('admin.rate.management.reset') }}", {
                _token: "{{ csrf_token() }}"
            }).done(function(response) {
                location.reload();
            });
        }
    }

    function viewCategoryRates(category) {
        // Redirect to category-specific page
        window.location.href = "{{ route('admin.rate.management.index') }}/" + category;
    }
</script>
@endpush
