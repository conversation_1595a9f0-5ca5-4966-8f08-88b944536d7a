import 'package:flutter/material.dart';
import 'package:viserpay/core/utils/dimensions.dart';
import 'package:viserpay/core/utils/my_color.dart';

class EnhancedCard extends StatelessWidget {
  final Widget child;
  final EdgeInsetsGeometry? padding;
  final EdgeInsetsGeometry? margin;
  final double? width;
  final double? height;
  final Color? backgroundColor;
  final Color? borderColor;
  final double borderWidth;
  final double borderRadius;
  final List<BoxShadow>? boxShadow;
  final bool hasGradient;
  final Gradient? gradient;
  final VoidCallback? onTap;
  final bool isClickable;

  const EnhancedCard({
    super.key,
    required this.child,
    this.padding,
    this.margin,
    this.width,
    this.height,
    this.backgroundColor,
    this.borderColor,
    this.borderWidth = 1,
    this.borderRadius = Dimensions.cardRadius,
    this.boxShadow,
    this.hasGradient = false,
    this.gradient,
    this.onTap,
    this.isClickable = false,
  });

  @override
  Widget build(BuildContext context) {
    final defaultShadow = [
      BoxShadow(
        offset: const Offset(0, 2),
        blurRadius: 8,
        color: MyColor.brandBlack.withOpacity(0.08),
      ),
      BoxShadow(
        offset: const Offset(0, 1),
        blurRadius: 2,
        color: MyColor.brandBlack.withOpacity(0.04),
      ),
    ];

    Widget cardWidget = Container(
      width: width,
      height: height,
      margin: margin,
      padding: padding ?? const EdgeInsets.all(Dimensions.space15),
      decoration: BoxDecoration(
        color: hasGradient ? null : (backgroundColor ?? MyColor.brandWhite),
        gradient: hasGradient ? (gradient ?? MyColor.primaryGradient) : null,
        borderRadius: BorderRadius.circular(borderRadius),
        border: borderColor != null 
          ? Border.all(color: borderColor!, width: borderWidth)
          : null,
        boxShadow: boxShadow ?? defaultShadow,
      ),
      child: child,
    );

    if (isClickable && onTap != null) {
      return Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: onTap,
          borderRadius: BorderRadius.circular(borderRadius),
          splashColor: MyColor.primaryColor.withOpacity(0.1),
          highlightColor: MyColor.primaryColor.withOpacity(0.05),
          child: cardWidget,
        ),
      );
    }

    return cardWidget;
  }
}

// Specialized card variants
class PrimaryCard extends StatelessWidget {
  final Widget child;
  final EdgeInsetsGeometry? padding;
  final EdgeInsetsGeometry? margin;
  final VoidCallback? onTap;

  const PrimaryCard({
    super.key,
    required this.child,
    this.padding,
    this.margin,
    this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return EnhancedCard(
      padding: padding,
      margin: margin,
      onTap: onTap,
      isClickable: onTap != null,
      hasGradient: true,
      gradient: MyColor.primaryGradient,
      borderRadius: Dimensions.largeRadius,
      boxShadow: [
        BoxShadow(
          offset: const Offset(0, 8),
          blurRadius: 24,
          color: MyColor.primaryColor.withOpacity(0.25),
        ),
      ],
      child: DefaultTextStyle(
        style: const TextStyle(color: MyColor.brandWhite),
        child: child,
      ),
    );
  }
}

class AccentCard extends StatelessWidget {
  final Widget child;
  final EdgeInsetsGeometry? padding;
  final EdgeInsetsGeometry? margin;
  final VoidCallback? onTap;

  const AccentCard({
    super.key,
    required this.child,
    this.padding,
    this.margin,
    this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return EnhancedCard(
      padding: padding,
      margin: margin,
      onTap: onTap,
      isClickable: onTap != null,
      backgroundColor: MyColor.brandYellow.withOpacity(0.1),
      borderColor: MyColor.brandYellow,
      borderWidth: 2,
      borderRadius: Dimensions.mediumRadius,
      boxShadow: [
        BoxShadow(
          offset: const Offset(0, 4),
          blurRadius: 12,
          color: MyColor.brandYellow.withOpacity(0.2),
        ),
      ],
      child: child,
    );
  }
}

class InfoCard extends StatelessWidget {
  final Widget child;
  final EdgeInsetsGeometry? padding;
  final EdgeInsetsGeometry? margin;
  final VoidCallback? onTap;
  final Color? backgroundColor;
  final IconData? icon;
  final String? title;

  const InfoCard({
    super.key,
    required this.child,
    this.padding,
    this.margin,
    this.onTap,
    this.backgroundColor,
    this.icon,
    this.title,
  });

  @override
  Widget build(BuildContext context) {
    return EnhancedCard(
      padding: padding ?? const EdgeInsets.all(Dimensions.space20),
      margin: margin,
      onTap: onTap,
      isClickable: onTap != null,
      backgroundColor: backgroundColor ?? MyColor.brandWhite,
      borderColor: MyColor.borderColor,
      borderRadius: Dimensions.mediumRadius,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisSize: MainAxisSize.min,
        children: [
          if (icon != null || title != null) ...[
            Row(
              children: [
                if (icon != null) ...[
                  Container(
                    padding: const EdgeInsets.all(8),
                    decoration: BoxDecoration(
                      color: MyColor.primaryColor.withOpacity(0.1),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Icon(
                      icon,
                      color: MyColor.primaryColor,
                      size: 20,
                    ),
                  ),
                  const SizedBox(width: Dimensions.space10),
                ],
                if (title != null)
                  Expanded(
                    child: Text(
                      title!,
                      style: const TextStyle(
                        fontSize: Dimensions.fontLarge,
                        fontWeight: FontWeight.w600,
                        color: MyColor.primaryTextColor,
                      ),
                    ),
                  ),
              ],
            ),
            const SizedBox(height: Dimensions.space15),
          ],
          child,
        ],
      ),
    );
  }
}

class StatCard extends StatelessWidget {
  final String title;
  final String value;
  final String? subtitle;
  final IconData? icon;
  final Color? iconColor;
  final VoidCallback? onTap;

  const StatCard({
    super.key,
    required this.title,
    required this.value,
    this.subtitle,
    this.icon,
    this.iconColor,
    this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return EnhancedCard(
      onTap: onTap,
      isClickable: onTap != null,
      borderRadius: Dimensions.largeRadius,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                title,
                style: const TextStyle(
                  fontSize: Dimensions.fontDefault,
                  color: MyColor.contentTextColor,
                  fontWeight: FontWeight.w500,
                ),
              ),
              if (icon != null)
                Icon(
                  icon,
                  color: iconColor ?? MyColor.primaryColor,
                  size: 20,
                ),
            ],
          ),
          const SizedBox(height: Dimensions.space10),
          Text(
            value,
            style: const TextStyle(
              fontSize: Dimensions.fontExtraLarge,
              fontWeight: FontWeight.w700,
              color: MyColor.primaryTextColor,
            ),
          ),
          if (subtitle != null) ...[
            const SizedBox(height: Dimensions.space5),
            Text(
              subtitle!,
              style: const TextStyle(
                fontSize: Dimensions.fontSmall,
                color: MyColor.contentTextColor,
              ),
            ),
          ],
        ],
      ),
    );
  }
}
