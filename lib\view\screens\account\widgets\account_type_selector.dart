import 'package:flutter/material.dart';
import 'package:viserpay/core/utils/dimensions.dart';
import 'package:viserpay/core/utils/my_color.dart';
import 'package:viserpay/core/utils/style.dart';
import 'package:viserpay/data/model/account/account_model.dart';

class AccountTypeSelector extends StatelessWidget {
  final List<AccountType> accountTypes;
  final AccountType? selectedAccountType;
  final Function(AccountType) onAccountTypeSelected;

  const AccountTypeSelector({
    super.key,
    required this.accountTypes,
    required this.selectedAccountType,
    required this.onAccountTypeSelected,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      children: accountTypes.map((accountType) {
        final isSelected = selectedAccountType?.id == accountType.id;
        final accountTypeColor = _getAccountTypeColor(accountType.code);
        final accountTypeIcon = _getAccountTypeIcon(accountType.code);

        return GestureDetector(
          onTap: () => onAccountTypeSelected(accountType),
          child: Container(
            margin: const EdgeInsets.only(bottom: Dimensions.space15),
            padding: const EdgeInsets.all(Dimensions.space20),
            decoration: BoxDecoration(
              color: isSelected 
                  ? accountTypeColor.withOpacity(0.1)
                  : MyColor.colorWhite,
              borderRadius: BorderRadius.circular(Dimensions.mediumRadius),
              border: Border.all(
                color: isSelected 
                    ? accountTypeColor
                    : MyColor.borderColor,
                width: isSelected ? 2 : 1,
              ),
              boxShadow: [
                BoxShadow(
                  color: MyColor.colorGrey.withOpacity(0.1),
                  blurRadius: 10,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            child: Row(
              children: [
                // Account Type Icon
                Container(
                  padding: const EdgeInsets.all(Dimensions.space12),
                  decoration: BoxDecoration(
                    color: accountTypeColor.withOpacity(0.2),
                    borderRadius: BorderRadius.circular(Dimensions.smallRadius),
                  ),
                  child: Icon(
                    accountTypeIcon,
                    color: accountTypeColor,
                    size: 24,
                  ),
                ),
                
                const SizedBox(width: Dimensions.space15),
                
                // Account Type Details
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        accountType.name ?? 'Account Type',
                        style: semiBoldLarge.copyWith(
                          color: isSelected 
                              ? accountTypeColor
                              : MyColor.colorBlack,
                        ),
                      ),
                      if (accountType.description != null) ...[
                        const SizedBox(height: Dimensions.space5),
                        Text(
                          accountType.description!,
                          style: regularDefault.copyWith(
                            color: MyColor.colorGrey,
                          ),
                          maxLines: 2,
                          overflow: TextOverflow.ellipsis,
                        ),
                      ],
                      
                      const SizedBox(height: Dimensions.space10),
                      
                      // Account Type Features
                      Row(
                        children: [
                          if (accountType.interestRate != null && 
                              accountType.interestRate != '0') ...[
                            Container(
                              padding: const EdgeInsets.symmetric(
                                horizontal: Dimensions.space8,
                                vertical: Dimensions.space4,
                              ),
                              decoration: BoxDecoration(
                                color: MyColor.colorGreen.withOpacity(0.1),
                                borderRadius: BorderRadius.circular(Dimensions.smallRadius),
                              ),
                              child: Text(
                                '${accountType.interestRate}% p.a.',
                                style: regularSmall.copyWith(
                                  color: MyColor.colorGreen,
                                  fontWeight: FontWeight.w600,
                                ),
                              ),
                            ),
                            const SizedBox(width: Dimensions.space8),
                          ],
                          
                          if (accountType.minimumBalance != null &&
                              accountType.minimumBalance != '0') ...[
                            Container(
                              padding: const EdgeInsets.symmetric(
                                horizontal: Dimensions.space8,
                                vertical: Dimensions.space4,
                              ),
                              decoration: BoxDecoration(
                                color: MyColor.colorOrange.withOpacity(0.1),
                                borderRadius: BorderRadius.circular(Dimensions.smallRadius),
                              ),
                              child: Text(
                                'Min: \$${accountType.minimumBalance}',
                                style: regularSmall.copyWith(
                                  color: MyColor.colorOrange,
                                  fontWeight: FontWeight.w600,
                                ),
                              ),
                            ),
                          ],
                        ],
                      ),
                    ],
                  ),
                ),
                
                // Selection Indicator
                Container(
                  width: 24,
                  height: 24,
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    border: Border.all(
                      color: isSelected 
                          ? accountTypeColor
                          : MyColor.colorGrey,
                      width: 2,
                    ),
                    color: isSelected 
                        ? accountTypeColor
                        : Colors.transparent,
                  ),
                  child: isSelected
                      ? const Icon(
                          Icons.check,
                          color: MyColor.colorWhite,
                          size: 16,
                        )
                      : null,
                ),
              ],
            ),
          ),
        );
      }).toList(),
    );
  }

  Color _getAccountTypeColor(String? code) {
    switch (code?.toUpperCase()) {
      case 'SAVINGS':
        return MyColor.colorGreen;
      case 'CURRENT':
        return MyColor.primaryColor;
      case 'FIXED_DEPOSIT':
        return MyColor.colorOrange;
      case 'BUSINESS':
        return MyColor.colorPurple;
      case 'STUDENT':
        return MyColor.colorTeal;
      default:
        return MyColor.colorGrey;
    }
  }

  IconData _getAccountTypeIcon(String? code) {
    switch (code?.toUpperCase()) {
      case 'SAVINGS':
        return Icons.savings;
      case 'CURRENT':
        return Icons.account_balance_wallet;
      case 'FIXED_DEPOSIT':
        return Icons.lock_clock;
      case 'BUSINESS':
        return Icons.business;
      case 'STUDENT':
        return Icons.school;
      default:
        return Icons.account_balance;
    }
  }
}
