import 'dart:convert';

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:viserpay/core/helper/string_format_helper.dart';
import 'package:viserpay/core/route/route.dart';
import 'package:viserpay/core/utils/my_strings.dart';
import 'package:viserpay/data/model/global/response_model/response_model.dart';
// import 'package:viserpay/data/repo/card/card_request_repo.dart';
import 'package:viserpay/view/components/snack_bar/show_custom_snackbar.dart';

enum CardType { physical, virtual }
enum CardCategory { debit, prepaid }

class CardRequestController extends GetxController {
  CardRequestRepo cardRequestRepo;
  CardRequestController({required this.cardRequestRepo});

  bool isLoading = false;
  bool submitLoading = false;

  // Form controllers
  final TextEditingController cardNameController = TextEditingController();
  final TextEditingController deliveryAddressController = TextEditingController();
  final TextEditingController spendingLimitController = TextEditingController();
  final TextEditingController phoneController = TextEditingController();

  // Focus nodes
  final FocusNode cardNameFocusNode = FocusNode();
  final FocusNode deliveryAddressFocusNode = FocusNode();
  final FocusNode spendingLimitFocusNode = FocusNode();
  final FocusNode phoneFocusNode = FocusNode();

  // Selected values
  CardType selectedCardType = CardType.physical;
  CardCategory selectedCardCategory = CardCategory.debit;
  String selectedDeliveryState = '';
  String selectedDeliveryCity = '';

  // Data
  String currentBalance = '0';
  String currency = '₦';
  List<String> nigerianStates = [
    'Lagos', 'Abuja', 'Kano', 'Rivers', 'Oyo', 'Kaduna', 'Anambra',
    'Imo', 'Enugu', 'Delta', 'Edo', 'Ogun', 'Osun', 'Ondo', 'Ekiti',
    'Cross River', 'Akwa Ibom', 'Abia', 'Ebonyi', 'Bayelsa', 'Plateau',
    'Benue', 'Nasarawa', 'Niger', 'Kwara', 'Kogi', 'Taraba', 'Adamawa',
    'Borno', 'Yobe', 'Gombe', 'Bauchi', 'Jigawa', 'Katsina', 'Kebbi',
    'Sokoto', 'Zamfara'
  ];

  List<Map<String, dynamic>> userCards = [];
  List<Map<String, dynamic>> cardRequests = [];

  @override
  void onInit() {
    super.onInit();
    loadCardData();
  }

  void selectCardType(CardType type) {
    selectedCardType = type;
    update();
  }

  void selectCardCategory(CardCategory category) {
    selectedCardCategory = category;
    update();
  }

  void selectDeliveryState(String state) {
    selectedDeliveryState = state;
    selectedDeliveryCity = ''; // Reset city when state changes
    update();
  }

  void selectDeliveryCity(String city) {
    selectedDeliveryCity = city;
    update();
  }

  Future<void> loadCardData() async {
    isLoading = true;
    update();

    try {
      ResponseModel responseModel = await cardRequestRepo.getCardData();
      if (responseModel.statusCode == 200) {
        var data = jsonDecode(responseModel.responseJson);
        if (data['status']?.toLowerCase() == 'success') {
          currentBalance = data['data']['current_balance']?.toString() ?? '0';
          userCards = List<Map<String, dynamic>>.from(data['data']['user_cards'] ?? []);
          cardRequests = List<Map<String, dynamic>>.from(data['data']['card_requests'] ?? []);
        }
      }
    } catch (e) {
      CustomSnackBar.error(errorList: [MyStrings.somethingWentWrong]);
    }

    isLoading = false;
    update();
  }

  Future<void> requestPhysicalCard() async {
    if (!validatePhysicalCardForm()) return;

    submitLoading = true;
    update();

    try {
      ResponseModel responseModel = await cardRequestRepo.requestPhysicalCard(
        cardName: cardNameController.text.trim(),
        cardCategory: selectedCardCategory.name,
        deliveryAddress: deliveryAddressController.text.trim(),
        deliveryState: selectedDeliveryState,
        deliveryCity: selectedDeliveryCity,
        phoneNumber: phoneController.text.trim(),
        spendingLimit: spendingLimitController.text.trim(),
      );

      if (responseModel.statusCode == 200) {
        var data = jsonDecode(responseModel.responseJson);
        if (data['status']?.toLowerCase() == 'success') {
          CustomSnackBar.success(successList: [
            'Physical card request submitted successfully! You will receive it within 5-7 business days.'
          ]);
          clearForm();
          Get.back();
          await loadCardData();
        } else {
          CustomSnackBar.error(errorList: data['message']['error'] ?? [MyStrings.requestFail]);
        }
      } else {
        CustomSnackBar.error(errorList: [responseModel.message]);
      }
    } catch (e) {
      CustomSnackBar.error(errorList: [MyStrings.somethingWentWrong]);
    }

    submitLoading = false;
    update();
  }

  Future<void> requestVirtualCard() async {
    if (!validateVirtualCardForm()) return;

    submitLoading = true;
    update();

    try {
      ResponseModel responseModel = await cardRequestRepo.requestVirtualCard(
        cardName: cardNameController.text.trim(),
        cardCategory: selectedCardCategory.name,
        spendingLimit: spendingLimitController.text.trim(),
      );

      if (responseModel.statusCode == 200) {
        var data = jsonDecode(responseModel.responseJson);
        if (data['status']?.toLowerCase() == 'success') {
          CustomSnackBar.success(successList: [
            'Virtual card generated successfully! Check your cards section.'
          ]);
          clearForm();
          Get.back();
          await loadCardData();
        } else {
          CustomSnackBar.error(errorList: data['message']['error'] ?? [MyStrings.requestFail]);
        }
      } else {
        CustomSnackBar.error(errorList: [responseModel.message]);
      }
    } catch (e) {
      CustomSnackBar.error(errorList: [MyStrings.somethingWentWrong]);
    }

    submitLoading = false;
    update();
  }

  bool validatePhysicalCardForm() {
    if (cardNameController.text.trim().isEmpty) {
      CustomSnackBar.error(errorList: ['Please enter card name']);
      return false;
    }
    if (deliveryAddressController.text.trim().isEmpty) {
      CustomSnackBar.error(errorList: ['Please enter delivery address']);
      return false;
    }
    if (selectedDeliveryState.isEmpty) {
      CustomSnackBar.error(errorList: ['Please select delivery state']);
      return false;
    }
    if (phoneController.text.trim().isEmpty) {
      CustomSnackBar.error(errorList: ['Please enter phone number']);
      return false;
    }
    if (spendingLimitController.text.trim().isEmpty) {
      CustomSnackBar.error(errorList: ['Please enter spending limit']);
      return false;
    }
    return true;
  }

  bool validateVirtualCardForm() {
    if (cardNameController.text.trim().isEmpty) {
      CustomSnackBar.error(errorList: ['Please enter card name']);
      return false;
    }
    if (spendingLimitController.text.trim().isEmpty) {
      CustomSnackBar.error(errorList: ['Please enter spending limit']);
      return false;
    }
    return true;
  }

  void clearForm() {
    cardNameController.clear();
    deliveryAddressController.clear();
    spendingLimitController.clear();
    phoneController.clear();
    selectedDeliveryState = '';
    selectedDeliveryCity = '';
    selectedCardType = CardType.physical;
    selectedCardCategory = CardCategory.debit;
  }

  Future<void> freezeCard(String cardId) async {
    try {
      ResponseModel responseModel = await cardRequestRepo.freezeCard(cardId);
      if (responseModel.statusCode == 200) {
        CustomSnackBar.success(successList: ['Card frozen successfully']);
        await loadCardData();
      } else {
        CustomSnackBar.error(errorList: [responseModel.message]);
      }
    } catch (e) {
      CustomSnackBar.error(errorList: [MyStrings.somethingWentWrong]);
    }
  }

  Future<void> unfreezeCard(String cardId) async {
    try {
      ResponseModel responseModel = await cardRequestRepo.unfreezeCard(cardId);
      if (responseModel.statusCode == 200) {
        CustomSnackBar.success(successList: ['Card unfrozen successfully']);
        await loadCardData();
      } else {
        CustomSnackBar.error(errorList: [responseModel.message]);
      }
    } catch (e) {
      CustomSnackBar.error(errorList: [MyStrings.somethingWentWrong]);
    }
  }

  String getCardStatusText(String status) {
    switch (status.toLowerCase()) {
      case 'active':
        return 'Active';
      case 'frozen':
        return 'Frozen';
      case 'pending':
        return 'Pending';
      case 'delivered':
        return 'Delivered';
      case 'processing':
        return 'Processing';
      default:
        return 'Unknown';
    }
  }

  Color getCardStatusColor(String status) {
    switch (status.toLowerCase()) {
      case 'active':
        return Colors.green;
      case 'frozen':
        return Colors.orange;
      case 'pending':
        return Colors.blue;
      case 'delivered':
        return Colors.green;
      case 'processing':
        return Colors.blue;
      default:
        return Colors.grey;
    }
  }
}
