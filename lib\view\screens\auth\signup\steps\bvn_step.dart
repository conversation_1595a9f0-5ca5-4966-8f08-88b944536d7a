import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:viserpay/core/utils/dimensions.dart';
import 'package:viserpay/core/utils/my_color.dart';
import 'package:viserpay/core/utils/style.dart';
import 'package:viserpay/view/components/text-form-field/enhanced_text_field.dart';
import 'package:viserpay/view/screens/auth/signup/signup_controller.dart';

class BvnStep extends StatelessWidget {
  final SignupController controller;

  const BvnStep({
    super.key,
    required this.controller,
  });

  @override
  Widget build(BuildContext context) {
    return Form(
      key: controller.bvnFormKey,
      child: Column(
        children: [
          // BVN Information Header
          Container(
            padding: const EdgeInsets.all(Dimensions.space20),
            decoration: BoxDecoration(
              color: MyColor.primaryColor.withOpacity(0.05),
              borderRadius: BorderRadius.circular(Dimensions.largeRadius),
              border: Border.all(
                color: MyColor.primaryColor.withOpacity(0.2),
                width: 1,
              ),
            ),
            child: Column(
              children: [
                Icon(
                  Icons.account_balance_outlined,
                  color: MyColor.primaryColor,
                  size: 48,
                ),
                const SizedBox(height: Dimensions.space15),
                Text(
                  'Bank Verification Number (BVN)',
                  style: semiBoldLarge.copyWith(
                    color: MyColor.primaryColor,
                  ),
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: Dimensions.space10),
                Text(
                  'We need to verify your identity using your BVN to comply with financial regulations and ensure account security.',
                  style: regularDefault.copyWith(
                    color: MyColor.contentTextColor,
                    height: 1.4,
                  ),
                  textAlign: TextAlign.center,
                ),
              ],
            ),
          ),
          
          const SizedBox(height: Dimensions.space30),
          
          // BVN Input
          EnhancedTextField(
            label: 'Bank Verification Number (BVN)',
            hint: 'Enter your 11-digit BVN',
            controller: controller.bvnController,
            keyboardType: TextInputType.number,
            textInputAction: TextInputAction.done,
            maxLength: 11,
            inputFormatters: [
              FilteringTextInputFormatter.digitsOnly,
              LengthLimitingTextInputFormatter(11),
            ],
            validator: (value) {
              if (value == null || value.trim().isEmpty) {
                return 'BVN is required';
              }
              if (value.trim().length != 11) {
                return 'BVN must be exactly 11 digits';
              }
              if (!RegExp(r'^[0-9]+$').hasMatch(value.trim())) {
                return 'BVN must contain only numbers';
              }
              return null;
            },
            prefixIcon: const Icon(Icons.credit_card_outlined),
          ),
          
          const SizedBox(height: Dimensions.space30),
          
          // How to get BVN
          _buildHowToGetBvn(),
          
          const SizedBox(height: Dimensions.space30),
          
          // Security and Privacy Notice
          Container(
            padding: const EdgeInsets.all(Dimensions.space15),
            decoration: BoxDecoration(
              color: MyColor.colorGreen.withOpacity(0.05),
              borderRadius: BorderRadius.circular(Dimensions.mediumRadius),
              border: Border.all(
                color: MyColor.colorGreen.withOpacity(0.2),
                width: 1,
              ),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Icon(
                      Icons.security_outlined,
                      color: MyColor.colorGreen,
                      size: 20,
                    ),
                    const SizedBox(width: Dimensions.space10),
                    Text(
                      'Your Privacy & Security',
                      style: mediumDefault.copyWith(
                        color: MyColor.colorGreen,
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: Dimensions.space10),
                Text(
                  '• Your BVN is encrypted and stored securely\n• We only use it for identity verification\n• Your BVN is never shared with third parties\n• This complies with CBN regulations',
                  style: regularSmall.copyWith(
                    color: MyColor.contentTextColor,
                    height: 1.5,
                  ),
                ),
              ],
            ),
          ),
          
          const SizedBox(height: Dimensions.space20),
          
          // Why we need BVN
          Container(
            padding: const EdgeInsets.all(Dimensions.space15),
            decoration: BoxDecoration(
              color: MyColor.brandYellow.withOpacity(0.1),
              borderRadius: BorderRadius.circular(Dimensions.mediumRadius),
              border: Border.all(
                color: MyColor.brandYellow.withOpacity(0.3),
                width: 1,
              ),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Icon(
                      Icons.info_outline,
                      color: MyColor.brandYellow,
                      size: 20,
                    ),
                    const SizedBox(width: Dimensions.space10),
                    Text(
                      'Why We Need Your BVN',
                      style: mediumDefault.copyWith(
                        color: MyColor.brandYellow,
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: Dimensions.space10),
                Text(
                  '• Required by Central Bank of Nigeria (CBN)\n• Prevents fraud and identity theft\n• Enables secure financial transactions\n• Allows higher transaction limits',
                  style: regularSmall.copyWith(
                    color: MyColor.contentTextColor,
                    height: 1.5,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildHowToGetBvn() {
    return Container(
      padding: const EdgeInsets.all(Dimensions.space15),
      decoration: BoxDecoration(
        color: MyColor.borderColor.withOpacity(0.3),
        borderRadius: BorderRadius.circular(Dimensions.mediumRadius),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            "Don't have your BVN?",
            style: mediumDefault.copyWith(
              color: MyColor.primaryTextColor,
            ),
          ),
          const SizedBox(height: Dimensions.space10),
          Text(
            'You can get your BVN by:',
            style: regularDefault.copyWith(
              color: MyColor.contentTextColor,
            ),
          ),
          const SizedBox(height: Dimensions.space10),
          _buildBvnMethod(
            'Dial *565*0#',
            'From the phone number linked to your bank account',
            Icons.phone_outlined,
          ),
          _buildBvnMethod(
            'Visit any bank branch',
            'Bring a valid ID to any Nigerian bank',
            Icons.location_on_outlined,
          ),
          _buildBvnMethod(
            'Use your bank app',
            'Check your mobile banking app for BVN',
            Icons.smartphone_outlined,
          ),
          _buildBvnMethod(
            'Call your bank',
            'Contact your bank customer service',
            Icons.support_agent_outlined,
          ),
        ],
      ),
    );
  }

  Widget _buildBvnMethod(String title, String description, IconData icon) {
    return Padding(
      padding: const EdgeInsets.only(bottom: Dimensions.space10),
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(Dimensions.space8),
            decoration: BoxDecoration(
              color: MyColor.primaryColor.withOpacity(0.1),
              borderRadius: BorderRadius.circular(Dimensions.buttonRadius),
            ),
            child: Icon(
              icon,
              color: MyColor.primaryColor,
              size: 16,
            ),
          ),
          const SizedBox(width: Dimensions.space10),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: mediumSmall.copyWith(
                    color: MyColor.primaryTextColor,
                  ),
                ),
                Text(
                  description,
                  style: regularSmall.copyWith(
                    color: MyColor.contentTextColor,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
