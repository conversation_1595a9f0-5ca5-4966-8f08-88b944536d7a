import 'package:flutter/material.dart';
import 'package:viserpay/core/utils/my_color.dart';

enum AppThemeType { personal, business, kids }

class AppTheme {
  static const String _themeKey = 'app_theme';
  
  // Theme configurations
  static final Map<AppThemeType, ThemeData> _themes = {
    AppThemeType.personal: _personalTheme,
    AppThemeType.business: _businessTheme,
    AppThemeType.kids: _kidsTheme,
  };

  // Personal Theme (Default)
  static ThemeData get _personalTheme => ThemeData(
    useMaterial3: true,
    primarySwatch: _createMaterialColor(MyColor.primaryColor),
    primaryColor: MyColor.primaryColor,
    scaffoldBackgroundColor: MyColor.brandWhite,
    cardTheme: CardTheme(
      elevation: 2,
      shadowColor: MyColor.brandBlack.withOpacity(0.1),
      surfaceTintColor: Colors.transparent,
      color: MyColor.brandWhite,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
        side: BorderSide(color: MyColor.borderColor, width: 1),
      ),
    ),
    elevatedButtonTheme: ElevatedButtonThemeData(
      style: ElevatedButton.styleFrom(
        backgroundColor: MyColor.primaryColor,
        foregroundColor: MyColor.brandWhite,
        elevation: 4,
        shadowColor: MyColor.primaryColor.withOpacity(0.3),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(12),
        ),
        padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 16),
      ),
    ),
    appBarTheme: AppBarTheme(
      backgroundColor: MyColor.brandWhite,
      foregroundColor: MyColor.brandBlack,
      elevation: 0,
      surfaceTintColor: Colors.transparent,
      titleTextStyle: TextStyle(
        color: MyColor.brandBlack,
        fontSize: 18,
        fontWeight: FontWeight.w600,
        fontFamily: 'Inter',
      ),
    ),
    colorScheme: ColorScheme.fromSeed(
      seedColor: MyColor.primaryColor,
      brightness: Brightness.light,
    ),
  );

  // Business Theme
  static ThemeData get _businessTheme => ThemeData(
    useMaterial3: true,
    primarySwatch: _createMaterialColor(MyColor.brandBlack),
    primaryColor: MyColor.brandBlack,
    scaffoldBackgroundColor: const Color(0xffF5F5F5),
    cardTheme: CardTheme(
      elevation: 1,
      shadowColor: MyColor.brandBlack.withOpacity(0.05),
      surfaceTintColor: Colors.transparent,
      color: MyColor.brandWhite,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(8),
        side: BorderSide(color: MyColor.borderColor, width: 1),
      ),
    ),
    elevatedButtonTheme: ElevatedButtonThemeData(
      style: ElevatedButton.styleFrom(
        backgroundColor: MyColor.brandBlack,
        foregroundColor: MyColor.brandWhite,
        elevation: 2,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(8),
        ),
        padding: const EdgeInsets.symmetric(horizontal: 32, vertical: 16),
      ),
    ),
    appBarTheme: AppBarTheme(
      backgroundColor: MyColor.brandWhite,
      foregroundColor: MyColor.brandBlack,
      elevation: 1,
      surfaceTintColor: Colors.transparent,
      titleTextStyle: TextStyle(
        color: MyColor.brandBlack,
        fontSize: 18,
        fontWeight: FontWeight.w700,
        fontFamily: 'Rajdhani',
      ),
    ),
    colorScheme: ColorScheme.fromSeed(
      seedColor: MyColor.brandBlack,
      brightness: Brightness.light,
    ),
  );



  // Kids Theme
  static ThemeData get _kidsTheme => ThemeData(
    useMaterial3: true,
    primarySwatch: _createMaterialColor(const Color(0xffFF6B6B)),
    primaryColor: const Color(0xffFF6B6B),
    scaffoldBackgroundColor: const Color(0xffFFF8E1),
    cardTheme: CardTheme(
      elevation: 4,
      shadowColor: const Color(0xffFF6B6B).withOpacity(0.2),
      surfaceTintColor: Colors.transparent,
      color: MyColor.brandWhite,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(20),
        side: BorderSide(color: MyColor.brandYellow, width: 2),
      ),
    ),
    elevatedButtonTheme: ElevatedButtonThemeData(
      style: ElevatedButton.styleFrom(
        backgroundColor: const Color(0xffFF6B6B),
        foregroundColor: MyColor.brandWhite,
        elevation: 6,
        shadowColor: const Color(0xffFF6B6B).withOpacity(0.4),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(20),
        ),
        padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 18),
      ),
    ),
    appBarTheme: AppBarTheme(
      backgroundColor: MyColor.brandYellow,
      foregroundColor: const Color(0xff333333),
      elevation: 0,
      surfaceTintColor: Colors.transparent,
      titleTextStyle: const TextStyle(
        color: Color(0xff333333),
        fontSize: 20,
        fontWeight: FontWeight.w700,
        fontFamily: 'Rajdhani',
      ),
    ),
    colorScheme: ColorScheme.fromSeed(
      seedColor: const Color(0xffFF6B6B),
      brightness: Brightness.light,
    ),
  );

  // Helper method to create MaterialColor
  static MaterialColor _createMaterialColor(Color color) {
    List strengths = <double>[.05];
    Map<int, Color> swatch = {};
    final int r = color.red, g = color.green, b = color.blue;

    for (int i = 1; i < 10; i++) {
      strengths.add(0.1 * i);
    }
    for (var strength in strengths) {
      final double ds = 0.5 - strength;
      swatch[(strength * 1000).round()] = Color.fromRGBO(
        r + ((ds < 0 ? r : (255 - r)) * ds).round(),
        g + ((ds < 0 ? g : (255 - g)) * ds).round(),
        b + ((ds < 0 ? b : (255 - b)) * ds).round(),
        1,
      );
    }
    return MaterialColor(color.value, swatch);
  }

  // Get theme by type
  static ThemeData getTheme(AppThemeType themeType) {
    return _themes[themeType] ?? _personalTheme;
  }

  // Get all available themes
  static Map<AppThemeType, ThemeData> get allThemes => _themes;
}
