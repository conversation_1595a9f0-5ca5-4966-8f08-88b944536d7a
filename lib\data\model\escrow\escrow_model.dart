class EscrowResponseModel {
  String? status;
  Message? message;
  EscrowData? data;

  EscrowResponseModel({
    this.status,
    this.message,
    this.data,
  });

  factory EscrowResponseModel.fromJson(Map<String, dynamic> json) => EscrowResponseModel(
        status: json["status"],
        message: json["message"] == null ? null : Message.fromJson(json["message"]),
        data: json["data"] == null ? null : EscrowData.fromJson(json["data"]),
      );

  Map<String, dynamic> toJson() => {
        "status": status,
        "message": message?.toJson(),
        "data": data?.toJson(),
      };
}

class Message {
  List<String>? success;
  List<String>? error;

  Message({
    this.success,
    this.error,
  });

  factory Message.fromJson(Map<String, dynamic> json) => Message(
        success: json["success"] == null ? [] : List<String>.from(json["success"]!.map((x) => x)),
        error: json["error"] == null ? [] : List<String>.from(json["error"]!.map((x) => x)),
      );

  Map<String, dynamic> toJson() => {
        "success": success == null ? [] : List<dynamic>.from(success!.map((x) => x)),
        "error": error == null ? [] : List<dynamic>.from(error!.map((x) => x)),
      };
}

class EscrowData {
  List<EscrowTransaction>? transactions;
  EscrowTransaction? transaction;
  List<Dispute>? disputes;
  Dispute? dispute;
  EscrowStats? stats;

  EscrowData({
    this.transactions,
    this.transaction,
    this.disputes,
    this.dispute,
    this.stats,
  });

  factory EscrowData.fromJson(Map<String, dynamic> json) => EscrowData(
        transactions: json["transactions"] == null ? [] : List<EscrowTransaction>.from(json["transactions"]!.map((x) => EscrowTransaction.fromJson(x))),
        transaction: json["transaction"] == null ? null : EscrowTransaction.fromJson(json["transaction"]),
        disputes: json["disputes"] == null ? [] : List<Dispute>.from(json["disputes"]!.map((x) => Dispute.fromJson(x))),
        dispute: json["dispute"] == null ? null : Dispute.fromJson(json["dispute"]),
        stats: json["stats"] == null ? null : EscrowStats.fromJson(json["stats"]),
      );

  Map<String, dynamic> toJson() => {
        "transactions": transactions == null ? [] : List<dynamic>.from(transactions!.map((x) => x.toJson())),
        "transaction": transaction?.toJson(),
        "disputes": disputes == null ? [] : List<dynamic>.from(disputes!.map((x) => x.toJson())),
        "dispute": dispute?.toJson(),
        "stats": stats?.toJson(),
      };
}

class EscrowTransaction {
  int? id;
  String? escrowId;
  int? buyerId;
  int? sellerId;
  int? orderId;
  String? type; // 'order', 'service', 'milestone'
  String? title;
  String? description;
  String? amount;
  String? fee;
  String? totalAmount;
  String? currency;
  String? status; // 'pending', 'funded', 'delivered', 'completed', 'cancelled', 'disputed'
  String? paymentMethod;
  List<Milestone>? milestones;
  int? currentMilestone;
  String? deliveryDate;
  String? completionDate;
  String? cancellationReason;
  bool? isDisputed;
  String? disputeReason;
  User? buyer;
  User? seller;
  List<EscrowActivity>? activities;
  String? createdAt;
  String? updatedAt;

  EscrowTransaction({
    this.id,
    this.escrowId,
    this.buyerId,
    this.sellerId,
    this.orderId,
    this.type,
    this.title,
    this.description,
    this.amount,
    this.fee,
    this.totalAmount,
    this.currency,
    this.status,
    this.paymentMethod,
    this.milestones,
    this.currentMilestone,
    this.deliveryDate,
    this.completionDate,
    this.cancellationReason,
    this.isDisputed,
    this.disputeReason,
    this.buyer,
    this.seller,
    this.activities,
    this.createdAt,
    this.updatedAt,
  });

  factory EscrowTransaction.fromJson(Map<String, dynamic> json) => EscrowTransaction(
        id: json["id"],
        escrowId: json["escrow_id"],
        buyerId: json["buyer_id"],
        sellerId: json["seller_id"],
        orderId: json["order_id"],
        type: json["type"],
        title: json["title"],
        description: json["description"],
        amount: json["amount"],
        fee: json["fee"],
        totalAmount: json["total_amount"],
        currency: json["currency"],
        status: json["status"],
        paymentMethod: json["payment_method"],
        milestones: json["milestones"] == null ? [] : List<Milestone>.from(json["milestones"]!.map((x) => Milestone.fromJson(x))),
        currentMilestone: json["current_milestone"],
        deliveryDate: json["delivery_date"],
        completionDate: json["completion_date"],
        cancellationReason: json["cancellation_reason"],
        isDisputed: json["is_disputed"] == 1,
        disputeReason: json["dispute_reason"],
        buyer: json["buyer"] == null ? null : User.fromJson(json["buyer"]),
        seller: json["seller"] == null ? null : User.fromJson(json["seller"]),
        activities: json["activities"] == null ? [] : List<EscrowActivity>.from(json["activities"]!.map((x) => EscrowActivity.fromJson(x))),
        createdAt: json["created_at"],
        updatedAt: json["updated_at"],
      );

  Map<String, dynamic> toJson() => {
        "id": id,
        "escrow_id": escrowId,
        "buyer_id": buyerId,
        "seller_id": sellerId,
        "order_id": orderId,
        "type": type,
        "title": title,
        "description": description,
        "amount": amount,
        "fee": fee,
        "total_amount": totalAmount,
        "currency": currency,
        "status": status,
        "payment_method": paymentMethod,
        "milestones": milestones == null ? [] : List<dynamic>.from(milestones!.map((x) => x.toJson())),
        "current_milestone": currentMilestone,
        "delivery_date": deliveryDate,
        "completion_date": completionDate,
        "cancellation_reason": cancellationReason,
        "is_disputed": isDisputed == true ? 1 : 0,
        "dispute_reason": disputeReason,
        "buyer": buyer?.toJson(),
        "seller": seller?.toJson(),
        "activities": activities == null ? [] : List<dynamic>.from(activities!.map((x) => x.toJson())),
        "created_at": createdAt,
        "updated_at": updatedAt,
      };
}

class Milestone {
  int? id;
  int? escrowId;
  String? title;
  String? description;
  String? amount;
  int? order;
  String? status; // 'pending', 'in_progress', 'completed', 'disputed'
  String? dueDate;
  String? completedDate;
  String? deliverables;

  Milestone({
    this.id,
    this.escrowId,
    this.title,
    this.description,
    this.amount,
    this.order,
    this.status,
    this.dueDate,
    this.completedDate,
    this.deliverables,
  });

  factory Milestone.fromJson(Map<String, dynamic> json) => Milestone(
        id: json["id"],
        escrowId: json["escrow_id"],
        title: json["title"],
        description: json["description"],
        amount: json["amount"],
        order: json["order"],
        status: json["status"],
        dueDate: json["due_date"],
        completedDate: json["completed_date"],
        deliverables: json["deliverables"],
      );

  Map<String, dynamic> toJson() => {
        "id": id,
        "escrow_id": escrowId,
        "title": title,
        "description": description,
        "amount": amount,
        "order": order,
        "status": status,
        "due_date": dueDate,
        "completed_date": completedDate,
        "deliverables": deliverables,
      };
}

class User {
  int? id;
  String? firstName;
  String? lastName;
  String? email;
  String? phone;
  String? avatar;
  bool? isVerified;

  User({
    this.id,
    this.firstName,
    this.lastName,
    this.email,
    this.phone,
    this.avatar,
    this.isVerified,
  });

  factory User.fromJson(Map<String, dynamic> json) => User(
        id: json["id"],
        firstName: json["first_name"],
        lastName: json["last_name"],
        email: json["email"],
        phone: json["phone"],
        avatar: json["avatar"],
        isVerified: json["is_verified"] == 1,
      );

  Map<String, dynamic> toJson() => {
        "id": id,
        "first_name": firstName,
        "last_name": lastName,
        "email": email,
        "phone": phone,
        "avatar": avatar,
        "is_verified": isVerified == true ? 1 : 0,
      };
}

class EscrowActivity {
  int? id;
  int? escrowId;
  int? userId;
  String? action;
  String? description;
  String? metadata;
  String? createdAt;

  EscrowActivity({
    this.id,
    this.escrowId,
    this.userId,
    this.action,
    this.description,
    this.metadata,
    this.createdAt,
  });

  factory EscrowActivity.fromJson(Map<String, dynamic> json) => EscrowActivity(
        id: json["id"],
        escrowId: json["escrow_id"],
        userId: json["user_id"],
        action: json["action"],
        description: json["description"],
        metadata: json["metadata"],
        createdAt: json["created_at"],
      );

  Map<String, dynamic> toJson() => {
        "id": id,
        "escrow_id": escrowId,
        "user_id": userId,
        "action": action,
        "description": description,
        "metadata": metadata,
        "created_at": createdAt,
      };
}

class Dispute {
  int? id;
  int? escrowId;
  int? raisedBy;
  String? reason;
  String? description;
  String? status; // 'open', 'investigating', 'resolved', 'closed'
  String? resolution;
  int? resolvedBy;
  List<DisputeMessage>? messages;
  List<String>? evidence;
  String? createdAt;
  String? resolvedAt;

  Dispute({
    this.id,
    this.escrowId,
    this.raisedBy,
    this.reason,
    this.description,
    this.status,
    this.resolution,
    this.resolvedBy,
    this.messages,
    this.evidence,
    this.createdAt,
    this.resolvedAt,
  });

  factory Dispute.fromJson(Map<String, dynamic> json) => Dispute(
        id: json["id"],
        escrowId: json["escrow_id"],
        raisedBy: json["raised_by"],
        reason: json["reason"],
        description: json["description"],
        status: json["status"],
        resolution: json["resolution"],
        resolvedBy: json["resolved_by"],
        messages: json["messages"] == null ? [] : List<DisputeMessage>.from(json["messages"]!.map((x) => DisputeMessage.fromJson(x))),
        evidence: json["evidence"] == null ? [] : List<String>.from(json["evidence"]!.map((x) => x)),
        createdAt: json["created_at"],
        resolvedAt: json["resolved_at"],
      );

  Map<String, dynamic> toJson() => {
        "id": id,
        "escrow_id": escrowId,
        "raised_by": raisedBy,
        "reason": reason,
        "description": description,
        "status": status,
        "resolution": resolution,
        "resolved_by": resolvedBy,
        "messages": messages == null ? [] : List<dynamic>.from(messages!.map((x) => x.toJson())),
        "evidence": evidence == null ? [] : List<dynamic>.from(evidence!.map((x) => x)),
        "created_at": createdAt,
        "resolved_at": resolvedAt,
      };
}

class DisputeMessage {
  int? id;
  int? disputeId;
  int? userId;
  String? message;
  List<String>? attachments;
  String? createdAt;

  DisputeMessage({
    this.id,
    this.disputeId,
    this.userId,
    this.message,
    this.attachments,
    this.createdAt,
  });

  factory DisputeMessage.fromJson(Map<String, dynamic> json) => DisputeMessage(
        id: json["id"],
        disputeId: json["dispute_id"],
        userId: json["user_id"],
        message: json["message"],
        attachments: json["attachments"] == null ? [] : List<String>.from(json["attachments"]!.map((x) => x)),
        createdAt: json["created_at"],
      );

  Map<String, dynamic> toJson() => {
        "id": id,
        "dispute_id": disputeId,
        "user_id": userId,
        "message": message,
        "attachments": attachments == null ? [] : List<dynamic>.from(attachments!.map((x) => x)),
        "created_at": createdAt,
      };
}

class EscrowStats {
  int? totalTransactions;
  String? totalAmount;
  int? completedTransactions;
  int? pendingTransactions;
  int? disputedTransactions;
  String? successRate;

  EscrowStats({
    this.totalTransactions,
    this.totalAmount,
    this.completedTransactions,
    this.pendingTransactions,
    this.disputedTransactions,
    this.successRate,
  });

  factory EscrowStats.fromJson(Map<String, dynamic> json) => EscrowStats(
        totalTransactions: json["total_transactions"],
        totalAmount: json["total_amount"],
        completedTransactions: json["completed_transactions"],
        pendingTransactions: json["pending_transactions"],
        disputedTransactions: json["disputed_transactions"],
        successRate: json["success_rate"],
      );

  Map<String, dynamic> toJson() => {
        "total_transactions": totalTransactions,
        "total_amount": totalAmount,
        "completed_transactions": completedTransactions,
        "pending_transactions": pendingTransactions,
        "disputed_transactions": disputedTransactions,
        "success_rate": successRate,
      };
}

// Request models
class CreateEscrowRequest {
  int sellerId;
  String type;
  String title;
  String description;
  String amount;
  String paymentMethod;
  List<CreateMilestoneRequest>? milestones;
  String? deliveryDate;

  CreateEscrowRequest({
    required this.sellerId,
    required this.type,
    required this.title,
    required this.description,
    required this.amount,
    required this.paymentMethod,
    this.milestones,
    this.deliveryDate,
  });

  Map<String, dynamic> toJson() => {
        "seller_id": sellerId,
        "type": type,
        "title": title,
        "description": description,
        "amount": amount,
        "payment_method": paymentMethod,
        if (milestones != null) "milestones": milestones!.map((x) => x.toJson()).toList(),
        if (deliveryDate != null) "delivery_date": deliveryDate,
      };
}

class CreateMilestoneRequest {
  String title;
  String description;
  String amount;
  String? dueDate;

  CreateMilestoneRequest({
    required this.title,
    required this.description,
    required this.amount,
    this.dueDate,
  });

  Map<String, dynamic> toJson() => {
        "title": title,
        "description": description,
        "amount": amount,
        if (dueDate != null) "due_date": dueDate,
      };
}

class RaiseDisputeRequest {
  int escrowId;
  String reason;
  String description;
  List<String>? evidence;

  RaiseDisputeRequest({
    required this.escrowId,
    required this.reason,
    required this.description,
    this.evidence,
  });

  Map<String, dynamic> toJson() => {
        "escrow_id": escrowId,
        "reason": reason,
        "description": description,
        if (evidence != null) "evidence": evidence,
      };
}
