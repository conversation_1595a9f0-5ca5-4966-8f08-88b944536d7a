<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Account Tiers Table
        Schema::create('account_tiers', function (Blueprint $table) {
            $table->id();
            $table->string('name');
            $table->integer('level')->unique();
            $table->decimal('daily_limit', 28, 8);
            $table->decimal('monthly_limit', 28, 8);
            $table->decimal('single_transaction_limit', 28, 8);
            $table->json('kyc_requirements')->nullable();
            $table->json('features')->nullable();
            $table->json('benefits')->nullable();
            $table->string('color_code', 7)->nullable(); // Hex color for UI
            $table->string('icon')->nullable();
            $table->boolean('is_active')->default(true);
            $table->timestamps();
            
            $table->index('level');
        });

        // User Referrals Table
        Schema::create('user_referrals', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('referrer_id');
            $table->unsignedBigInteger('referee_id');
            $table->string('referral_code', 20)->unique();
            $table->enum('status', ['pending', 'qualified', 'rewarded', 'expired'])->default('pending');
            $table->decimal('referrer_reward', 28, 8)->default(0);
            $table->decimal('referee_reward', 28, 8)->default(0);
            $table->json('qualification_criteria')->nullable();
            $table->timestamp('qualification_date')->nullable();
            $table->timestamp('reward_paid_date')->nullable();
            $table->string('reward_trx', 40)->nullable();
            $table->timestamps();
            
            $table->foreign('referrer_id')->references('id')->on('users')->onDelete('cascade');
            $table->foreign('referee_id')->references('id')->on('users')->onDelete('cascade');
            $table->index(['referrer_id', 'status']);
            $table->index('referral_code');
        });

        // Admin Configurable Rates Table
        Schema::create('admin_rates', function (Blueprint $table) {
            $table->id();
            $table->string('category'); // savings, investment, loan, card, transaction
            $table->string('type'); // fixed_deposit, target_saving, etc.
            $table->string('name');
            $table->decimal('rate', 8, 4);
            $table->string('unit')->default('percent'); // percent, fixed, per_transaction
            $table->text('description')->nullable();
            $table->boolean('is_percentage')->default(true);
            $table->boolean('is_active')->default(true);
            $table->decimal('min_value', 8, 4)->nullable();
            $table->decimal('max_value', 8, 4)->nullable();
            $table->timestamps();
            
            $table->unique(['category', 'type', 'name']);
            $table->index(['category', 'is_active']);
        });

        // User Account Tier History Table
        Schema::create('user_tier_history', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('user_id');
            $table->unsignedBigInteger('from_tier_id')->nullable();
            $table->unsignedBigInteger('to_tier_id');
            $table->enum('reason', ['registration', 'kyc_upgrade', 'transaction_volume', 'manual_upgrade', 'downgrade']);
            $table->text('notes')->nullable();
            $table->unsignedBigInteger('processed_by')->nullable(); // Admin ID
            $table->timestamps();
            
            $table->foreign('user_id')->references('id')->on('users')->onDelete('cascade');
            $table->foreign('from_tier_id')->references('id')->on('account_tiers')->onDelete('set null');
            $table->foreign('to_tier_id')->references('id')->on('account_tiers')->onDelete('cascade');
            $table->foreign('processed_by')->references('id')->on('admins')->onDelete('set null');
            $table->index(['user_id', 'to_tier_id']);
        });

        // Referral Campaigns Table
        Schema::create('referral_campaigns', function (Blueprint $table) {
            $table->id();
            $table->string('name');
            $table->text('description')->nullable();
            $table->decimal('referrer_reward', 28, 8);
            $table->decimal('referee_reward', 28, 8);
            $table->json('qualification_criteria');
            $table->date('start_date');
            $table->date('end_date')->nullable();
            $table->integer('max_referrals_per_user')->nullable();
            $table->integer('total_budget')->nullable();
            $table->integer('used_budget')->default(0);
            $table->boolean('is_active')->default(true);
            $table->timestamps();
            
            $table->index(['is_active', 'start_date', 'end_date']);
        });

        // User Insights Table
        Schema::create('user_insights', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('user_id');
            $table->date('date');
            $table->decimal('total_balance', 28, 8)->default(0);
            $table->decimal('savings_balance', 28, 8)->default(0);
            $table->decimal('investment_balance', 28, 8)->default(0);
            $table->decimal('daily_spending', 28, 8)->default(0);
            $table->decimal('monthly_spending', 28, 8)->default(0);
            $table->decimal('daily_income', 28, 8)->default(0);
            $table->decimal('monthly_income', 28, 8)->default(0);
            $table->integer('transaction_count')->default(0);
            $table->json('spending_categories')->nullable();
            $table->json('income_sources')->nullable();
            $table->decimal('financial_health_score', 5, 2)->default(0);
            $table->timestamps();
            
            $table->foreign('user_id')->references('id')->on('users')->onDelete('cascade');
            $table->unique(['user_id', 'date']);
            $table->index(['user_id', 'date']);
        });

        // Notification Preferences Table
        Schema::create('user_notification_preferences', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('user_id');
            $table->boolean('email_notifications')->default(true);
            $table->boolean('sms_notifications')->default(true);
            $table->boolean('push_notifications')->default(true);
            $table->boolean('transaction_alerts')->default(true);
            $table->boolean('savings_reminders')->default(true);
            $table->boolean('investment_updates')->default(true);
            $table->boolean('loan_reminders')->default(true);
            $table->boolean('promotional_offers')->default(true);
            $table->boolean('security_alerts')->default(true);
            $table->json('quiet_hours')->nullable(); // Start and end time for no notifications
            $table->timestamps();
            
            $table->foreign('user_id')->references('id')->on('users')->onDelete('cascade');
            $table->unique('user_id');
        });

        // System Settings Table
        Schema::create('system_settings', function (Blueprint $table) {
            $table->id();
            $table->string('key')->unique();
            $table->text('value');
            $table->string('type')->default('string'); // string, number, boolean, json
            $table->text('description')->nullable();
            $table->string('category')->nullable();
            $table->boolean('is_public')->default(false); // Can be accessed via API
            $table->timestamps();
            
            $table->index(['category', 'is_public']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('system_settings');
        Schema::dropIfExists('user_notification_preferences');
        Schema::dropIfExists('user_insights');
        Schema::dropIfExists('referral_campaigns');
        Schema::dropIfExists('user_tier_history');
        Schema::dropIfExists('admin_rates');
        Schema::dropIfExists('user_referrals');
        Schema::dropIfExists('account_tiers');
    }
};
