import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:viserpay/core/utils/dimensions.dart';
import 'package:viserpay/core/utils/my_color.dart';
import 'package:viserpay/core/utils/style.dart';
import 'package:viserpay/data/controller/account/account_manager_controller.dart';
import 'package:viserpay/view/components/card/enhanced_card.dart';

class AccountSwitcher extends StatelessWidget {
  final bool showCreateOptions;
  
  const AccountSwitcher({
    super.key,
    this.showCreateOptions = true,
  });

  @override
  Widget build(BuildContext context) {
    return GetBuilder<AccountManagerController>(
      builder: (accountController) => Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Current Account Header
          _buildCurrentAccountHeader(accountController),
          
          const SizedBox(height: Dimensions.space20),
          
          // Available Accounts
          if (accountController.userAccounts.length > 1) ...[
            Text(
              'Switch Account',
              style: semiBoldLarge.copyWith(
                color: MyColor.primaryTextColor,
              ),
            ),
            const SizedBox(height: Dimensions.space15),
            _buildAccountsList(accountController),
            const SizedBox(height: Dimensions.space20),
          ],
          
          // Create New Account Options
          if (showCreateOptions) ...[
            Text(
              'Create New Account',
              style: semiBoldLarge.copyWith(
                color: MyColor.primaryTextColor,
              ),
            ),
            const SizedBox(height: Dimensions.space15),
            _buildCreateAccountOptions(accountController),
          ],
        ],
      ),
    );
  }

  Widget _buildCurrentAccountHeader(AccountManagerController controller) {
    final currentAccount = controller.currentAccount.value;
    if (currentAccount == null) return const SizedBox.shrink();

    return EnhancedCard(
      hasGradient: true,
      gradient: LinearGradient(
        colors: [
          controller.getAccountTypeColor(currentAccount.type),
          controller.getAccountTypeColor(currentAccount.type).withOpacity(0.7),
        ],
        begin: Alignment.topLeft,
        end: Alignment.bottomRight,
      ),
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(Dimensions.space12),
            decoration: BoxDecoration(
              color: MyColor.brandWhite.withOpacity(0.2),
              borderRadius: BorderRadius.circular(Dimensions.mediumRadius),
            ),
            child: Icon(
              controller.getAccountTypeIcon(currentAccount.type),
              color: MyColor.brandWhite,
              size: 24,
            ),
          ),
          const SizedBox(width: Dimensions.space15),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Current Account',
                  style: regularSmall.copyWith(
                    color: MyColor.brandWhite.withOpacity(0.8),
                  ),
                ),
                const SizedBox(height: Dimensions.space5),
                Text(
                  currentAccount.name,
                  style: semiBoldLarge.copyWith(
                    color: MyColor.brandWhite,
                  ),
                ),
                Text(
                  controller.getAccountTypeName(currentAccount.type),
                  style: regularDefault.copyWith(
                    color: MyColor.brandWhite.withOpacity(0.9),
                  ),
                ),
              ],
            ),
          ),
          Icon(
            Icons.check_circle,
            color: MyColor.brandWhite,
            size: 20,
          ),
        ],
      ),
    );
  }

  Widget _buildAccountsList(AccountManagerController controller) {
    final otherAccounts = controller.userAccounts
        .where((account) => account.id != controller.currentAccount.value?.id)
        .toList();

    return Column(
      children: otherAccounts.map((account) => 
        _buildAccountCard(account, controller)
      ).toList(),
    );
  }

  Widget _buildAccountCard(AccountInfo account, AccountManagerController controller) {
    return Padding(
      padding: const EdgeInsets.only(bottom: Dimensions.space10),
      child: EnhancedCard(
        onTap: () => controller.switchAccount(account),
        isClickable: true,
        borderColor: controller.getAccountTypeColor(account.type).withOpacity(0.3),
        child: Row(
          children: [
            Container(
              padding: const EdgeInsets.all(Dimensions.space10),
              decoration: BoxDecoration(
                color: controller.getAccountTypeColor(account.type).withOpacity(0.1),
                borderRadius: BorderRadius.circular(Dimensions.buttonRadius),
              ),
              child: Icon(
                controller.getAccountTypeIcon(account.type),
                color: controller.getAccountTypeColor(account.type),
                size: 20,
              ),
            ),
            const SizedBox(width: Dimensions.space15),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    account.name,
                    style: mediumDefault.copyWith(
                      color: MyColor.primaryTextColor,
                    ),
                  ),
                  Text(
                    controller.getAccountTypeName(account.type),
                    style: regularSmall.copyWith(
                      color: MyColor.contentTextColor,
                    ),
                  ),
                ],
              ),
            ),
            Icon(
              Icons.arrow_forward_ios,
              color: MyColor.contentTextColor,
              size: 16,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildCreateAccountOptions(AccountManagerController controller) {
    return Column(
      children: [
        // Create Business Account
        if (controller.canCreateBusinessAccount())
          _buildCreateAccountOption(
            title: 'Business Account',
            subtitle: 'For business transactions and management',
            icon: Icons.business,
            color: MyColor.brandBlack,
            onTap: () => _showCreateBusinessDialog(controller),
          ),
        
        // Create Kids Account
        if (controller.canCreateKidsAccount()) ...[
          if (controller.canCreateBusinessAccount())
            const SizedBox(height: Dimensions.space10),
          _buildCreateAccountOption(
            title: 'Kids Account',
            subtitle: 'Managed account for children',
            icon: Icons.child_care,
            color: const Color(0xffFF6B6B),
            onTap: () => _showCreateKidsDialog(controller),
          ),
        ],
      ],
    );
  }

  Widget _buildCreateAccountOption({
    required String title,
    required String subtitle,
    required IconData icon,
    required Color color,
    required VoidCallback onTap,
  }) {
    return EnhancedCard(
      onTap: onTap,
      isClickable: true,
      borderColor: color.withOpacity(0.3),
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(Dimensions.space10),
            decoration: BoxDecoration(
              color: color.withOpacity(0.1),
              borderRadius: BorderRadius.circular(Dimensions.buttonRadius),
            ),
            child: Icon(
              icon,
              color: color,
              size: 20,
            ),
          ),
          const SizedBox(width: Dimensions.space15),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: mediumDefault.copyWith(
                    color: MyColor.primaryTextColor,
                  ),
                ),
                Text(
                  subtitle,
                  style: regularSmall.copyWith(
                    color: MyColor.contentTextColor,
                  ),
                ),
              ],
            ),
          ),
          Icon(
            Icons.add_circle_outline,
            color: color,
            size: 20,
          ),
        ],
      ),
    );
  }

  void _showCreateBusinessDialog(AccountManagerController controller) {
    final TextEditingController nameController = TextEditingController();
    
    Get.dialog(
      AlertDialog(
        title: const Text('Create Business Account'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const Text('Enter your business name to create a business account.'),
            const SizedBox(height: Dimensions.space15),
            TextField(
              controller: nameController,
              decoration: const InputDecoration(
                labelText: 'Business Name',
                hintText: 'Enter business name',
                border: OutlineInputBorder(),
              ),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () async {
              if (nameController.text.trim().isNotEmpty) {
                final success = await controller.createBusinessAccount(
                  nameController.text.trim(),
                );
                if (success) {
                  Get.back();
                }
              }
            },
            child: const Text('Create'),
          ),
        ],
      ),
    );
  }

  void _showCreateKidsDialog(AccountManagerController controller) {
    final TextEditingController nameController = TextEditingController();
    final TextEditingController ageController = TextEditingController();
    
    Get.dialog(
      AlertDialog(
        title: const Text('Create Kids Account'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const Text('Create a managed account for your child.'),
            const SizedBox(height: Dimensions.space15),
            TextField(
              controller: nameController,
              decoration: const InputDecoration(
                labelText: "Child's Name",
                hintText: 'Enter child name',
                border: OutlineInputBorder(),
              ),
            ),
            const SizedBox(height: Dimensions.space10),
            TextField(
              controller: ageController,
              keyboardType: TextInputType.number,
              decoration: const InputDecoration(
                labelText: 'Age',
                hintText: 'Enter age',
                border: OutlineInputBorder(),
              ),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () async {
              if (nameController.text.trim().isNotEmpty && 
                  ageController.text.trim().isNotEmpty) {
                final age = int.tryParse(ageController.text.trim()) ?? 0;
                if (age > 0) {
                  final success = await controller.createKidsAccount(
                    nameController.text.trim(),
                    age,
                  );
                  if (success) {
                    Get.back();
                  }
                }
              }
            },
            child: const Text('Create'),
          ),
        ],
      ),
    );
  }
}
