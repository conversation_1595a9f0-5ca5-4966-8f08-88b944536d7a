<?php

/*
 * This file is part of the Symfony package.
 *
 * (c) <PERSON><PERSON><PERSON> <<EMAIL>>
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

namespace Symfony\Component\Validator\Constraints;

use Symfony\Component\Validator\Exception\ConstraintDefinitionException;

/**
 * @internal
 *
 * <AUTHOR> <jan.scha<PERSON><PERSON>@sensiolabs.de>
 * <AUTHOR> <<EMAIL>>
 */
trait ZeroComparisonConstraintTrait
{
    public function __construct(?array $options = null, ?string $message = null, ?array $groups = null, mixed $payload = null)
    {
        $options ??= [];

        if (isset($options['propertyPath'])) {
            throw new ConstraintDefinitionException(sprintf('The "propertyPath" option of the "%s" constraint cannot be set.', static::class));
        }

        if (isset($options['value'])) {
            throw new ConstraintDefinitionException(sprintf('The "value" option of the "%s" constraint cannot be set.', static::class));
        }

        parent::__construct(0, null, $message, $groups, $payload, $options);
    }

    public function validatedBy(): string
    {
        return parent::class.'Validator';
    }
}
