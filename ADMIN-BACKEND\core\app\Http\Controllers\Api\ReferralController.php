<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\User;
use App\Models\Referral;
use App\Models\ReferralReward;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Str;

class ReferralController extends Controller
{
    public function __construct()
    {
        $this->middleware('auth:api');
    }

    /**
     * Get user's referral code
     */
    public function getReferralCode(): JsonResponse
    {
        $user = Auth::user();
        
        // Generate referral code if not exists
        if (empty($user->referral_code)) {
            $user->referral_code = $this->generateUniqueReferralCode();
            $user->save();
        }

        $referralLink = config('app.url') . '/register?ref=' . $user->referral_code;

        return response()->json([
            'status' => 'success',
            'data' => [
                'referral_code' => $user->referral_code,
                'referral_link' => $referralLink,
                'qr_code_url' => $this->generateQRCodeUrl($referralLink),
                'share_message' => $this->getShareMessage($user->referral_code)
            ]
        ]);
    }

    /**
     * Use referral code during registration
     */
    public function useReferralCode(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'referral_code' => 'required|string|exists:users,referral_code'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'status' => 'error',
                'message' => 'Invalid referral code',
                'errors' => $validator->errors()
            ], 422);
        }

        $user = Auth::user();
        $referralCode = $request->referral_code;

        // Check if user already used a referral code
        if (!empty($user->referred_by)) {
            return response()->json([
                'status' => 'error',
                'message' => 'You have already used a referral code'
            ], 400);
        }

        // Find referrer
        $referrer = User::where('referral_code', $referralCode)->first();
        
        if (!$referrer) {
            return response()->json([
                'status' => 'error',
                'message' => 'Invalid referral code'
            ], 400);
        }

        // Prevent self-referral
        if ($referrer->id === $user->id) {
            return response()->json([
                'status' => 'error',
                'message' => 'You cannot refer yourself'
            ], 400);
        }

        // Create referral record
        $referral = Referral::create([
            'referrer_id' => $referrer->id,
            'referee_id' => $user->id,
            'referral_code' => $referralCode,
            'status' => 'pending'
        ]);

        // Update user's referred_by field
        $user->referred_by = $referrer->id;
        $user->save();

        return response()->json([
            'status' => 'success',
            'message' => 'Referral code applied successfully',
            'data' => [
                'referrer_name' => $referrer->firstname . ' ' . $referrer->lastname,
                'bonus_info' => $this->getReferralBonusInfo()
            ]
        ]);
    }

    /**
     * Get referral statistics
     */
    public function getReferralStats(): JsonResponse
    {
        $user = Auth::user();
        
        $referrals = Referral::where('referrer_id', $user->id)->get();
        $rewards = ReferralReward::where('user_id', $user->id)->get();

        $stats = [
            'total_referrals' => $referrals->count(),
            'pending_referrals' => $referrals->where('status', 'pending')->count(),
            'qualified_referrals' => $referrals->where('status', 'qualified')->count(),
            'rewarded_referrals' => $referrals->where('status', 'rewarded')->count(),
            'total_rewards_earned' => $rewards->sum('amount'),
            'pending_rewards' => $rewards->where('status', 'pending')->sum('amount'),
            'paid_rewards' => $rewards->where('status', 'paid')->sum('amount'),
            'referral_tier' => $this->getUserReferralTier($referrals->count()),
            'next_tier_requirements' => $this->getNextTierRequirements($referrals->count()),
            'recent_referrals' => $this->getRecentReferrals($user->id),
            'monthly_referral_trend' => $this->getMonthlyReferralTrend($user->id)
        ];

        return response()->json([
            'status' => 'success',
            'data' => $stats
        ]);
    }

    /**
     * Get referral leaderboard
     */
    public function getLeaderboard(Request $request): JsonResponse
    {
        $period = $request->get('period', 'all_time'); // all_time, monthly, weekly
        $limit = $request->get('limit', 50);

        $query = Referral::selectRaw('referrer_id, COUNT(*) as referral_count')
                        ->where('status', 'qualified')
                        ->groupBy('referrer_id');

        // Apply period filter
        if ($period === 'monthly') {
            $query->whereMonth('created_at', now()->month)
                  ->whereYear('created_at', now()->year);
        } elseif ($period === 'weekly') {
            $query->whereBetween('created_at', [now()->startOfWeek(), now()->endOfWeek()]);
        }

        $leaderboard = $query->orderByDesc('referral_count')
                           ->limit($limit)
                           ->with('referrer:id,firstname,lastname,image')
                           ->get()
                           ->map(function ($item, $index) {
                               return [
                                   'rank' => $index + 1,
                                   'user' => $item->referrer,
                                   'referral_count' => $item->referral_count,
                                   'tier' => $this->getUserReferralTier($item->referral_count),
                                   'estimated_earnings' => $this->calculateEstimatedEarnings($item->referral_count)
                               ];
                           });

        // Get current user's position
        $userPosition = $this->getUserLeaderboardPosition(Auth::id(), $period);

        return response()->json([
            'status' => 'success',
            'data' => [
                'leaderboard' => $leaderboard,
                'user_position' => $userPosition,
                'period' => $period,
                'total_participants' => $leaderboard->count()
            ]
        ]);
    }

    /**
     * Get referral rewards history
     */
    public function getRewardsHistory(): JsonResponse
    {
        $user = Auth::user();
        
        $rewards = ReferralReward::where('user_id', $user->id)
                                ->with('referral.referee:id,firstname,lastname')
                                ->orderBy('created_at', 'desc')
                                ->paginate(20);

        return response()->json([
            'status' => 'success',
            'data' => $rewards
        ]);
    }

    /**
     * Share referral code via different channels
     */
    public function shareReferral(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'channel' => 'required|in:whatsapp,telegram,twitter,facebook,email,sms',
            'recipient' => 'nullable|string'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'status' => 'error',
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], 422);
        }

        $user = Auth::user();
        $channel = $request->channel;
        $referralLink = config('app.url') . '/register?ref=' . $user->referral_code;
        $message = $this->getShareMessage($user->referral_code);

        $shareUrls = [
            'whatsapp' => "https://wa.me/?text=" . urlencode($message . " " . $referralLink),
            'telegram' => "https://t.me/share/url?url=" . urlencode($referralLink) . "&text=" . urlencode($message),
            'twitter' => "https://twitter.com/intent/tweet?text=" . urlencode($message) . "&url=" . urlencode($referralLink),
            'facebook' => "https://www.facebook.com/sharer/sharer.php?u=" . urlencode($referralLink),
            'email' => "mailto:?subject=" . urlencode("Join KojaPay with my referral") . "&body=" . urlencode($message . " " . $referralLink),
            'sms' => "sms:?body=" . urlencode($message . " " . $referralLink)
        ];

        return response()->json([
            'status' => 'success',
            'data' => [
                'share_url' => $shareUrls[$channel] ?? null,
                'message' => $message,
                'referral_link' => $referralLink
            ]
        ]);
    }

    // Private helper methods
    private function generateUniqueReferralCode(): string
    {
        do {
            $code = strtoupper(Str::random(8));
        } while (User::where('referral_code', $code)->exists());

        return $code;
    }

    private function generateQRCodeUrl($link): string
    {
        return "https://api.qrserver.com/v1/create-qr-code/?size=200x200&data=" . urlencode($link);
    }

    private function getShareMessage($referralCode): string
    {
        return "🎉 Join me on KojaPay and start saving & investing smartly! Use my referral code {$referralCode} and we both get rewarded. Download the app now!";
    }

    private function getReferralBonusInfo(): array
    {
        return [
            'referee_bonus' => 1000, // ₦1,000 for new user
            'referrer_bonus' => 500,  // ₦500 for referrer
            'qualification_requirements' => [
                'Complete KYC verification',
                'Make first deposit of ₦5,000 or more',
                'Keep account active for 30 days'
            ]
        ];
    }

    private function getUserReferralTier($referralCount): array
    {
        if ($referralCount >= 100) {
            return ['name' => 'Diamond', 'level' => 5, 'bonus_multiplier' => 2.0];
        } elseif ($referralCount >= 50) {
            return ['name' => 'Platinum', 'level' => 4, 'bonus_multiplier' => 1.5];
        } elseif ($referralCount >= 25) {
            return ['name' => 'Gold', 'level' => 3, 'bonus_multiplier' => 1.3];
        } elseif ($referralCount >= 10) {
            return ['name' => 'Silver', 'level' => 2, 'bonus_multiplier' => 1.2];
        } else {
            return ['name' => 'Bronze', 'level' => 1, 'bonus_multiplier' => 1.0];
        }
    }

    private function getNextTierRequirements($currentReferrals): ?array
    {
        if ($currentReferrals < 10) {
            return ['tier' => 'Silver', 'referrals_needed' => 10 - $currentReferrals];
        } elseif ($currentReferrals < 25) {
            return ['tier' => 'Gold', 'referrals_needed' => 25 - $currentReferrals];
        } elseif ($currentReferrals < 50) {
            return ['tier' => 'Platinum', 'referrals_needed' => 50 - $currentReferrals];
        } elseif ($currentReferrals < 100) {
            return ['tier' => 'Diamond', 'referrals_needed' => 100 - $currentReferrals];
        }
        
        return null; // Already at highest tier
    }

    private function getRecentReferrals($userId): array
    {
        return Referral::where('referrer_id', $userId)
                      ->with('referee:id,firstname,lastname,created_at')
                      ->orderBy('created_at', 'desc')
                      ->limit(10)
                      ->get()
                      ->toArray();
    }

    private function getMonthlyReferralTrend($userId): array
    {
        $months = [];
        for ($i = 11; $i >= 0; $i--) {
            $date = now()->subMonths($i);
            $count = Referral::where('referrer_id', $userId)
                           ->whereMonth('created_at', $date->month)
                           ->whereYear('created_at', $date->year)
                           ->count();
            
            $months[] = [
                'month' => $date->format('M Y'),
                'referrals' => $count
            ];
        }
        
        return $months;
    }

    private function getUserLeaderboardPosition($userId, $period): ?array
    {
        $query = Referral::selectRaw('referrer_id, COUNT(*) as referral_count')
                        ->where('status', 'qualified')
                        ->groupBy('referrer_id');

        if ($period === 'monthly') {
            $query->whereMonth('created_at', now()->month)
                  ->whereYear('created_at', now()->year);
        } elseif ($period === 'weekly') {
            $query->whereBetween('created_at', [now()->startOfWeek(), now()->endOfWeek()]);
        }

        $leaderboard = $query->orderByDesc('referral_count')->get();
        
        $userPosition = $leaderboard->search(function ($item) use ($userId) {
            return $item->referrer_id == $userId;
        });

        if ($userPosition !== false) {
            $userStats = $leaderboard[$userPosition];
            return [
                'rank' => $userPosition + 1,
                'referral_count' => $userStats->referral_count,
                'tier' => $this->getUserReferralTier($userStats->referral_count)
            ];
        }

        return null;
    }

    private function calculateEstimatedEarnings($referralCount): float
    {
        $baseReward = 500; // ₦500 per qualified referral
        $tier = $this->getUserReferralTier($referralCount);
        
        return $referralCount * $baseReward * $tier['bonus_multiplier'];
    }
}
