import 'package:flutter/material.dart';
import 'package:viserpay/core/utils/dimensions.dart';
import 'package:viserpay/core/utils/my_color.dart';
import 'package:viserpay/core/utils/style.dart';
import 'package:viserpay/data/model/insights/insights_model.dart';

class ChartWidget extends StatelessWidget {
  final ChartData chartData;
  final double height;

  const ChartWidget({
    super.key,
    required this.chartData,
    this.height = 200,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      height: height,
      padding: const EdgeInsets.all(Dimensions.space20),
      decoration: BoxDecoration(
        color: MyColor.colorWhite,
        borderRadius: BorderRadius.circular(Dimensions.cardRadius),
        boxShadow: [
          BoxShadow(
            color: MyColor.colorGrey.withOpacity(0.1),
            blurRadius: 15,
            offset: const Offset(0, 5),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Chart Header
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                chartData.title ?? 'Chart',
                style: semiBoldLarge.copyWith(
                  color: MyColor.colorBlack,
                  fontSize: 16,
                ),
              ),
              Container(
                padding: const EdgeInsets.symmetric(
                  horizontal: Dimensions.space8,
                  vertical: Dimensions.space4,
                ),
                decoration: BoxDecoration(
                  color: MyColor.primaryColor.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(Dimensions.smallRadius),
                ),
                child: Text(
                  chartData.period ?? 'Period',
                  style: regularSmall.copyWith(
                    color: MyColor.primaryColor,
                    fontSize: 10,
                  ),
                ),
              ),
            ],
          ),

          const SizedBox(height: Dimensions.space20),

          // Chart Content
          Expanded(
            child: _buildChart(),
          ),
        ],
      ),
    );
  }

  Widget _buildChart() {
    switch (chartData.type?.toLowerCase()) {
      case 'pie':
      case 'doughnut':
        return _buildPieChart();
      case 'bar':
        return _buildBarChart();
      case 'line':
      default:
        return _buildLineChart();
    }
  }

  Widget _buildLineChart() {
    List<DataPoint> dataPoints = chartData.dataPoints ?? [];
    if (dataPoints.isEmpty) {
      return _buildEmptyChart();
    }

    return CustomPaint(
      size: Size.infinite,
      painter: LineChartPainter(dataPoints),
    );
  }

  Widget _buildBarChart() {
    List<DataPoint> dataPoints = chartData.dataPoints ?? [];
    if (dataPoints.isEmpty) {
      return _buildEmptyChart();
    }

    return Row(
      crossAxisAlignment: CrossAxisAlignment.end,
      children: dataPoints.map((point) => _buildBarItem(point, dataPoints)).toList(),
    );
  }

  Widget _buildBarItem(DataPoint point, List<DataPoint> allPoints) {
    double maxValue = allPoints.map((p) => p.value ?? 0).reduce((a, b) => a > b ? a : b);
    double normalizedHeight = maxValue > 0 ? (point.value ?? 0) / maxValue : 0;
    
    Color barColor = _parseColor(point.color) ?? MyColor.primaryColor;

    return Expanded(
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 2),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.end,
          children: [
            Container(
              height: normalizedHeight * 100,
              decoration: BoxDecoration(
                color: barColor,
                borderRadius: const BorderRadius.only(
                  topLeft: Radius.circular(4),
                  topRight: Radius.circular(4),
                ),
              ),
            ),
            const SizedBox(height: Dimensions.space5),
            Text(
              point.label ?? '',
              style: regularSmall.copyWith(
                color: MyColor.colorGrey,
                fontSize: 9,
              ),
              textAlign: TextAlign.center,
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildPieChart() {
    List<DataPoint> dataPoints = chartData.dataPoints ?? [];
    if (dataPoints.isEmpty) {
      return _buildEmptyChart();
    }

    return Row(
      children: [
        // Pie Chart
        Expanded(
          flex: 2,
          child: AspectRatio(
            aspectRatio: 1,
            child: CustomPaint(
              painter: PieChartPainter(dataPoints),
            ),
          ),
        ),
        
        const SizedBox(width: Dimensions.space15),
        
        // Legend
        Expanded(
          flex: 1,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: dataPoints.take(5).map((point) => _buildLegendItem(point)).toList(),
          ),
        ),
      ],
    );
  }

  Widget _buildLegendItem(DataPoint point) {
    Color itemColor = _parseColor(point.color) ?? MyColor.primaryColor;
    
    return Padding(
      padding: const EdgeInsets.only(bottom: Dimensions.space8),
      child: Row(
        children: [
          Container(
            width: 12,
            height: 12,
            decoration: BoxDecoration(
              color: itemColor,
              shape: BoxShape.circle,
            ),
          ),
          const SizedBox(width: Dimensions.space8),
          Expanded(
            child: Text(
              point.label ?? '',
              style: regularSmall.copyWith(
                color: MyColor.colorGrey,
                fontSize: 10,
              ),
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildEmptyChart() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.bar_chart,
            color: MyColor.colorGrey.withOpacity(0.5),
            size: 40,
          ),
          const SizedBox(height: Dimensions.space10),
          Text(
            'No data available',
            style: regularDefault.copyWith(
              color: MyColor.colorGrey,
              fontSize: 12,
            ),
          ),
        ],
      ),
    );
  }

  Color? _parseColor(String? colorString) {
    if (colorString == null || colorString.isEmpty) return null;
    
    try {
      if (colorString.startsWith('#')) {
        return Color(int.parse(colorString.substring(1), radix: 16) + 0xFF000000);
      }
      return null;
    } catch (e) {
      return null;
    }
  }
}

class LineChartPainter extends CustomPainter {
  final List<DataPoint> dataPoints;

  LineChartPainter(this.dataPoints);

  @override
  void paint(Canvas canvas, Size size) {
    if (dataPoints.isEmpty) return;

    final paint = Paint()
      ..color = MyColor.primaryColor
      ..strokeWidth = 2
      ..style = PaintingStyle.stroke;

    final path = Path();
    final points = <Offset>[];

    double maxValue = dataPoints.map((p) => p.value ?? 0).reduce((a, b) => a > b ? a : b);
    
    for (int i = 0; i < dataPoints.length; i++) {
      double x = (i / (dataPoints.length - 1)) * size.width;
      double y = size.height - ((dataPoints[i].value ?? 0) / maxValue) * size.height;
      points.add(Offset(x, y));
    }

    if (points.isNotEmpty) {
      path.moveTo(points.first.dx, points.first.dy);
      for (int i = 1; i < points.length; i++) {
        path.lineTo(points[i].dx, points[i].dy);
      }
    }

    canvas.drawPath(path, paint);

    // Draw points
    final pointPaint = Paint()
      ..color = MyColor.primaryColor
      ..style = PaintingStyle.fill;

    for (final point in points) {
      canvas.drawCircle(point, 4, pointPaint);
    }
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => true;
}

class PieChartPainter extends CustomPainter {
  final List<DataPoint> dataPoints;

  PieChartPainter(this.dataPoints);

  @override
  void paint(Canvas canvas, Size size) {
    if (dataPoints.isEmpty) return;

    final center = Offset(size.width / 2, size.height / 2);
    final radius = size.width / 2 * 0.8;

    double total = dataPoints.map((p) => p.value ?? 0).reduce((a, b) => a + b);
    double startAngle = -90 * (3.14159 / 180); // Start from top

    for (int i = 0; i < dataPoints.length; i++) {
      final point = dataPoints[i];
      final sweepAngle = ((point.value ?? 0) / total) * 2 * 3.14159;
      
      final paint = Paint()
        ..color = _parseColor(point.color) ?? _getDefaultColor(i)
        ..style = PaintingStyle.fill;

      canvas.drawArc(
        Rect.fromCircle(center: center, radius: radius),
        startAngle,
        sweepAngle,
        true,
        paint,
      );

      startAngle += sweepAngle;
    }
  }

  Color _parseColor(String? colorString) {
    if (colorString == null || colorString.isEmpty) return MyColor.primaryColor;
    
    try {
      if (colorString.startsWith('#')) {
        return Color(int.parse(colorString.substring(1), radix: 16) + 0xFF000000);
      }
      return MyColor.primaryColor;
    } catch (e) {
      return MyColor.primaryColor;
    }
  }

  Color _getDefaultColor(int index) {
    final colors = [
      MyColor.primaryColor,
      MyColor.colorGreen,
      MyColor.colorOrange,
      MyColor.colorRed,
      MyColor.colorBlue,
      MyColor.colorPurple,
    ];
    return colors[index % colors.length];
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => true;
}
