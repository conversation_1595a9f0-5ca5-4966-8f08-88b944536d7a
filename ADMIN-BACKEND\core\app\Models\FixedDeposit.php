<?php

namespace App\Models;

use App\Traits\Searchable;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Carbon\Carbon;

class FixedDeposit extends Model
{
    use Searchable;

    protected $fillable = [
        'user_id',
        'amount',
        'duration_days',
        'interest_rate',
        'start_date',
        'maturity_date',
        'accrued_interest',
        'status',
        'penalty_rate',
        'auto_renewal'
    ];

    protected $casts = [
        'amount' => 'decimal:8',
        'interest_rate' => 'decimal:2',
        'accrued_interest' => 'decimal:8',
        'penalty_rate' => 'decimal:2',
        'auto_renewal' => 'boolean',
        'start_date' => 'date',
        'maturity_date' => 'date'
    ];

    protected $appends = [
        'days_remaining',
        'is_matured',
        'projected_return',
        'current_value',
        'can_break'
    ];

    // Relationships
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    public function transactions(): HasMany
    {
        return $this->hasMany(SavingsTransaction::class, 'savings_id')
                    ->where('savings_type', 'fixed_deposit');
    }

    // Accessors
    public function getDaysRemainingAttribute(): int
    {
        if ($this->status !== 'active') {
            return 0;
        }
        
        return max(0, Carbon::now()->diffInDays($this->maturity_date, false));
    }

    public function getIsMaturedAttribute(): bool
    {
        return Carbon::now()->gte($this->maturity_date);
    }

    public function getProjectedReturnAttribute(): float
    {
        return $this->amount * ($this->interest_rate / 100) * ($this->duration_days / 365);
    }

    public function getCurrentValueAttribute(): float
    {
        return $this->amount + $this->accrued_interest;
    }

    public function getCanBreakAttribute(): bool
    {
        return $this->status === 'active' && !$this->is_matured;
    }

    // Scopes
    public function scopeActive($query)
    {
        return $query->where('status', 'active');
    }

    public function scopeMatured($query)
    {
        return $query->where('status', 'matured');
    }

    public function scopeBroken($query)
    {
        return $query->where('status', 'broken');
    }

    public function scopeForUser($query, $userId)
    {
        return $query->where('user_id', $userId);
    }

    public function scopeDueForMaturity($query)
    {
        return $query->where('status', 'active')
                    ->where('maturity_date', '<=', Carbon::now()->toDateString());
    }

    public function scopeAutoRenewal($query)
    {
        return $query->where('auto_renewal', true);
    }

    // Methods
    public function calculateAccruedInterest(): float
    {
        if ($this->status !== 'active') {
            return $this->accrued_interest;
        }

        $daysElapsed = Carbon::parse($this->start_date)->diffInDays(Carbon::now());
        $daysElapsed = min($daysElapsed, $this->duration_days); // Don't exceed duration
        
        $dailyRate = $this->interest_rate / 100 / 365;
        return $this->amount * $dailyRate * $daysElapsed;
    }

    public function updateAccruedInterest(): void
    {
        $this->accrued_interest = $this->calculateAccruedInterest();
        $this->save();
    }

    public function breakDeposit(): array
    {
        if (!$this->can_break) {
            return [
                'success' => false,
                'message' => 'This deposit cannot be broken'
            ];
        }

        $penalty = $this->accrued_interest * ($this->penalty_rate / 100);
        $netInterest = $this->accrued_interest - $penalty;
        $totalAmount = $this->amount + $netInterest;

        $this->status = 'broken';
        $this->save();

        // Create transaction record
        SavingsTransaction::create([
            'user_id' => $this->user_id,
            'savings_type' => 'fixed_deposit',
            'savings_id' => $this->id,
            'type' => 'withdrawal',
            'amount' => $totalAmount,
            'balance_before' => $this->amount + $this->accrued_interest,
            'balance_after' => 0,
            'description' => 'Fixed deposit broken early with penalty',
            'trx' => getTrx()
        ]);

        if ($penalty > 0) {
            SavingsTransaction::create([
                'user_id' => $this->user_id,
                'savings_type' => 'fixed_deposit',
                'savings_id' => $this->id,
                'type' => 'penalty',
                'amount' => $penalty,
                'balance_before' => $this->accrued_interest,
                'balance_after' => $netInterest,
                'description' => 'Early withdrawal penalty',
                'trx' => getTrx()
            ]);
        }

        return [
            'success' => true,
            'amount_returned' => $totalAmount,
            'penalty_charged' => $penalty,
            'net_interest' => $netInterest
        ];
    }

    public function mature(): array
    {
        if ($this->status !== 'active') {
            return [
                'success' => false,
                'message' => 'Deposit is not active'
            ];
        }

        $this->updateAccruedInterest();
        $totalAmount = $this->amount + $this->accrued_interest;
        
        $this->status = 'matured';
        $this->save();

        // Create transaction record
        SavingsTransaction::create([
            'user_id' => $this->user_id,
            'savings_type' => 'fixed_deposit',
            'savings_id' => $this->id,
            'type' => 'interest',
            'amount' => $this->accrued_interest,
            'balance_before' => $this->amount,
            'balance_after' => $totalAmount,
            'description' => 'Fixed deposit matured',
            'trx' => getTrx()
        ]);

        return [
            'success' => true,
            'total_amount' => $totalAmount,
            'principal' => $this->amount,
            'interest_earned' => $this->accrued_interest
        ];
    }

    public function renew($newDuration = null, $newRate = null): FixedDeposit
    {
        $this->mature();
        
        $newDeposit = self::create([
            'user_id' => $this->user_id,
            'amount' => $this->current_value,
            'duration_days' => $newDuration ?? $this->duration_days,
            'interest_rate' => $newRate ?? $this->interest_rate,
            'start_date' => Carbon::now()->toDateString(),
            'maturity_date' => Carbon::now()->addDays($newDuration ?? $this->duration_days)->toDateString(),
            'penalty_rate' => $this->penalty_rate,
            'auto_renewal' => $this->auto_renewal
        ]);

        return $newDeposit;
    }

    public static function getInterestRates(): array
    {
        return [
            30 => 15.0,   // 30 days - 15%
            60 => 16.0,   // 60 days - 16%
            90 => 17.0,   // 90 days - 17%
            180 => 19.0,  // 180 days - 19%
            365 => 22.0   // 365 days - 22%
        ];
    }

    public static function getMinimumAmount(): float
    {
        return 10000; // ₦10,000 minimum
    }

    public static function getMaximumAmount(): float
    {
        return 10000000; // ₦10,000,000 maximum
    }
}
