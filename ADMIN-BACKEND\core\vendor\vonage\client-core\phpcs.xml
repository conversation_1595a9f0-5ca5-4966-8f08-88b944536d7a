<?xml version="1.0"?>
<ruleset xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="vendor/squizlabs/php_codesniffer/phpcs.xsd">
    <arg name="basepath" value="."/>
    <arg name="cache" value=".php_cs.cache"/>
    <arg name="colors"/>
    <arg name="extensions" value="php"/>
    <arg name="parallel" value="10"/>

    <arg value="p"/>

    <file>src</file>
    <file>test</file>

    <rule ref="PSR12"/>
</ruleset>
