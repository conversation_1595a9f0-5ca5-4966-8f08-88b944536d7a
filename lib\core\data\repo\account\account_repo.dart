import 'dart:convert';
import 'package:viserpay/core/utils/method.dart';
import 'package:viserpay/core/utils/url_container.dart';
import 'package:viserpay/data/model/account/account_model.dart';
import 'package:viserpay/data/model/global/response_model/response_model.dart';
import 'package:viserpay/data/services/api_service.dart';

class AccountRepo {
  ApiClient apiClient;
  
  AccountRepo({required this.apiClient});

  // Get all user accounts
  Future<ResponseModel> getUserAccounts() async {
    String url = '${UrlContainer.baseUrl}${UrlContainer.accountsEndPoint}';
    ResponseModel responseModel = await apiClient.request(url, Method.getMethod, null, passHeader: true);
    return responseModel;
  }

  // Get account types
  Future<ResponseModel> getAccountTypes() async {
    String url = '${UrlContainer.baseUrl}${UrlContainer.accountTypesEndPoint}';
    ResponseModel responseModel = await apiClient.request(url, Method.getMethod, null, passHeader: true);
    return responseModel;
  }

  // Create new account
  Future<ResponseModel> createAccount(CreateAccountRequest request) async {
    String url = '${UrlContainer.baseUrl}${UrlContainer.createAccountEndPoint}';
    Map<String, String> params = {
      'account_name': request.accountName,
      'account_type_id': request.accountTypeId.toString(),
      if (request.initialDeposit != null) 'initial_deposit': request.initialDeposit!,
    };
    ResponseModel responseModel = await apiClient.request(url, Method.postMethod, params, passHeader: true);
    return responseModel;
  }

  // Set default account
  Future<ResponseModel> setDefaultAccount(int accountId) async {
    String url = '${UrlContainer.baseUrl}${UrlContainer.setDefaultAccountEndPoint}';
    Map<String, String> params = {
      'account_id': accountId.toString(),
    };
    ResponseModel responseModel = await apiClient.request(url, Method.postMethod, params, passHeader: true);
    return responseModel;
  }

  // Get account details
  Future<ResponseModel> getAccountDetails(int accountId) async {
    String url = '${UrlContainer.baseUrl}${UrlContainer.accountDetailsEndPoint}/$accountId';
    ResponseModel responseModel = await apiClient.request(url, Method.getMethod, null, passHeader: true);
    return responseModel;
  }

  // Update account name
  Future<ResponseModel> updateAccountName(int accountId, String accountName) async {
    String url = '${UrlContainer.baseUrl}${UrlContainer.updateAccountEndPoint}';
    Map<String, String> params = {
      'account_id': accountId.toString(),
      'account_name': accountName,
    };
    ResponseModel responseModel = await apiClient.request(url, Method.postMethod, params, passHeader: true);
    return responseModel;
  }

  // Close account
  Future<ResponseModel> closeAccount(int accountId, String reason) async {
    String url = '${UrlContainer.baseUrl}${UrlContainer.closeAccountEndPoint}';
    Map<String, String> params = {
      'account_id': accountId.toString(),
      'reason': reason,
    };
    ResponseModel responseModel = await apiClient.request(url, Method.postMethod, params, passHeader: true);
    return responseModel;
  }

  // Transfer between accounts
  Future<ResponseModel> transferBetweenAccounts({
    required int fromAccountId,
    required int toAccountId,
    required String amount,
    required String pin,
    String? description,
  }) async {
    String url = '${UrlContainer.baseUrl}${UrlContainer.accountTransferEndPoint}';
    Map<String, String> params = {
      'from_account_id': fromAccountId.toString(),
      'to_account_id': toAccountId.toString(),
      'amount': amount,
      'pin': pin,
      if (description != null) 'description': description,
    };
    ResponseModel responseModel = await apiClient.request(url, Method.postMethod, params, passHeader: true);
    return responseModel;
  }

  // Get account transaction history
  Future<ResponseModel> getAccountTransactions(int accountId, {int page = 1, String? search}) async {
    String url = '${UrlContainer.baseUrl}${UrlContainer.accountTransactionsEndPoint}/$accountId?page=$page';
    if (search != null && search.isNotEmpty) {
      url += '&search=$search';
    }
    ResponseModel responseModel = await apiClient.request(url, Method.getMethod, null, passHeader: true);
    return responseModel;
  }

  // Generate account statement
  Future<ResponseModel> generateAccountStatement({
    required int accountId,
    required String fromDate,
    required String toDate,
    String format = 'pdf',
  }) async {
    String url = '${UrlContainer.baseUrl}${UrlContainer.accountStatementEndPoint}';
    Map<String, String> params = {
      'account_id': accountId.toString(),
      'from_date': fromDate,
      'to_date': toDate,
      'format': format,
    };
    ResponseModel responseModel = await apiClient.request(url, Method.postMethod, params, passHeader: true);
    return responseModel;
  }

  // Get account balance
  Future<ResponseModel> getAccountBalance(int accountId) async {
    String url = '${UrlContainer.baseUrl}${UrlContainer.accountBalanceEndPoint}/$accountId';
    ResponseModel responseModel = await apiClient.request(url, Method.getMethod, null, passHeader: true);
    return responseModel;
  }

  // Freeze/Unfreeze account
  Future<ResponseModel> toggleAccountFreeze(int accountId, bool freeze) async {
    String url = '${UrlContainer.baseUrl}${UrlContainer.toggleAccountFreezeEndPoint}';
    Map<String, String> params = {
      'account_id': accountId.toString(),
      'action': freeze ? 'freeze' : 'unfreeze',
    };
    ResponseModel responseModel = await apiClient.request(url, Method.postMethod, params, passHeader: true);
    return responseModel;
  }
}
