import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:viserpay/core/data/controller/card/card_pin_controller.dart';

class CardManagementScreen extends StatefulWidget {
  const CardManagementScreen({super.key});

  @override
  State<CardManagementScreen> createState() => _CardManagementScreenState();
}

class _CardManagementScreenState extends State<CardManagementScreen>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 2, vsync: this);
    Get.put(CardPinController());
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey[50],
      appBar: AppBar(
        title: const Text(
          'Card Management',
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.w600,
            color: Colors.white,
          ),
        ),
        backgroundColor: const Color(0xFF1E88E5),
        elevation: 0,
        iconTheme: const IconThemeData(color: Colors.white),
        bottom: TabBar(
          controller: _tabController,
          indicatorColor: Colors.white,
          labelColor: Colors.white,
          unselectedLabelColor: Colors.white70,
          tabs: const [
            Tab(
              icon: Icon(Icons.person),
              text: 'Personal Cards',
            ),
            Tab(
              icon: Icon(Icons.business),
              text: 'Business Cards',
            ),
          ],
        ),
      ),
      body: GetBuilder<CardPinController>(
        builder: (controller) {
          if (controller.isLoading) {
            return const Center(
              child: CircularProgressIndicator(
                valueColor: AlwaysStoppedAnimation<Color>(Color(0xFF1E88E5)),
              ),
            );
          }

          return TabBarView(
            controller: _tabController,
            children: [
              _buildCardList(controller, CardAccountType.personal),
              _buildCardList(controller, CardAccountType.business),
            ],
          );
        },
      ),
      floatingActionButton: FloatingActionButton.extended(
        onPressed: () {
          _showAddCardDialog();
        },
        backgroundColor: const Color(0xFF1E88E5),
        icon: const Icon(Icons.add, color: Colors.white),
        label: const Text(
          'Add Card',
          style: TextStyle(color: Colors.white),
        ),
      ),
    );
  }

  Widget _buildCardList(CardPinController controller, CardAccountType accountType) {
    final filteredCards = controller.userCards
        .where((card) => card['account_type'] == accountType.name)
        .toList();

    if (filteredCards.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              accountType == CardAccountType.business
                  ? Icons.business_center_outlined
                  : Icons.credit_card_outlined,
              size: 80,
              color: Colors.grey[400],
            ),
            const SizedBox(height: 16),
            Text(
              'No ${accountType.name} cards found',
              style: TextStyle(
                fontSize: 18,
                color: Colors.grey[600],
                fontWeight: FontWeight.w500,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              'Add your first ${accountType.name} card to get started',
              style: TextStyle(
                fontSize: 14,
                color: Colors.grey[500],
              ),
            ),
          ],
        ),
      );
    }

    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: filteredCards.length,
      itemBuilder: (context, index) {
        final card = filteredCards[index];
        return _buildCardItem(controller, card);
      },
    );
  }

  Widget _buildCardItem(CardPinController controller, Map<String, dynamic> card) {
    final bool isPinSet = card['pin_set'] ?? false;
    final bool isPinLocked = card['pin_locked'] ?? false;
    final int failedAttempts = card['failed_attempts'] ?? 0;
    final String cardType = card['type'] ?? '';
    final String accountType = card['account_type'] ?? '';

    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        children: [
          // Card Header
          Container(
            padding: const EdgeInsets.all(20),
            decoration: BoxDecoration(
              gradient: LinearGradient(
                colors: cardType == 'physical'
                    ? [const Color(0xFF1E88E5), const Color(0xFF1565C0)]
                    : [const Color(0xFF43A047), const Color(0xFF2E7D32)],
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
              ),
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(16),
                topRight: Radius.circular(16),
              ),
            ),
            child: Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: Colors.white.withOpacity(0.2),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Icon(
                    controller.getCardTypeIcon(cardType),
                    color: Colors.white,
                    size: 24,
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        card['card_name'] ?? 'Card',
                        style: const TextStyle(
                          color: Colors.white,
                          fontSize: 16,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        card['card_number'] ?? '',
                        style: const TextStyle(
                          color: Colors.white70,
                          fontSize: 14,
                        ),
                      ),
                    ],
                  ),
                ),
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                  decoration: BoxDecoration(
                    color: Colors.white.withOpacity(0.2),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Icon(
                        controller.getAccountTypeIcon(accountType),
                        color: Colors.white,
                        size: 16,
                      ),
                      const SizedBox(width: 4),
                      Text(
                        controller.getAccountTypeText(accountType),
                        style: const TextStyle(
                          color: Colors.white,
                          fontSize: 12,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),

          // Card Details
          Padding(
            padding: const EdgeInsets.all(20),
            child: Column(
              children: [
                // Status Row
                Row(
                  children: [
                    _buildStatusChip(
                      'PIN Status',
                      isPinSet ? 'Set' : 'Not Set',
                      isPinSet ? Colors.green : Colors.orange,
                    ),
                    const SizedBox(width: 12),
                    if (isPinLocked)
                      _buildStatusChip(
                        'PIN',
                        'Locked',
                        Colors.red,
                      ),
                    if (failedAttempts > 0 && !isPinLocked)
                      _buildStatusChip(
                        'Failed Attempts',
                        failedAttempts.toString(),
                        Colors.orange,
                      ),
                  ],
                ),

                const SizedBox(height: 20),

                // PIN Management Actions
                Row(
                  children: [
                    if (!isPinSet)
                      Expanded(
                        child: _buildActionButton(
                          'Create PIN',
                          Icons.add_circle_outline,
                          const Color(0xFF1E88E5),
                          () => _showPinDialog(controller, card, CardPinAction.create),
                        ),
                      ),
                    if (isPinSet && !isPinLocked) ...[
                      Expanded(
                        child: _buildActionButton(
                          'Change PIN',
                          Icons.edit_outlined,
                          const Color(0xFF43A047),
                          () => _showPinDialog(controller, card, CardPinAction.change),
                        ),
                      ),
                      const SizedBox(width: 12),
                      Expanded(
                        child: _buildActionButton(
                          'Reset PIN',
                          Icons.refresh_outlined,
                          const Color(0xFFFF9800),
                          () => _showPinDialog(controller, card, CardPinAction.reset),
                        ),
                      ),
                    ],
                    if (isPinLocked)
                      Expanded(
                        child: _buildActionButton(
                          'Unlock PIN',
                          Icons.lock_open_outlined,
                          const Color(0xFFE53935),
                          () => _showPinDialog(controller, card, CardPinAction.unlock),
                        ),
                      ),
                  ],
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildStatusChip(String label, String value, Color color) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(20),
        border: Border.all(color: color.withOpacity(0.3)),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Container(
            width: 8,
            height: 8,
            decoration: BoxDecoration(
              color: color,
              shape: BoxShape.circle,
            ),
          ),
          const SizedBox(width: 6),
          Text(
            '$label: $value',
            style: TextStyle(
              color: color,
              fontSize: 12,
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildActionButton(
    String text,
    IconData icon,
    Color color,
    VoidCallback onPressed,
  ) {
    return ElevatedButton.icon(
      onPressed: onPressed,
      icon: Icon(icon, size: 18),
      label: Text(text),
      style: ElevatedButton.styleFrom(
        backgroundColor: color,
        foregroundColor: Colors.white,
        elevation: 0,
        padding: const EdgeInsets.symmetric(vertical: 12),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(8),
        ),
      ),
    );
  }

  void _showPinDialog(
    CardPinController controller,
    Map<String, dynamic> card,
    CardPinAction action,
  ) {
    controller.selectCard(card);
    controller.selectAction(action);

    showDialog(
      context: context,
      builder: (context) => _buildPinDialog(controller, action),
    );
  }

  Widget _buildPinDialog(CardPinController controller, CardPinAction action) {
    String title = '';
    String description = '';

    switch (action) {
      case CardPinAction.create:
        title = 'Create Card PIN';
        description = 'Create a 4-digit PIN for your card';
        break;
      case CardPinAction.change:
        title = 'Change Card PIN';
        description = 'Enter your current PIN and create a new one';
        break;
      case CardPinAction.reset:
        title = 'Reset Card PIN';
        description = 'Reset your card PIN using transaction PIN';
        break;
      case CardPinAction.unlock:
        title = 'Unlock Card PIN';
        description = 'Unlock your card using transaction PIN';
        break;
    }

    return Dialog(
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
      ),
      child: Container(
        padding: const EdgeInsets.all(24),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              title,
              style: const TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.w600,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              description,
              style: TextStyle(
                fontSize: 14,
                color: Colors.grey[600],
              ),
            ),
            const SizedBox(height: 24),

            // Form fields based on action
            if (action == CardPinAction.change) ...[
              _buildPinField(
                controller: controller.currentPinController,
                focusNode: controller.currentPinFocusNode,
                label: 'Current PIN',
                hint: 'Enter current 4-digit PIN',
              ),
              const SizedBox(height: 16),
            ],

            if (action != CardPinAction.unlock) ...[
              _buildPinField(
                controller: controller.newPinController,
                focusNode: controller.newPinFocusNode,
                label: action == CardPinAction.change ? 'New PIN' : 'Card PIN',
                hint: 'Enter 4-digit PIN',
                isObscure: !controller.isPinVisible,
                suffixIcon: IconButton(
                  icon: Icon(
                    controller.isPinVisible ? Icons.visibility_off : Icons.visibility,
                  ),
                  onPressed: controller.togglePinVisibility,
                ),
              ),
              const SizedBox(height: 16),
              _buildPinField(
                controller: controller.confirmPinController,
                focusNode: controller.confirmPinFocusNode,
                label: 'Confirm PIN',
                hint: 'Confirm 4-digit PIN',
                isObscure: !controller.isConfirmPinVisible,
                suffixIcon: IconButton(
                  icon: Icon(
                    controller.isConfirmPinVisible ? Icons.visibility_off : Icons.visibility,
                  ),
                  onPressed: controller.toggleConfirmPinVisibility,
                ),
              ),
              const SizedBox(height: 16),
            ],

            _buildPinField(
              controller: controller.transactionPinController,
              focusNode: controller.transactionPinFocusNode,
              label: 'Transaction PIN',
              hint: 'Enter your transaction PIN',
              isObscure: true,
            ),

            const SizedBox(height: 24),

            // Action buttons
            Row(
              children: [
                Expanded(
                  child: TextButton(
                    onPressed: () {
                      controller.clearForm();
                      Get.back();
                    },
                    child: const Text('Cancel'),
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: GetBuilder<CardPinController>(
                    builder: (controller) => ElevatedButton(
                      onPressed: controller.submitLoading
                          ? null
                          : () => _handlePinAction(controller, action),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: const Color(0xFF1E88E5),
                        foregroundColor: Colors.white,
                        padding: const EdgeInsets.symmetric(vertical: 12),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(8),
                        ),
                      ),
                      child: controller.submitLoading
                          ? const SizedBox(
                              width: 20,
                              height: 20,
                              child: CircularProgressIndicator(
                                strokeWidth: 2,
                                valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                              ),
                            )
                          : Text(_getActionButtonText(action)),
                    ),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildPinField({
    required TextEditingController controller,
    required FocusNode focusNode,
    required String label,
    required String hint,
    bool isObscure = true,
    Widget? suffixIcon,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: const TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.w500,
          ),
        ),
        const SizedBox(height: 8),
        TextFormField(
          controller: controller,
          focusNode: focusNode,
          obscureText: isObscure,
          keyboardType: TextInputType.number,
          maxLength: 4,
          decoration: InputDecoration(
            hintText: hint,
            suffixIcon: suffixIcon,
            counterText: '',
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: const BorderSide(color: Color(0xFF1E88E5)),
            ),
          ),
        ),
      ],
    );
  }

  void _handlePinAction(CardPinController controller, CardPinAction action) {
    switch (action) {
      case CardPinAction.create:
        controller.createCardPin();
        break;
      case CardPinAction.change:
        controller.changeCardPin();
        break;
      case CardPinAction.reset:
        controller.resetCardPin();
        break;
      case CardPinAction.unlock:
        controller.unlockCardPin();
        break;
    }
  }

  String _getActionButtonText(CardPinAction action) {
    switch (action) {
      case CardPinAction.create:
        return 'Create PIN';
      case CardPinAction.change:
        return 'Change PIN';
      case CardPinAction.reset:
        return 'Reset PIN';
      case CardPinAction.unlock:
        return 'Unlock PIN';
    }
  }

  void _showAddCardDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Add New Card'),
        content: const Text('Would you like to request a new card?'),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              Get.back();
              // Navigate to card request screen
              // Get.toNamed(RouteHelper.cardRequestScreen);
            },
            child: const Text('Request Card'),
          ),
        ],
      ),
    );
  }
}
