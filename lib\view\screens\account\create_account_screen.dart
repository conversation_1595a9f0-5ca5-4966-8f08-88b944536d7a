import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:viserpay/core/utils/dimensions.dart';
import 'package:viserpay/core/utils/my_color.dart';
import 'package:viserpay/core/utils/my_strings.dart';
import 'package:viserpay/core/utils/style.dart';
import 'package:viserpay/data/controller/account/account_controller.dart';
import 'package:viserpay/data/model/account/account_model.dart';
import 'package:viserpay/view/components/app-bar/custom_appbar.dart';
import 'package:viserpay/view/components/buttons/gradient_rounded_button.dart';
import 'package:viserpay/view/components/custom_loader/custom_loader.dart';
import 'package:viserpay/view/components/text-form-field/custom_text_field.dart';
import 'package:viserpay/view/screens/account/widgets/account_type_selector.dart';

class CreateAccountScreen extends StatefulWidget {
  const CreateAccountScreen({super.key});

  @override
  State<CreateAccountScreen> createState() => _CreateAccountScreenState();
}

class _CreateAccountScreenState extends State<CreateAccountScreen> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: MyColor.screenBgColor,
      appBar: CustomAppBar(
        title: MyStrings.createAccount,
        isShowBackBtn: true,
      ),
      body: GetBuilder<AccountController>(
        builder: (controller) {
          return controller.isLoading
              ? const CustomLoader()
              : SingleChildScrollView(
                  padding: const EdgeInsets.all(Dimensions.space20),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // Header Section
                      Container(
                        width: double.infinity,
                        padding: const EdgeInsets.all(Dimensions.space20),
                        decoration: BoxDecoration(
                          color: MyColor.colorWhite,
                          borderRadius: BorderRadius.circular(Dimensions.mediumRadius),
                          boxShadow: [
                            BoxShadow(
                              color: MyColor.colorGrey.withOpacity(0.1),
                              blurRadius: 10,
                              offset: const Offset(0, 2),
                            ),
                          ],
                        ),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Row(
                              children: [
                                Container(
                                  padding: const EdgeInsets.all(Dimensions.space12),
                                  decoration: BoxDecoration(
                                    color: MyColor.primaryColor.withOpacity(0.1),
                                    borderRadius: BorderRadius.circular(Dimensions.smallRadius),
                                  ),
                                  child: const Icon(
                                    Icons.account_balance,
                                    color: MyColor.primaryColor,
                                    size: 24,
                                  ),
                                ),
                                const SizedBox(width: Dimensions.space15),
                                Expanded(
                                  child: Column(
                                    crossAxisAlignment: CrossAxisAlignment.start,
                                    children: [
                                      Text(
                                        'Create New Account',
                                        style: semiBoldLarge.copyWith(
                                          color: MyColor.colorBlack,
                                        ),
                                      ),
                                      const SizedBox(height: Dimensions.space5),
                                      Text(
                                        'Choose an account type that suits your needs',
                                        style: regularDefault.copyWith(
                                          color: MyColor.colorGrey,
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                              ],
                            ),
                          ],
                        ),
                      ),

                      const SizedBox(height: Dimensions.space25),

                      // Account Type Selection
                      Text(
                        MyStrings.accountType,
                        style: semiBoldLarge.copyWith(
                          color: MyColor.colorBlack,
                        ),
                      ),
                      const SizedBox(height: Dimensions.space15),
                      
                      AccountTypeSelector(
                        accountTypes: controller.accountTypes,
                        selectedAccountType: controller.selectedAccountType,
                        onAccountTypeSelected: controller.selectAccountType,
                      ),

                      const SizedBox(height: Dimensions.space25),

                      // Account Details Form
                      Container(
                        width: double.infinity,
                        padding: const EdgeInsets.all(Dimensions.space20),
                        decoration: BoxDecoration(
                          color: MyColor.colorWhite,
                          borderRadius: BorderRadius.circular(Dimensions.mediumRadius),
                          boxShadow: [
                            BoxShadow(
                              color: MyColor.colorGrey.withOpacity(0.1),
                              blurRadius: 10,
                              offset: const Offset(0, 2),
                            ),
                          ],
                        ),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              'Account Details',
                              style: semiBoldLarge.copyWith(
                                color: MyColor.colorBlack,
                              ),
                            ),
                            const SizedBox(height: Dimensions.space20),

                            // Account Name Field
                            CustomTextField(
                              labelText: MyStrings.accountName,
                              hintText: MyStrings.enterAccountName,
                              controller: controller.accountNameController,
                              focusNode: FocusNode(),
                              textInputType: TextInputType.text,
                              nextFocus: FocusNode(),
                              validator: (value) {
                                if (value == null || value.isEmpty) {
                                  return MyStrings.enterAccountName;
                                }
                                return null;
                              },
                            ),

                            const SizedBox(height: Dimensions.space20),

                            // Initial Deposit Field (Optional)
                            CustomTextField(
                              labelText: MyStrings.initialDeposit,
                              hintText: MyStrings.enterInitialDeposit,
                              controller: controller.initialDepositController,
                              focusNode: FocusNode(),
                              textInputType: TextInputType.number,
                              nextFocus: FocusNode(),
                              isRequired: false,
                            ),

                            const SizedBox(height: Dimensions.space15),

                            // Account Number Preview
                            Container(
                              width: double.infinity,
                              padding: const EdgeInsets.all(Dimensions.space15),
                              decoration: BoxDecoration(
                                color: MyColor.colorGrey2,
                                borderRadius: BorderRadius.circular(Dimensions.smallRadius),
                                border: Border.all(
                                  color: MyColor.borderColor,
                                  width: 1,
                                ),
                              ),
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Text(
                                    'Account Number (Auto-generated)',
                                    style: regularSmall.copyWith(
                                      color: MyColor.colorGrey,
                                    ),
                                  ),
                                  const SizedBox(height: Dimensions.space5),
                                  Text(
                                    controller.generateAccountNumber(),
                                    style: semiBoldDefault.copyWith(
                                      color: MyColor.colorBlack,
                                      letterSpacing: 1.2,
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          ],
                        ),
                      ),

                      // Selected Account Type Info
                      if (controller.selectedAccountType != null) ...[
                        const SizedBox(height: Dimensions.space25),
                        Container(
                          width: double.infinity,
                          padding: const EdgeInsets.all(Dimensions.space20),
                          decoration: BoxDecoration(
                            color: controller.getAccountTypeColor(controller.selectedAccountType?.code).withOpacity(0.1),
                            borderRadius: BorderRadius.circular(Dimensions.mediumRadius),
                            border: Border.all(
                              color: controller.getAccountTypeColor(controller.selectedAccountType?.code).withOpacity(0.3),
                              width: 1,
                            ),
                          ),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Row(
                                children: [
                                  Icon(
                                    controller.getAccountTypeIcon(controller.selectedAccountType?.code),
                                    color: controller.getAccountTypeColor(controller.selectedAccountType?.code),
                                    size: 24,
                                  ),
                                  const SizedBox(width: Dimensions.space10),
                                  Text(
                                    controller.selectedAccountType?.name ?? '',
                                    style: semiBoldLarge.copyWith(
                                      color: controller.getAccountTypeColor(controller.selectedAccountType?.code),
                                    ),
                                  ),
                                ],
                              ),
                              if (controller.selectedAccountType?.description != null) ...[
                                const SizedBox(height: Dimensions.space10),
                                Text(
                                  controller.selectedAccountType!.description!,
                                  style: regularDefault.copyWith(
                                    color: MyColor.colorGrey,
                                  ),
                                ),
                              ],
                              if (controller.selectedAccountType?.interestRate != null && 
                                  controller.selectedAccountType!.interestRate != '0') ...[
                                const SizedBox(height: Dimensions.space10),
                                Row(
                                  children: [
                                    const Icon(
                                      Icons.trending_up,
                                      color: MyColor.colorGreen,
                                      size: 16,
                                    ),
                                    const SizedBox(width: Dimensions.space5),
                                    Text(
                                      '${MyStrings.interestRate}: ${controller.selectedAccountType?.interestRate}% p.a.',
                                      style: semiBoldDefault.copyWith(
                                        color: MyColor.colorGreen,
                                      ),
                                    ),
                                  ],
                                ),
                              ],
                              if (controller.selectedAccountType?.minimumBalance != null &&
                                  controller.selectedAccountType!.minimumBalance != '0') ...[
                                const SizedBox(height: Dimensions.space5),
                                Row(
                                  children: [
                                    const Icon(
                                      Icons.account_balance_wallet,
                                      color: MyColor.colorOrange,
                                      size: 16,
                                    ),
                                    const SizedBox(width: Dimensions.space5),
                                    Text(
                                      '${MyStrings.minimumBalance}: ${controller.curSymbol}${controller.selectedAccountType?.minimumBalance}',
                                      style: regularDefault.copyWith(
                                        color: MyColor.colorGrey,
                                      ),
                                    ),
                                  ],
                                ),
                              ],
                            ],
                          ),
                        ),
                      ],

                      const SizedBox(height: Dimensions.space30),

                      // Create Account Button
                      GradientRoundedButton(
                        text: MyStrings.createAccount,
                        press: () {
                          controller.createAccount();
                        },
                        isLoading: controller.isSubmitLoading,
                      ),

                      const SizedBox(height: Dimensions.space20),
                    ],
                  ),
                );
        },
      ),
    );
  }
}
