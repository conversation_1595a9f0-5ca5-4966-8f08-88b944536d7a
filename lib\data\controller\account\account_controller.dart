import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:viserpay/core/helper/string_format_helper.dart';
import 'package:viserpay/core/route/route.dart';
import 'package:viserpay/core/utils/my_strings.dart';
import 'package:viserpay/data/model/account/account_model.dart';
import 'package:viserpay/data/model/global/response_model/response_model.dart';
import 'package:viserpay/data/repo/account/account_repo.dart';
import 'package:viserpay/view/components/snack_bar/show_custom_snackbar.dart';

class AccountController extends GetxController {
  AccountRepo accountRepo;
  
  AccountController({required this.accountRepo});

  bool isLoading = false;
  bool isSubmitLoading = false;
  
  List<Account> userAccounts = [];
  List<AccountType> accountTypes = [];
  Account? selectedAccount;
  AccountType? selectedAccountType;
  
  String currentBalance = '0.00';
  String curSymbol = '\$';

  // Controllers for forms
  TextEditingController accountNameController = TextEditingController();
  TextEditingController initialDepositController = TextEditingController();
  TextEditingController transferAmountController = TextEditingController();
  TextEditingController pinController = TextEditingController();
  TextEditingController descriptionController = TextEditingController();

  @override
  void onInit() {
    super.onInit();
    loadAccountData();
  }

  @override
  void dispose() {
    accountNameController.dispose();
    initialDepositController.dispose();
    transferAmountController.dispose();
    pinController.dispose();
    descriptionController.dispose();
    super.dispose();
  }

  // Load user accounts and account types
  Future<void> loadAccountData() async {
    isLoading = true;
    update();

    try {
      // Load user accounts
      ResponseModel accountResponse = await accountRepo.getUserAccounts();
      if (accountResponse.statusCode == 200) {
        AccountResponseModel model = AccountResponseModel.fromJson(jsonDecode(accountResponse.responseJson));
        if (model.status?.toLowerCase() == MyStrings.success.toLowerCase()) {
          userAccounts.clear();
          userAccounts.addAll(model.data?.accounts ?? []);
          currentBalance = model.data?.currentBalance ?? '0.00';
          
          // Set default account if available
          if (userAccounts.isNotEmpty) {
            selectedAccount = userAccounts.firstWhere(
              (account) => account.isDefault == true,
              orElse: () => userAccounts.first,
            );
          }
        }
      }

      // Load account types
      ResponseModel typesResponse = await accountRepo.getAccountTypes();
      if (typesResponse.statusCode == 200) {
        AccountResponseModel model = AccountResponseModel.fromJson(jsonDecode(typesResponse.responseJson));
        if (model.status?.toLowerCase() == MyStrings.success.toLowerCase()) {
          accountTypes.clear();
          accountTypes.addAll(model.data?.accountTypes ?? []);
        }
      }
    } catch (e) {
      CustomSnackBar.error(errorList: [MyStrings.somethingWentWrong]);
    }

    isLoading = false;
    update();
  }

  // Create new account
  Future<void> createAccount() async {
    if (!validateCreateAccountForm()) return;

    isSubmitLoading = true;
    update();

    try {
      CreateAccountRequest request = CreateAccountRequest(
        accountName: accountNameController.text.trim(),
        accountTypeId: selectedAccountType!.id!,
        initialDeposit: initialDepositController.text.trim().isEmpty 
            ? null 
            : initialDepositController.text.trim(),
      );

      ResponseModel response = await accountRepo.createAccount(request);
      if (response.statusCode == 200) {
        AccountResponseModel model = AccountResponseModel.fromJson(jsonDecode(response.responseJson));
        if (model.status?.toLowerCase() == MyStrings.success.toLowerCase()) {
          CustomSnackBar.success(successList: model.message?.success ?? [MyStrings.accountCreatedSuccessfully]);
          clearCreateAccountForm();
          await loadAccountData();
          Get.back();
        } else {
          CustomSnackBar.error(errorList: model.message?.error ?? [MyStrings.requestFail]);
        }
      } else {
        CustomSnackBar.error(errorList: [response.message]);
      }
    } catch (e) {
      CustomSnackBar.error(errorList: [MyStrings.somethingWentWrong]);
    }

    isSubmitLoading = false;
    update();
  }

  // Set default account
  Future<void> setDefaultAccount(Account account) async {
    isLoading = true;
    update();

    try {
      ResponseModel response = await accountRepo.setDefaultAccount(account.id!);
      if (response.statusCode == 200) {
        AccountResponseModel model = AccountResponseModel.fromJson(jsonDecode(response.responseJson));
        if (model.status?.toLowerCase() == MyStrings.success.toLowerCase()) {
          CustomSnackBar.success(successList: model.message?.success ?? [MyStrings.defaultAccountUpdated]);
          await loadAccountData();
        } else {
          CustomSnackBar.error(errorList: model.message?.error ?? [MyStrings.requestFail]);
        }
      } else {
        CustomSnackBar.error(errorList: [response.message]);
      }
    } catch (e) {
      CustomSnackBar.error(errorList: [MyStrings.somethingWentWrong]);
    }

    isLoading = false;
    update();
  }

  // Transfer between accounts
  Future<void> transferBetweenAccounts(Account fromAccount, Account toAccount) async {
    if (!validateTransferForm()) return;

    isSubmitLoading = true;
    update();

    try {
      ResponseModel response = await accountRepo.transferBetweenAccounts(
        fromAccountId: fromAccount.id!,
        toAccountId: toAccount.id!,
        amount: transferAmountController.text.trim(),
        pin: pinController.text.trim(),
        description: descriptionController.text.trim().isEmpty 
            ? null 
            : descriptionController.text.trim(),
      );

      if (response.statusCode == 200) {
        AccountResponseModel model = AccountResponseModel.fromJson(jsonDecode(response.responseJson));
        if (model.status?.toLowerCase() == MyStrings.success.toLowerCase()) {
          CustomSnackBar.success(successList: model.message?.success ?? [MyStrings.transferSuccessful]);
          clearTransferForm();
          await loadAccountData();
          Get.back();
        } else {
          CustomSnackBar.error(errorList: model.message?.error ?? [MyStrings.requestFail]);
        }
      } else {
        CustomSnackBar.error(errorList: [response.message]);
      }
    } catch (e) {
      CustomSnackBar.error(errorList: [MyStrings.somethingWentWrong]);
    }

    isSubmitLoading = false;
    update();
  }

  // Validation methods
  bool validateCreateAccountForm() {
    if (accountNameController.text.trim().isEmpty) {
      CustomSnackBar.error(errorList: [MyStrings.enterAccountName]);
      return false;
    }
    if (selectedAccountType == null) {
      CustomSnackBar.error(errorList: [MyStrings.selectAccountType]);
      return false;
    }
    return true;
  }

  bool validateTransferForm() {
    if (transferAmountController.text.trim().isEmpty) {
      CustomSnackBar.error(errorList: [MyStrings.enterAmount]);
      return false;
    }
    if (pinController.text.trim().isEmpty) {
      CustomSnackBar.error(errorList: [MyStrings.enterPin]);
      return false;
    }
    
    double amount = double.tryParse(transferAmountController.text.trim()) ?? 0;
    if (amount <= 0) {
      CustomSnackBar.error(errorList: [MyStrings.enterValidAmount]);
      return false;
    }
    
    return true;
  }

  // Clear form methods
  void clearCreateAccountForm() {
    accountNameController.clear();
    initialDepositController.clear();
    selectedAccountType = null;
    update();
  }

  void clearTransferForm() {
    transferAmountController.clear();
    pinController.clear();
    descriptionController.clear();
    update();
  }

  // Selection methods
  void selectAccountType(AccountType accountType) {
    selectedAccountType = accountType;
    update();
  }

  void selectAccount(Account account) {
    selectedAccount = account;
    update();
  }

  // Generate account number
  String generateAccountNumber() {
    String timestamp = DateTime.now().millisecondsSinceEpoch.toString();
    return 'KP${timestamp.substring(timestamp.length - 10)}';
  }

  // Get account type icon
  IconData getAccountTypeIcon(String? code) {
    switch (code?.toUpperCase()) {
      case 'SAVINGS':
        return Icons.savings;
      case 'CURRENT':
        return Icons.account_balance_wallet;
      case 'FIXED_DEPOSIT':
        return Icons.lock_clock;
      case 'BUSINESS':
        return Icons.business;
      case 'STUDENT':
        return Icons.school;
      default:
        return Icons.account_balance;
    }
  }

  // Get account type color
  Color getAccountTypeColor(String? code) {
    switch (code?.toUpperCase()) {
      case 'SAVINGS':
        return Colors.green;
      case 'CURRENT':
        return Colors.blue;
      case 'FIXED_DEPOSIT':
        return Colors.orange;
      case 'BUSINESS':
        return Colors.purple;
      case 'STUDENT':
        return Colors.teal;
      default:
        return Colors.grey;
    }
  }

  // Format balance
  String formatBalance(String? balance) {
    if (balance == null || balance.isEmpty) return '0.00';
    return Converter.formatNumber(balance);
  }
}
