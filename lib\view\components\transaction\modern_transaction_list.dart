import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:viserpay/core/utils/dimensions.dart';
import 'package:viserpay/core/utils/my_color.dart';
import 'package:viserpay/core/utils/style.dart';
import 'package:viserpay/data/model/transaction/transaction_model.dart';

class ModernTransactionList extends StatelessWidget {
  final List<Transaction> transactions;
  final bool isLoading;
  final VoidCallback? onRefresh;
  final VoidCallback? onLoadMore;
  final bool hasMoreData;
  final Function(Transaction)? onTransactionTap;

  const ModernTransactionList({
    super.key,
    required this.transactions,
    this.isLoading = false,
    this.onRefresh,
    this.onLoadMore,
    this.hasMoreData = false,
    this.onTransactionTap,
  });

  @override
  Widget build(BuildContext context) {
    if (isLoading && transactions.isEmpty) {
      return const Center(
        child: CircularProgressIndicator(
          color: MyColor.primaryColor,
        ),
      );
    }

    if (transactions.isEmpty) {
      return _buildEmptyState();
    }

    return RefreshIndicator(
      onRefresh: () async {
        onRefresh?.call();
      },
      color: MyColor.primaryColor,
      backgroundColor: MyColor.colorWhite,
      child: ListView.builder(
        physics: const AlwaysScrollableScrollPhysics(),
        padding: const EdgeInsets.symmetric(
          horizontal: Dimensions.space15,
          vertical: Dimensions.space10,
        ),
        itemCount: transactions.length + (hasMoreData ? 1 : 0),
        itemBuilder: (context, index) {
          if (index == transactions.length) {
            return _buildLoadMoreButton();
          }

          final transaction = transactions[index];
          final isToday = _isToday(transaction.createdAt);
          final isYesterday = _isYesterday(transaction.createdAt);
          
          bool showDateHeader = false;
          if (index == 0) {
            showDateHeader = true;
          } else {
            final prevTransaction = transactions[index - 1];
            showDateHeader = !_isSameDay(transaction.createdAt, prevTransaction.createdAt);
          }

          return Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              if (showDateHeader) _buildDateHeader(transaction.createdAt, isToday, isYesterday),
              _buildTransactionItem(transaction),
            ],
          );
        },
      ),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Container(
            width: 120,
            height: 120,
            decoration: BoxDecoration(
              color: MyColor.colorGrey.withOpacity(0.1),
              shape: BoxShape.circle,
            ),
            child: const Icon(
              Icons.receipt_long,
              size: 60,
              color: MyColor.colorGrey,
            ),
          ),
          const SizedBox(height: Dimensions.space20),
          Text(
            'No Transactions Yet',
            style: semiBoldLarge.copyWith(
              color: MyColor.colorBlack,
            ),
          ),
          const SizedBox(height: Dimensions.space10),
          Text(
            'Your transaction history will appear here',
            style: regularDefault.copyWith(
              color: MyColor.colorGrey,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildDateHeader(String? dateTime, bool isToday, bool isYesterday) {
    String headerText;
    if (isToday) {
      headerText = 'Today';
    } else if (isYesterday) {
      headerText = 'Yesterday';
    } else {
      headerText = _formatDate(dateTime);
    }

    return Padding(
      padding: const EdgeInsets.only(
        top: Dimensions.space20,
        bottom: Dimensions.space10,
      ),
      child: Text(
        headerText,
        style: semiBoldDefault.copyWith(
          color: MyColor.colorGrey,
          fontSize: 14,
        ),
      ),
    );
  }

  Widget _buildTransactionItem(Transaction transaction) {
    final isCredit = _isCredit(transaction.type);
    final statusColor = _getStatusColor(transaction.status);
    final typeIcon = _getTransactionIcon(transaction.type);
    final typeColor = _getTransactionColor(transaction.type);

    return GestureDetector(
      onTap: () => onTransactionTap?.call(transaction),
      child: Container(
        margin: const EdgeInsets.only(bottom: Dimensions.space10),
        padding: const EdgeInsets.all(Dimensions.space15),
        decoration: BoxDecoration(
          color: MyColor.colorWhite,
          borderRadius: BorderRadius.circular(Dimensions.mediumRadius),
          border: Border.all(
            color: MyColor.borderColor.withOpacity(0.5),
            width: 1,
          ),
          boxShadow: [
            BoxShadow(
              color: MyColor.colorGrey.withOpacity(0.05),
              blurRadius: 10,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Row(
          children: [
            // Transaction Icon
            Container(
              width: 45,
              height: 45,
              decoration: BoxDecoration(
                color: typeColor.withOpacity(0.1),
                borderRadius: BorderRadius.circular(Dimensions.smallRadius),
              ),
              child: Icon(
                typeIcon,
                color: typeColor,
                size: 24,
              ),
            ),

            const SizedBox(width: Dimensions.space15),

            // Transaction Details
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Expanded(
                        child: Text(
                          transaction.description ?? 'Transaction',
                          style: semiBoldDefault.copyWith(
                            color: MyColor.colorBlack,
                          ),
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                        ),
                      ),
                      Text(
                        '${isCredit ? '+' : '-'}₦${_formatAmount(transaction.amount)}',
                        style: semiBoldDefault.copyWith(
                          color: isCredit ? MyColor.colorGreen : MyColor.colorRed,
                          fontSize: 16,
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: Dimensions.space5),
                  Row(
                    children: [
                      Expanded(
                        child: Text(
                          _getTransactionSubtitle(transaction),
                          style: regularSmall.copyWith(
                            color: MyColor.colorGrey,
                          ),
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                        ),
                      ),
                      Container(
                        padding: const EdgeInsets.symmetric(
                          horizontal: Dimensions.space8,
                          vertical: Dimensions.space2,
                        ),
                        decoration: BoxDecoration(
                          color: statusColor.withOpacity(0.1),
                          borderRadius: BorderRadius.circular(Dimensions.smallRadius),
                        ),
                        child: Text(
                          _getStatusText(transaction.status),
                          style: regularSmall.copyWith(
                            color: statusColor,
                            fontSize: 10,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: Dimensions.space5),
                  Text(
                    _formatTime(transaction.createdAt),
                    style: regularSmall.copyWith(
                      color: MyColor.colorGrey,
                      fontSize: 11,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildLoadMoreButton() {
    return Container(
      margin: const EdgeInsets.symmetric(vertical: Dimensions.space15),
      child: Center(
        child: isLoading
            ? const CircularProgressIndicator(
                color: MyColor.primaryColor,
                strokeWidth: 2,
              )
            : GestureDetector(
                onTap: onLoadMore,
                child: Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: Dimensions.space20,
                    vertical: Dimensions.space10,
                  ),
                  decoration: BoxDecoration(
                    color: MyColor.primaryColor.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(Dimensions.smallRadius),
                  ),
                  child: Text(
                    'Load More',
                    style: semiBoldDefault.copyWith(
                      color: MyColor.primaryColor,
                    ),
                  ),
                ),
              ),
      ),
    );
  }

  bool _isCredit(String? type) {
    const creditTypes = ['deposit', 'transfer_in', 'refund', 'cashback', 'interest', 'bonus'];
    return creditTypes.contains(type?.toLowerCase());
  }

  Color _getStatusColor(String? status) {
    switch (status?.toLowerCase()) {
      case 'successful':
      case 'completed':
        return MyColor.colorGreen;
      case 'pending':
      case 'processing':
        return MyColor.colorOrange;
      case 'failed':
      case 'cancelled':
        return MyColor.colorRed;
      default:
        return MyColor.colorGrey;
    }
  }

  IconData _getTransactionIcon(String? type) {
    switch (type?.toLowerCase()) {
      case 'deposit':
        return Icons.add_circle_outline;
      case 'withdrawal':
        return Icons.remove_circle_outline;
      case 'transfer_out':
        return Icons.send;
      case 'transfer_in':
        return Icons.call_received;
      case 'bill_payment':
        return Icons.receipt;
      case 'airtime':
        return Icons.phone;
      case 'data':
        return Icons.wifi;
      case 'electricity':
        return Icons.flash_on;
      case 'cable_tv':
        return Icons.tv;
      case 'betting':
        return Icons.sports_soccer;
      case 'cashout':
        return Icons.atm;
      case 'merchant_payment':
        return Icons.store;
      case 'refund':
        return Icons.undo;
      case 'interest':
        return Icons.trending_up;
      case 'bonus':
        return Icons.card_giftcard;
      default:
        return Icons.swap_horiz;
    }
  }

  Color _getTransactionColor(String? type) {
    switch (type?.toLowerCase()) {
      case 'deposit':
      case 'transfer_in':
      case 'refund':
      case 'interest':
      case 'bonus':
        return MyColor.colorGreen;
      case 'withdrawal':
      case 'transfer_out':
      case 'cashout':
        return MyColor.colorRed;
      case 'bill_payment':
      case 'airtime':
      case 'data':
      case 'electricity':
      case 'cable_tv':
        return MyColor.colorOrange;
      case 'merchant_payment':
        return MyColor.colorCyan;
      default:
        return MyColor.primaryColor;
    }
  }

  String _getTransactionSubtitle(Transaction transaction) {
    switch (transaction.type?.toLowerCase()) {
      case 'transfer_out':
        return 'To ${transaction.recipientName ?? 'Unknown'}';
      case 'transfer_in':
        return 'From ${transaction.senderName ?? 'Unknown'}';
      case 'bill_payment':
        return transaction.billProvider ?? 'Bill Payment';
      case 'merchant_payment':
        return transaction.merchantName ?? 'Merchant Payment';
      default:
        return transaction.reference ?? '';
    }
  }

  String _getStatusText(String? status) {
    switch (status?.toLowerCase()) {
      case 'successful':
        return 'Success';
      case 'pending':
        return 'Pending';
      case 'processing':
        return 'Processing';
      case 'failed':
        return 'Failed';
      case 'cancelled':
        return 'Cancelled';
      default:
        return 'Unknown';
    }
  }

  String _formatAmount(String? amount) {
    if (amount == null || amount.isEmpty) return '0.00';
    try {
      double value = double.parse(amount);
      if (value >= 1000000) {
        return '${(value / 1000000).toStringAsFixed(1)}M';
      } else if (value >= 1000) {
        return '${(value / 1000).toStringAsFixed(1)}K';
      } else {
        return value.toStringAsFixed(2);
      }
    } catch (e) {
      return amount;
    }
  }

  String _formatDate(String? dateTime) {
    if (dateTime == null) return '';
    try {
      DateTime date = DateTime.parse(dateTime);
      return '${date.day}/${date.month}/${date.year}';
    } catch (e) {
      return dateTime;
    }
  }

  String _formatTime(String? dateTime) {
    if (dateTime == null) return '';
    try {
      DateTime date = DateTime.parse(dateTime);
      String period = date.hour >= 12 ? 'PM' : 'AM';
      int hour = date.hour > 12 ? date.hour - 12 : (date.hour == 0 ? 12 : date.hour);
      String minute = date.minute.toString().padLeft(2, '0');
      return '$hour:$minute $period';
    } catch (e) {
      return dateTime;
    }
  }

  bool _isToday(String? dateTime) {
    if (dateTime == null) return false;
    try {
      DateTime date = DateTime.parse(dateTime);
      DateTime now = DateTime.now();
      return date.year == now.year && date.month == now.month && date.day == now.day;
    } catch (e) {
      return false;
    }
  }

  bool _isYesterday(String? dateTime) {
    if (dateTime == null) return false;
    try {
      DateTime date = DateTime.parse(dateTime);
      DateTime yesterday = DateTime.now().subtract(const Duration(days: 1));
      return date.year == yesterday.year && date.month == yesterday.month && date.day == yesterday.day;
    } catch (e) {
      return false;
    }
  }

  bool _isSameDay(String? date1, String? date2) {
    if (date1 == null || date2 == null) return false;
    try {
      DateTime d1 = DateTime.parse(date1);
      DateTime d2 = DateTime.parse(date2);
      return d1.year == d2.year && d1.month == d2.month && d1.day == d2.day;
    } catch (e) {
      return false;
    }
  }
}
