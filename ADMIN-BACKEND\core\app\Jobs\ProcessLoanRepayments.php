<?php

namespace App\Jobs;

use App\Models\UserLoan;
use App\Models\LoanRepayment;
use App\Models\User;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;
use Carbon\Carbon;

class ProcessLoanRepayments implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    public $timeout = 600; // 10 minutes timeout

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        Log::info('Starting loan repayment processing job');

        try {
            $autoDebitsProcessed = $this->processAutoDebits();
            $overdueLoansUpdated = $this->updateOverdueLoans();
            $maturedLoansProcessed = $this->processMaturedLoans();

            Log::info("Loan processing completed: {$autoDebitsProcessed} auto-debits, {$overdueLoansUpdated} overdue updates, {$maturedLoansProcessed} matured loans");
        } catch (\Exception $e) {
            Log::error('Loan repayment processing failed: ' . $e->getMessage());
            throw $e;
        }
    }

    /**
     * Process auto-debit repayments
     */
    private function processAutoDebits(): int
    {
        $dueRepayments = LoanRepayment::where('status', 'pending')
                                    ->where('due_date', '<=', Carbon::now()->toDateString())
                                    ->whereHas('loan', function($q) {
                                        $q->where('auto_debit_enabled', true)
                                          ->where('status', 'repaying');
                                    })
                                    ->get();

        $processed = 0;

        foreach ($dueRepayments as $repayment) {
            try {
                $result = $this->processAutoDebit($repayment);
                if ($result['success']) {
                    $processed++;
                }
            } catch (\Exception $e) {
                Log::error("Failed to process auto-debit for repayment {$repayment->id}: " . $e->getMessage());
            }
        }

        return $processed;
    }

    /**
     * Update overdue loan statuses
     */
    private function updateOverdueLoans(): int
    {
        $overdueRepayments = LoanRepayment::where('status', 'pending')
                                        ->where('due_date', '<', Carbon::now()->toDateString())
                                        ->get();

        $updated = 0;

        foreach ($overdueRepayments as $repayment) {
            try {
                $repayment->status = 'overdue';
                $repayment->save();

                // Update loan status if needed
                $loan = $repayment->loan;
                if ($loan->status !== 'overdue') {
                    $loan->status = 'overdue';
                    $loan->save();

                    // Apply late payment penalty
                    $this->applyLatePenalty($loan);
                }

                $updated++;
            } catch (\Exception $e) {
                Log::error("Failed to update overdue status for repayment {$repayment->id}: " . $e->getMessage());
            }
        }

        return $updated;
    }

    /**
     * Process matured loans
     */
    private function processMaturedLoans(): int
    {
        $maturedLoans = UserLoan::where('status', 'repaying')
                               ->where('maturity_date', '<=', Carbon::now()->toDateString())
                               ->whereColumn('amount_paid', '>=', 'total_repayment')
                               ->get();

        $processed = 0;

        foreach ($maturedLoans as $loan) {
            try {
                $loan->status = 'completed';
                $loan->completion_date = Carbon::now()->toDateString();
                $loan->save();

                // Update user credit score
                $this->updateCreditScore($loan->user, 'loan_completed');

                $processed++;
                Log::info("Loan {$loan->id} marked as completed");
            } catch (\Exception $e) {
                Log::error("Failed to process matured loan {$loan->id}: " . $e->getMessage());
            }
        }

        return $processed;
    }

    /**
     * Process individual auto-debit
     */
    private function processAutoDebit(LoanRepayment $repayment): array
    {
        $loan = $repayment->loan;
        $user = $loan->user;

        // Check if user has sufficient balance
        if ($user->balance < $repayment->amount) {
            return [
                'success' => false,
                'message' => 'Insufficient balance for auto-debit'
            ];
        }

        try {
            // Deduct from user balance
            $user->balance -= $repayment->amount;
            $user->save();

            // Update repayment
            $repayment->status = 'paid';
            $repayment->paid_date = Carbon::now()->toDateString();
            $repayment->payment_method = 'auto_debit';
            $repayment->save();

            // Update loan
            $loan->amount_paid += $repayment->amount;
            $loan->outstanding_balance -= $repayment->amount;
            
            if ($loan->outstanding_balance <= 0) {
                $loan->status = 'completed';
                $loan->completion_date = Carbon::now()->toDateString();
            }
            
            $loan->save();

            // Create transaction record
            \App\Models\Transaction::create([
                'user_id' => $user->id,
                'amount' => $repayment->amount,
                'post_balance' => $user->balance,
                'charge' => 0,
                'trx_type' => '-',
                'details' => "Loan repayment - {$loan->reference}",
                'trx' => getTrx(),
                'remark' => 'loan_repayment'
            ]);

            // Update credit score
            $this->updateCreditScore($user, 'on_time_payment');

            return [
                'success' => true,
                'message' => 'Auto-debit processed successfully'
            ];

        } catch (\Exception $e) {
            // Rollback user balance if transaction failed
            $user->balance += $repayment->amount;
            $user->save();

            return [
                'success' => false,
                'message' => 'Auto-debit processing failed: ' . $e->getMessage()
            ];
        }
    }

    /**
     * Apply late payment penalty
     */
    private function applyLatePenalty(UserLoan $loan): void
    {
        $penaltyRate = $loan->product->late_payment_penalty ?? 5.0; // 5% default
        $penaltyAmount = ($loan->monthly_payment * $penaltyRate) / 100;

        $loan->outstanding_balance += $penaltyAmount;
        $loan->total_repayment += $penaltyAmount;
        $loan->save();

        // Create penalty transaction
        \App\Models\Transaction::create([
            'user_id' => $loan->user_id,
            'amount' => $penaltyAmount,
            'post_balance' => $loan->user->balance,
            'charge' => 0,
            'trx_type' => '-',
            'details' => "Late payment penalty - {$loan->reference}",
            'trx' => getTrx(),
            'remark' => 'loan_penalty'
        ]);

        Log::info("Applied late penalty of ₦{$penaltyAmount} to loan {$loan->id}");
    }

    /**
     * Update user credit score
     */
    private function updateCreditScore(User $user, string $action): void
    {
        $currentScore = $user->credit_score ?? 500;
        $newScore = $currentScore;

        switch ($action) {
            case 'on_time_payment':
                $newScore = min(850, $currentScore + 5);
                break;
            case 'late_payment':
                $newScore = max(300, $currentScore - 10);
                break;
            case 'loan_completed':
                $newScore = min(850, $currentScore + 20);
                break;
            case 'loan_defaulted':
                $newScore = max(300, $currentScore - 50);
                break;
        }

        if ($newScore !== $currentScore) {
            $user->credit_score = $newScore;
            $user->save();

            Log::info("Updated credit score for user {$user->id}: {$currentScore} -> {$newScore} ({$action})");
        }
    }

    /**
     * Handle job failure
     */
    public function failed(\Throwable $exception): void
    {
        Log::error('Loan repayment processing job failed: ' . $exception->getMessage());
    }
}
