import 'package:flutter/cupertino.dart';

class Dimensions {
  // font-size
  static const double fontOverSmall = 7.00;
  static const double fontExtraSmall = 9.00;
  static const double fontSmall = 11.00;
  static const double fontDefault = 13.00;
  static const double fontLarge = 14.00;
  static const double fontMediumLarge = 17.00;
  static const double fontExtraLarge = 19.00;
  static const double fontTitle = 32.00;
  static const double fontOverLarge = 21.00;
  static const double fontBalance = 50.00;

  static const double defaultButtonH = 45;
  static const double defaultRadius = 4;

  static const double space5 = 5;
  static const double space7 = 7;
  static const double space8 = 8;
  static const double space3 = 3;
  static const double space2 = 2;
  static const double space10 = 10;
  static const double space15 = 15;
  static const double space16 = 16;
  static const double space17 = 17;
  static const double space12 = 12;
  static const double space20 = 20;
  static const double space25 = 25;
  static const double space30 = 30;
  static const double space35 = 35;
  static const double space40 = 40;
  static const double space45 = 45;
  static const double space50 = 50;
  static const double space60 = 60;

  static const EdgeInsets onBoardPadding = EdgeInsets.all(40);
  static const EdgeInsets screenPaddingHV = EdgeInsets.symmetric(horizontal: space15, vertical: space20);
  static const EdgeInsets screenPaddingHV1 = EdgeInsets.symmetric(horizontal: space12, vertical: space20);
  static const EdgeInsets defaultPaddingHV = EdgeInsets.symmetric(vertical: space20, horizontal: space15);
  static const double cardRadius = 8;
  static const double bottomSheetRadius = 15;
  static const double textToTextSpace = 8;
  static const double buttonRadius = 4;
  static const double mediumRadius = 8;
  static const double largeRadius = 12;
  static const double extraRadius = 16;
  static const double indicatorSize = 75;

}
