import 'package:flutter/services.dart';
import 'package:get/get.dart';
import 'package:local_auth/local_auth.dart';
import 'package:shared_preferences/shared_preferences.dart';

enum BiometricType { none, fingerprint, face, iris }
enum BiometricStatus { available, notAvailable, notEnrolled, unknown }

class BiometricService extends GetxService {
  static const String _biometricEnabledKey = 'biometric_enabled';
  static const String _biometricTypeKey = 'biometric_type';
  
  final LocalAuthentication _localAuth = LocalAuthentication();
  final SharedPreferences _prefs = Get.find();
  
  // Observable properties
  final RxBool isBiometricEnabled = false.obs;
  final Rx<BiometricType> availableBiometricType = BiometricType.none.obs;
  final Rx<BiometricStatus> biometricStatus = BiometricStatus.unknown.obs;

  @override
  void onInit() {
    super.onInit();
    _loadBiometricSettings();
    checkBiometricAvailability();
  }

  /// Load biometric settings from shared preferences
  void _loadBiometricSettings() {
    isBiometricEnabled.value = _prefs.getBool(_biometricEnabledKey) ?? false;
    final typeString = _prefs.getString(_biometricTypeKey) ?? 'none';
    availableBiometricType.value = BiometricType.values.firstWhere(
      (type) => type.toString() == typeString,
      orElse: () => BiometricType.none,
    );
  }

  /// Save biometric settings to shared preferences
  Future<void> _saveBiometricSettings() async {
    await _prefs.setBool(_biometricEnabledKey, isBiometricEnabled.value);
    await _prefs.setString(_biometricTypeKey, availableBiometricType.value.toString());
  }

  /// Check if biometric authentication is available on the device
  Future<BiometricStatus> checkBiometricAvailability() async {
    try {
      // Check if device supports biometric authentication
      final bool isAvailable = await _localAuth.canCheckBiometrics;
      if (!isAvailable) {
        biometricStatus.value = BiometricStatus.notAvailable;
        return BiometricStatus.notAvailable;
      }

      // Get available biometric types
      final List<BiometricType> availableTypes = await getAvailableBiometrics();
      if (availableTypes.isEmpty) {
        biometricStatus.value = BiometricStatus.notEnrolled;
        return BiometricStatus.notEnrolled;
      }

      // Set the primary biometric type
      availableBiometricType.value = availableTypes.first;
      biometricStatus.value = BiometricStatus.available;
      return BiometricStatus.available;
    } catch (e) {
      print('Error checking biometric availability: $e');
      biometricStatus.value = BiometricStatus.unknown;
      return BiometricStatus.unknown;
    }
  }

  /// Get list of available biometric types
  Future<List<BiometricType>> getAvailableBiometrics() async {
    try {
      final List<BiometricType> availableTypes = await _localAuth.getAvailableBiometrics();

      List<BiometricType> mappedTypes = [];
      for (var type in availableTypes) {
        // Note: This mapping needs to be updated based on actual local_auth BiometricType enum
        if (type.toString().contains('fingerprint')) {
          mappedTypes.add(BiometricType.fingerprint);
        } else if (type.toString().contains('face')) {
          mappedTypes.add(BiometricType.face);
        } else if (type.toString().contains('iris')) {
          mappedTypes.add(BiometricType.iris);
        }
      }

      return mappedTypes;
    } catch (e) {
      print('Error getting available biometrics: $e');
      return [];
    }
  }

  /// Authenticate using biometric
  Future<bool> authenticateWithBiometric({
    String reason = 'Please authenticate to continue',
    bool useErrorDialogs = true,
    bool stickyAuth = true,
  }) async {
    try {
      // Check if biometric is available
      if (biometricStatus.value != BiometricStatus.available) {
        await checkBiometricAvailability();
        if (biometricStatus.value != BiometricStatus.available) {
          return false;
        }
      }

      // Perform authentication
      final bool didAuthenticate = await _localAuth.authenticate(
        localizedReason: reason,
        options: AuthenticationOptions(
          useErrorDialogs: useErrorDialogs,
          stickyAuth: stickyAuth,
          biometricOnly: true,
        ),
      );

      return didAuthenticate;
    } on PlatformException catch (e) {
      print('Biometric authentication error: ${e.message}');
      _handleBiometricError(e);
      return false;
    } catch (e) {
      print('Unexpected biometric error: $e');
      return false;
    }
  }

  /// Enable biometric authentication
  Future<bool> enableBiometric() async {
    try {
      // First check if biometric is available
      final status = await checkBiometricAvailability();
      if (status != BiometricStatus.available) {
        _showBiometricNotAvailableDialog(status);
        return false;
      }

      // Test authentication
      final bool authenticated = await authenticateWithBiometric(
        reason: 'Authenticate to enable biometric login',
      );

      if (authenticated) {
        isBiometricEnabled.value = true;
        await _saveBiometricSettings();
        
        Get.snackbar(
          'Success',
          'Biometric authentication enabled successfully',
          snackPosition: SnackPosition.BOTTOM,
          backgroundColor: Get.theme.primaryColor.withOpacity(0.1),
          colorText: Get.theme.primaryColor,
        );
        
        return true;
      }
      
      return false;
    } catch (e) {
      print('Error enabling biometric: $e');
      return false;
    }
  }

  /// Disable biometric authentication
  Future<void> disableBiometric() async {
    isBiometricEnabled.value = false;
    await _saveBiometricSettings();
    
    Get.snackbar(
      'Disabled',
      'Biometric authentication has been disabled',
      snackPosition: SnackPosition.BOTTOM,
      backgroundColor: Get.theme.colorScheme.error.withOpacity(0.1),
      colorText: Get.theme.colorScheme.error,
    );
  }

  /// Handle biometric authentication errors
  void _handleBiometricError(PlatformException error) {
    String message;
    switch (error.code) {
      case 'NotAvailable':
        message = 'Biometric authentication is not available on this device';
        break;
      case 'NotEnrolled':
        message = 'No biometric credentials are enrolled. Please set up biometric authentication in your device settings';
        break;
      case 'LockedOut':
        message = 'Biometric authentication is temporarily locked. Please try again later';
        break;
      case 'PermanentlyLockedOut':
        message = 'Biometric authentication is permanently locked. Please use your device passcode';
        break;
      case 'UserCancel':
        message = 'Authentication was cancelled by user';
        break;
      case 'UserFallback':
        message = 'User chose to use fallback authentication';
        break;
      case 'SystemCancel':
        message = 'Authentication was cancelled by system';
        break;
      case 'InvalidContext':
        message = 'Authentication context is invalid';
        break;
      case 'NotRecognized':
        message = 'Biometric not recognized. Please try again';
        break;
      default:
        message = 'An unknown error occurred during biometric authentication';
    }

    if (error.code != 'UserCancel') {
      Get.snackbar(
        'Authentication Error',
        message,
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Get.theme.colorScheme.error.withOpacity(0.1),
        colorText: Get.theme.colorScheme.error,
        duration: const Duration(seconds: 4),
      );
    }
  }

  /// Show dialog when biometric is not available
  void _showBiometricNotAvailableDialog(BiometricStatus status) {
    String title;
    String message;

    switch (status) {
      case BiometricStatus.notAvailable:
        title = 'Biometric Not Available';
        message = 'Your device does not support biometric authentication.';
        break;
      case BiometricStatus.notEnrolled:
        title = 'Biometric Not Set Up';
        message = 'Please set up biometric authentication in your device settings first.';
        break;
      default:
        title = 'Biometric Error';
        message = 'Unable to access biometric authentication at this time.';
    }

    Get.dialog(
      AlertDialog(
        title: Text(title),
        content: Text(message),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            child: const Text('OK'),
          ),
        ],
      ),
    );
  }

  /// Get biometric type display name
  String getBiometricTypeName(BiometricType type) {
    switch (type) {
      case BiometricType.fingerprint:
        return 'Fingerprint';
      case BiometricType.face:
        return 'Face ID';
      case BiometricType.iris:
        return 'Iris';
      case BiometricType.none:
        return 'None';
    }
  }

  /// Get biometric type icon
  String getBiometricTypeIcon(BiometricType type) {
    switch (type) {
      case BiometricType.fingerprint:
        return '👆';
      case BiometricType.face:
        return '👤';
      case BiometricType.iris:
        return '👁️';
      case BiometricType.none:
        return '🔒';
    }
  }

  /// Check if biometric login should be shown
  bool shouldShowBiometricLogin() {
    return isBiometricEnabled.value && 
           biometricStatus.value == BiometricStatus.available;
  }
}
