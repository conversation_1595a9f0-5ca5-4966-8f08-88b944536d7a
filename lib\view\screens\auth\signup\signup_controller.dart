import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:viserpay/core/utils/my_color.dart';

enum SignupStep {
  personalInfo,    // First Name, Last Name, Phone, Email
  security,        // PIN and Password
  verification,    // OTP
  bvnVerification, // BVN
  accountCreation  // Final step
}

class SignupController extends GetxController {
  // Current step
  final Rx<SignupStep> currentStep = SignupStep.personalInfo.obs;
  final RxBool isLoading = false.obs;
  final RxString errorMessage = ''.obs;

  // Form controllers
  final firstNameController = TextEditingController();
  final lastNameController = TextEditingController();
  final phoneController = TextEditingController();
  final emailController = TextEditingController();
  final pinController = TextEditingController();
  final passwordController = TextEditingController();
  final confirmPasswordController = TextEditingController();
  final otpController = TextEditingController();
  final bvnController = TextEditingController();

  // Form keys
  final personalInfoFormKey = GlobalKey<FormState>();
  final securityFormKey = GlobalKey<FormState>();
  final otpFormKey = GlobalKey<FormState>();
  final bvnFormKey = GlobalKey<FormState>();

  // Validation states
  final RxBool isPersonalInfoValid = false.obs;
  final RxBool isSecurityValid = false.obs;
  final RxBool isOtpValid = false.obs;
  final RxBool isBvnValid = false.obs;

  // OTP related
  final RxInt otpCountdown = 0.obs;
  final RxBool canResendOtp = true.obs;

  // Password visibility
  final RxBool showPassword = false.obs;
  final RxBool showConfirmPassword = false.obs;

  @override
  void onInit() {
    super.onInit();
    _setupValidationListeners();
  }

  @override
  void onClose() {
    _disposeControllers();
    super.onClose();
  }

  void _setupValidationListeners() {
    // Personal info validation
    firstNameController.addListener(_validatePersonalInfo);
    lastNameController.addListener(_validatePersonalInfo);
    phoneController.addListener(_validatePersonalInfo);
    emailController.addListener(_validatePersonalInfo);

    // Security validation
    pinController.addListener(_validateSecurity);
    passwordController.addListener(_validateSecurity);
    confirmPasswordController.addListener(_validateSecurity);

    // OTP validation
    otpController.addListener(_validateOtp);

    // BVN validation
    bvnController.addListener(_validateBvn);
  }

  void _validatePersonalInfo() {
    isPersonalInfoValid.value = firstNameController.text.trim().isNotEmpty &&
        lastNameController.text.trim().isNotEmpty &&
        phoneController.text.trim().length >= 10 &&
        GetUtils.isEmail(emailController.text.trim());
  }

  void _validateSecurity() {
    final pin = pinController.text.trim();
    final password = passwordController.text.trim();
    final confirmPassword = confirmPasswordController.text.trim();

    isSecurityValid.value = pin.length == 4 &&
        password.length >= 8 &&
        password == confirmPassword &&
        _isValidPassword(password);
  }

  void _validateOtp() {
    isOtpValid.value = otpController.text.trim().length == 6;
  }

  void _validateBvn() {
    isBvnValid.value = bvnController.text.trim().length == 11;
  }

  bool _isValidPassword(String password) {
    // Password must contain at least one uppercase, lowercase, number, and special character
    final hasUppercase = password.contains(RegExp(r'[A-Z]'));
    final hasLowercase = password.contains(RegExp(r'[a-z]'));
    final hasDigits = password.contains(RegExp(r'[0-9]'));
    final hasSpecialCharacters = password.contains(RegExp(r'[!@#$%^&*(),.?":{}|<>]'));

    return hasUppercase && hasLowercase && hasDigits && hasSpecialCharacters;
  }

  void _disposeControllers() {
    firstNameController.dispose();
    lastNameController.dispose();
    phoneController.dispose();
    emailController.dispose();
    pinController.dispose();
    passwordController.dispose();
    confirmPasswordController.dispose();
    otpController.dispose();
    bvnController.dispose();
  }

  // Navigation methods
  Future<void> nextStep() async {
    errorMessage.value = '';
    
    switch (currentStep.value) {
      case SignupStep.personalInfo:
        if (personalInfoFormKey.currentState?.validate() ?? false) {
          currentStep.value = SignupStep.security;
        }
        break;
      case SignupStep.security:
        if (securityFormKey.currentState?.validate() ?? false) {
          await _sendOtp();
        }
        break;
      case SignupStep.verification:
        if (otpFormKey.currentState?.validate() ?? false) {
          await _verifyOtp();
        }
        break;
      case SignupStep.bvnVerification:
        if (bvnFormKey.currentState?.validate() ?? false) {
          await _verifyBvn();
        }
        break;
      case SignupStep.accountCreation:
        await _createAccount();
        break;
    }
  }

  void previousStep() {
    switch (currentStep.value) {
      case SignupStep.security:
        currentStep.value = SignupStep.personalInfo;
        break;
      case SignupStep.verification:
        currentStep.value = SignupStep.security;
        break;
      case SignupStep.bvnVerification:
        currentStep.value = SignupStep.verification;
        break;
      case SignupStep.accountCreation:
        currentStep.value = SignupStep.bvnVerification;
        break;
      default:
        break;
    }
  }

  // API calls
  Future<void> _sendOtp() async {
    isLoading.value = true;
    try {
      // TODO: Implement OTP sending API call
      await Future.delayed(const Duration(seconds: 2)); // Simulate API call
      
      currentStep.value = SignupStep.verification;
      _startOtpCountdown();
      
      Get.snackbar(
        'OTP Sent',
        'Verification code sent to ${phoneController.text}',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: MyColor.colorGreen.withOpacity(0.1),
        colorText: MyColor.colorGreen,
      );
    } catch (e) {
      errorMessage.value = 'Failed to send OTP. Please try again.';
    } finally {
      isLoading.value = false;
    }
  }

  Future<void> _verifyOtp() async {
    isLoading.value = true;
    try {
      // TODO: Implement OTP verification API call
      await Future.delayed(const Duration(seconds: 2)); // Simulate API call
      
      currentStep.value = SignupStep.bvnVerification;
      
      Get.snackbar(
        'OTP Verified',
        'Phone number verified successfully',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: MyColor.colorGreen.withOpacity(0.1),
        colorText: MyColor.colorGreen,
      );
    } catch (e) {
      errorMessage.value = 'Invalid OTP. Please try again.';
    } finally {
      isLoading.value = false;
    }
  }

  Future<void> _verifyBvn() async {
    isLoading.value = true;
    try {
      // TODO: Implement BVN verification API call
      await Future.delayed(const Duration(seconds: 3)); // Simulate API call
      
      currentStep.value = SignupStep.accountCreation;
      
      Get.snackbar(
        'BVN Verified',
        'Bank verification successful',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: MyColor.colorGreen.withOpacity(0.1),
        colorText: MyColor.colorGreen,
      );
    } catch (e) {
      errorMessage.value = 'BVN verification failed. Please check and try again.';
    } finally {
      isLoading.value = false;
    }
  }

  Future<void> _createAccount() async {
    isLoading.value = true;
    try {
      // TODO: Implement account creation API call
      await Future.delayed(const Duration(seconds: 3)); // Simulate API call
      
      Get.snackbar(
        'Account Created',
        'Welcome to ViserPay! Your account has been created successfully.',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: MyColor.colorGreen.withOpacity(0.1),
        colorText: MyColor.colorGreen,
        duration: const Duration(seconds: 4),
      );
      
      // Navigate to login or home screen
      // Get.offAllNamed(RouteHelper.loginScreen);
    } catch (e) {
      errorMessage.value = 'Account creation failed. Please try again.';
    } finally {
      isLoading.value = false;
    }
  }

  Future<void> resendOtp() async {
    if (!canResendOtp.value) return;
    
    isLoading.value = true;
    try {
      // TODO: Implement resend OTP API call
      await Future.delayed(const Duration(seconds: 1)); // Simulate API call
      
      _startOtpCountdown();
      otpController.clear();
      
      Get.snackbar(
        'OTP Resent',
        'New verification code sent to ${phoneController.text}',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: MyColor.colorGreen.withOpacity(0.1),
        colorText: MyColor.colorGreen,
      );
    } catch (e) {
      errorMessage.value = 'Failed to resend OTP. Please try again.';
    } finally {
      isLoading.value = false;
    }
  }

  void _startOtpCountdown() {
    canResendOtp.value = false;
    otpCountdown.value = 60;
    
    Future.doWhile(() async {
      await Future.delayed(const Duration(seconds: 1));
      otpCountdown.value--;
      
      if (otpCountdown.value <= 0) {
        canResendOtp.value = true;
        return false;
      }
      return true;
    });
  }

  void togglePasswordVisibility() {
    showPassword.value = !showPassword.value;
  }

  void toggleConfirmPasswordVisibility() {
    showConfirmPassword.value = !showConfirmPassword.value;
  }

  // Getters for step information
  String get currentStepTitle {
    switch (currentStep.value) {
      case SignupStep.personalInfo:
        return 'Personal Information';
      case SignupStep.security:
        return 'Security Setup';
      case SignupStep.verification:
        return 'Phone Verification';
      case SignupStep.bvnVerification:
        return 'BVN Verification';
      case SignupStep.accountCreation:
        return 'Creating Account';
    }
  }

  String get currentStepDescription {
    switch (currentStep.value) {
      case SignupStep.personalInfo:
        return 'Enter your basic information to get started';
      case SignupStep.security:
        return 'Create a secure PIN and password for your account';
      case SignupStep.verification:
        return 'Enter the verification code sent to your phone';
      case SignupStep.bvnVerification:
        return 'Verify your identity with your Bank Verification Number';
      case SignupStep.accountCreation:
        return 'Setting up your ViserPay account';
    }
  }

  double get progressValue {
    switch (currentStep.value) {
      case SignupStep.personalInfo:
        return 0.2;
      case SignupStep.security:
        return 0.4;
      case SignupStep.verification:
        return 0.6;
      case SignupStep.bvnVerification:
        return 0.8;
      case SignupStep.accountCreation:
        return 1.0;
    }
  }

  bool get canGoBack {
    return currentStep.value != SignupStep.personalInfo && 
           currentStep.value != SignupStep.accountCreation;
  }

  bool get canContinue {
    switch (currentStep.value) {
      case SignupStep.personalInfo:
        return isPersonalInfoValid.value;
      case SignupStep.security:
        return isSecurityValid.value;
      case SignupStep.verification:
        return isOtpValid.value;
      case SignupStep.bvnVerification:
        return isBvnValid.value;
      case SignupStep.accountCreation:
        return false; // Auto-proceeds
    }
  }
}
