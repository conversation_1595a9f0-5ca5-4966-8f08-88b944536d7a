class AccountResponseModel {
  String? status;
  Message? message;
  Data? data;

  AccountResponseModel({
    this.status,
    this.message,
    this.data,
  });

  factory AccountResponseModel.fromJson(Map<String, dynamic> json) => AccountResponseModel(
        status: json["status"],
        message: json["message"] == null ? null : Message.fromJson(json["message"]),
        data: json["data"] == null ? null : Data.fromJson(json["data"]),
      );

  Map<String, dynamic> toJson() => {
        "status": status,
        "message": message?.toJson(),
        "data": data?.toJson(),
      };
}

class Message {
  List<String>? success;
  List<String>? error;

  Message({
    this.success,
    this.error,
  });

  factory Message.fromJson(Map<String, dynamic> json) => Message(
        success: json["success"] == null ? [] : List<String>.from(json["success"]!.map((x) => x)),
        error: json["error"] == null ? [] : List<String>.from(json["error"]!.map((x) => x)),
      );

  Map<String, dynamic> toJson() => {
        "success": success == null ? [] : List<dynamic>.from(success!.map((x) => x)),
        "error": error == null ? [] : List<dynamic>.from(error!.map((x) => x)),
      };
}

class Data {
  List<Account>? accounts;
  List<AccountType>? accountTypes;
  String? currentBalance;

  Data({
    this.accounts,
    this.accountTypes,
    this.currentBalance,
  });

  factory Data.fromJson(Map<String, dynamic> json) => Data(
        accounts: json["accounts"] == null ? [] : List<Account>.from(json["accounts"]!.map((x) => Account.fromJson(x))),
        accountTypes: json["account_types"] == null ? [] : List<AccountType>.from(json["account_types"]!.map((x) => AccountType.fromJson(x))),
        currentBalance: json["current_balance"],
      );

  Map<String, dynamic> toJson() => {
        "accounts": accounts == null ? [] : List<dynamic>.from(accounts!.map((x) => x.toJson())),
        "account_types": accountTypes == null ? [] : List<dynamic>.from(accountTypes!.map((x) => x.toJson())),
        "current_balance": currentBalance,
      };
}

class Account {
  int? id;
  int? userId;
  String? accountNumber;
  String? accountName;
  AccountType? accountType;
  String? subType; // For Personal accounts: REGULAR, KIDS, PREMIUM
  String? balance;
  String? interestRate;
  String? status;
  bool? isDefault;
  // Business account specific fields
  String? businessName;
  String? businessCategory;
  String? businessAddress;
  String? businessPhone;
  String? businessEmail;
  String? businessLicense;
  bool? isStoreEnabled;
  bool? isEscrowEnabled;
  String? createdAt;
  String? updatedAt;

  Account({
    this.id,
    this.userId,
    this.accountNumber,
    this.accountName,
    this.accountType,
    this.subType,
    this.balance,
    this.interestRate,
    this.status,
    this.isDefault,
    this.businessName,
    this.businessCategory,
    this.businessAddress,
    this.businessPhone,
    this.businessEmail,
    this.businessLicense,
    this.isStoreEnabled,
    this.isEscrowEnabled,
    this.createdAt,
    this.updatedAt,
  });

  factory Account.fromJson(Map<String, dynamic> json) => Account(
        id: json["id"],
        userId: json["user_id"],
        accountNumber: json["account_number"],
        accountName: json["account_name"],
        accountType: json["account_type"] == null ? null : AccountType.fromJson(json["account_type"]),
        subType: json["sub_type"],
        balance: json["balance"],
        interestRate: json["interest_rate"],
        status: json["status"],
        isDefault: json["is_default"] == 1,
        businessName: json["business_name"],
        businessCategory: json["business_category"],
        businessAddress: json["business_address"],
        businessPhone: json["business_phone"],
        businessEmail: json["business_email"],
        businessLicense: json["business_license"],
        isStoreEnabled: json["is_store_enabled"] == 1,
        isEscrowEnabled: json["is_escrow_enabled"] == 1,
        createdAt: json["created_at"],
        updatedAt: json["updated_at"],
      );

  Map<String, dynamic> toJson() => {
        "id": id,
        "user_id": userId,
        "account_number": accountNumber,
        "account_name": accountName,
        "account_type": accountType?.toJson(),
        "sub_type": subType,
        "balance": balance,
        "interest_rate": interestRate,
        "status": status,
        "is_default": isDefault == true ? 1 : 0,
        "business_name": businessName,
        "business_category": businessCategory,
        "business_address": businessAddress,
        "business_phone": businessPhone,
        "business_email": businessEmail,
        "business_license": businessLicense,
        "is_store_enabled": isStoreEnabled == true ? 1 : 0,
        "is_escrow_enabled": isEscrowEnabled == true ? 1 : 0,
        "created_at": createdAt,
        "updated_at": updatedAt,
      };
}

class AccountType {
  int? id;
  String? name;
  String? code;
  String? description;
  String? interestRate;
  String? minimumBalance;
  String? maximumBalance;
  String? features;
  String? status;

  AccountType({
    this.id,
    this.name,
    this.code,
    this.description,
    this.interestRate,
    this.minimumBalance,
    this.maximumBalance,
    this.features,
    this.status,
  });

  factory AccountType.fromJson(Map<String, dynamic> json) => AccountType(
        id: json["id"],
        name: json["name"],
        code: json["code"],
        description: json["description"],
        interestRate: json["interest_rate"],
        minimumBalance: json["minimum_balance"],
        maximumBalance: json["maximum_balance"],
        features: json["features"],
        status: json["status"],
      );

  Map<String, dynamic> toJson() => {
        "id": id,
        "name": name,
        "code": code,
        "description": description,
        "interest_rate": interestRate,
        "minimum_balance": minimumBalance,
        "maximum_balance": maximumBalance,
        "features": features,
        "status": status,
      };
}

// Account creation request model
class CreateAccountRequest {
  String accountName;
  int accountTypeId;
  String? subType;
  String? initialDeposit;
  // Business account fields
  String? businessName;
  String? businessCategory;
  String? businessAddress;
  String? businessPhone;
  String? businessEmail;
  String? businessLicense;

  CreateAccountRequest({
    required this.accountName,
    required this.accountTypeId,
    this.subType,
    this.initialDeposit,
    this.businessName,
    this.businessCategory,
    this.businessAddress,
    this.businessPhone,
    this.businessEmail,
    this.businessLicense,
  });

  Map<String, dynamic> toJson() => {
        "account_name": accountName,
        "account_type_id": accountTypeId,
        "sub_type": subType,
        "initial_deposit": initialDeposit,
        if (businessName != null) "business_name": businessName,
        if (businessCategory != null) "business_category": businessCategory,
        if (businessAddress != null) "business_address": businessAddress,
        if (businessPhone != null) "business_phone": businessPhone,
        if (businessEmail != null) "business_email": businessEmail,
        if (businessLicense != null) "business_license": businessLicense,
      };
}

// Account types enum for easy reference
enum AccountTypeCode {
  personal('PERSONAL'),
  business('BUSINESS'),
  kids('KIDS'); // Sub-type of personal

  const AccountTypeCode(this.code);
  final String code;
}

// Account sub-types for Personal accounts
enum PersonalAccountSubType {
  regular('REGULAR'),
  kids('KIDS'),
  premium('PREMIUM');

  const PersonalAccountSubType(this.code);
  final String code;
}
