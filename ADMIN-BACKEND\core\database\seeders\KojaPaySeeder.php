<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\AdminRate;
use App\Models\InvestmentProduct;
use App\Models\LoanProduct;
use App\Models\AccountTier;

class KojaPaySeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $this->seedAdminRates();
        $this->seedInvestmentProducts();
        $this->seedLoanProducts();
        $this->seedAccountTiers();
    }

    /**
     * Seed admin configurable rates
     */
    private function seedAdminRates(): void
    {
        AdminRate::seedDefaultRates();
        $this->command->info('Admin rates seeded successfully');
    }

    /**
     * Seed investment products
     */
    private function seedInvestmentProducts(): void
    {
        $products = [
            [
                'name' => 'Nigerian Treasury Bills',
                'type' => 'treasury_bills',
                'description' => 'Government-backed treasury bills with guaranteed returns',
                'minimum_amount' => 100000,
                'maximum_amount' => ********,
                'interest_rate_min' => 10.0,
                'interest_rate_max' => 15.0,
                'risk_level' => 'low',
                'duration_days' => 91,
                'management_fee' => 0.5,
                'is_active' => true,
                'features' => [
                    'Government guaranteed',
                    'Fixed returns',
                    'Low risk',
                    'Quarterly maturity'
                ]
            ],
            [
                'name' => 'Equity Mutual Funds',
                'type' => 'mutual_funds',
                'description' => 'Diversified portfolio of Nigerian stocks managed by professionals',
                'minimum_amount' => 25000,
                'maximum_amount' => 10000000,
                'interest_rate_min' => 15.0,
                'interest_rate_max' => 25.0,
                'risk_level' => 'medium',
                'duration_days' => null,
                'management_fee' => 2.0,
                'is_active' => true,
                'features' => [
                    'Professional management',
                    'Diversified portfolio',
                    'Liquid investment',
                    'Potential for high returns'
                ]
            ],
            [
                'name' => 'Corporate Bonds',
                'type' => 'corporate_bonds',
                'description' => 'Fixed-income securities issued by Nigerian corporations',
                'minimum_amount' => 50000,
                'maximum_amount' => 20000000,
                'interest_rate_min' => 12.0,
                'interest_rate_max' => 18.0,
                'risk_level' => 'medium',
                'duration_days' => 365,
                'management_fee' => 1.0,
                'is_active' => true,
                'features' => [
                    'Fixed income',
                    'Regular coupon payments',
                    'Medium risk',
                    'Annual maturity'
                ]
            ],
            [
                'name' => 'Nigerian Stock Exchange',
                'type' => 'stocks',
                'description' => 'Direct investment in Nigerian listed companies',
                'minimum_amount' => 10000,
                'maximum_amount' => ********,
                'interest_rate_min' => 20.0,
                'interest_rate_max' => 40.0,
                'risk_level' => 'high',
                'duration_days' => null,
                'management_fee' => 1.5,
                'is_active' => true,
                'features' => [
                    'High growth potential',
                    'Dividend income',
                    'Voting rights',
                    'High volatility'
                ]
            ],
            [
                'name' => 'Real Estate Investment Trust',
                'type' => 'real_estate',
                'description' => 'Investment in Nigerian real estate properties and developments',
                'minimum_amount' => 100000,
                'maximum_amount' => 25000000,
                'interest_rate_min' => 18.0,
                'interest_rate_max' => 30.0,
                'risk_level' => 'medium',
                'duration_days' => 1095, // 3 years
                'management_fee' => 2.5,
                'is_active' => true,
                'features' => [
                    'Real estate exposure',
                    'Rental income',
                    'Capital appreciation',
                    'Inflation hedge'
                ]
            ]
        ];

        foreach ($products as $productData) {
            InvestmentProduct::updateOrCreate(
                ['name' => $productData['name']],
                $productData
            );
        }

        $this->command->info('Investment products seeded successfully');
    }

    /**
     * Seed loan products
     */
    private function seedLoanProducts(): void
    {
        $products = LoanProduct::getDefaultProducts();

        foreach ($products as $productData) {
            LoanProduct::updateOrCreate(
                ['name' => $productData['name']],
                $productData
            );
        }

        $this->command->info('Loan products seeded successfully');
    }

    /**
     * Seed account tiers
     */
    private function seedAccountTiers(): void
    {
        $tiers = [
            [
                'name' => 'Basic',
                'level' => 1,
                'daily_limit' => 50000,
                'monthly_limit' => 200000,
                'single_transaction_limit' => 20000,
                'kyc_requirements' => [
                    'phone_verification' => true,
                    'email_verification' => true,
                    'basic_info' => true
                ],
                'features' => [
                    'Send money',
                    'Receive money',
                    'Pay bills',
                    'Mobile recharge',
                    'Basic savings'
                ],
                'benefits' => [
                    'Free account opening',
                    'Basic customer support',
                    'Mobile app access'
                ],
                'color_code' => '#28a745',
                'icon' => 'basic-tier.png',
                'is_active' => true
            ],
            [
                'name' => 'Standard',
                'level' => 2,
                'daily_limit' => 200000,
                'monthly_limit' => 1000000,
                'single_transaction_limit' => 100000,
                'kyc_requirements' => [
                    'phone_verification' => true,
                    'email_verification' => true,
                    'basic_info' => true,
                    'id_verification' => true,
                    'address_verification' => true
                ],
                'features' => [
                    'All Basic features',
                    'Bank transfers',
                    'Virtual cards',
                    'All savings products',
                    'Basic investments'
                ],
                'benefits' => [
                    'Higher transaction limits',
                    'Virtual card access',
                    'Priority support',
                    'Investment access'
                ],
                'color_code' => '#007bff',
                'icon' => 'standard-tier.png',
                'is_active' => true
            ],
            [
                'name' => 'Premium',
                'level' => 3,
                'daily_limit' => 1000000,
                'monthly_limit' => 5000000,
                'single_transaction_limit' => 500000,
                'kyc_requirements' => [
                    'phone_verification' => true,
                    'email_verification' => true,
                    'basic_info' => true,
                    'id_verification' => true,
                    'address_verification' => true,
                    'income_verification' => true,
                    'bvn_verification' => true
                ],
                'features' => [
                    'All Standard features',
                    'Physical cards',
                    'All investment products',
                    'Loan access',
                    'Advanced analytics'
                ],
                'benefits' => [
                    'Highest transaction limits',
                    'Physical card access',
                    'Loan eligibility',
                    'Dedicated support',
                    'Premium rates'
                ],
                'color_code' => '#ffc107',
                'icon' => 'premium-tier.png',
                'is_active' => true
            ],
            [
                'name' => 'VIP',
                'level' => 4,
                'daily_limit' => 5000000,
                'monthly_limit' => 20000000,
                'single_transaction_limit' => 2000000,
                'kyc_requirements' => [
                    'phone_verification' => true,
                    'email_verification' => true,
                    'basic_info' => true,
                    'id_verification' => true,
                    'address_verification' => true,
                    'income_verification' => true,
                    'bvn_verification' => true,
                    'nin_verification' => true,
                    'wealth_verification' => true
                ],
                'features' => [
                    'All Premium features',
                    'Unlimited transactions',
                    'Exclusive investments',
                    'Business loans',
                    'Personal relationship manager'
                ],
                'benefits' => [
                    'Unlimited limits',
                    'Exclusive products',
                    'Personal banker',
                    'VIP support',
                    'Best rates'
                ],
                'color_code' => '#dc3545',
                'icon' => 'vip-tier.png',
                'is_active' => true
            ]
        ];

        foreach ($tiers as $tierData) {
            AccountTier::updateOrCreate(
                ['level' => $tierData['level']],
                $tierData
            );
        }

        $this->command->info('Account tiers seeded successfully');
    }
}
