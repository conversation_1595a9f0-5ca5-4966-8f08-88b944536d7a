class BillPaymentResponseModel {
  String? status;
  Message? message;
  BillPaymentData? data;

  BillPaymentResponseModel({
    this.status,
    this.message,
    this.data,
  });

  factory BillPaymentResponseModel.fromJson(Map<String, dynamic> json) => BillPaymentResponseModel(
        status: json["status"],
        message: json["message"] == null ? null : Message.fromJson(json["message"]),
        data: json["data"] == null ? null : BillPaymentData.fromJson(json["data"]),
      );

  Map<String, dynamic> toJson() => {
        "status": status,
        "message": message?.toJson(),
        "data": data?.toJson(),
      };
}

class Message {
  List<String>? success;
  List<String>? error;

  Message({
    this.success,
    this.error,
  });

  factory Message.fromJson(Map<String, dynamic> json) => Message(
        success: json["success"] == null ? [] : List<String>.from(json["success"]!.map((x) => x)),
        error: json["error"] == null ? [] : List<String>.from(json["error"]!.map((x) => x)),
      );

  Map<String, dynamic> toJson() => {
        "success": success == null ? [] : List<dynamic>.from(success!.map((x) => x)),
        "error": error == null ? [] : List<dynamic>.from(error!.map((x) => x)),
      };
}

class BillPaymentData {
  List<BillCategory>? categories;
  List<BillProvider>? providers;
  List<BillTransaction>? transactions;
  BillTransaction? transaction;
  List<Beneficiary>? beneficiaries;
  BillPaymentStats? stats;

  BillPaymentData({
    this.categories,
    this.providers,
    this.transactions,
    this.transaction,
    this.beneficiaries,
    this.stats,
  });

  factory BillPaymentData.fromJson(Map<String, dynamic> json) => BillPaymentData(
        categories: json["categories"] == null ? [] : List<BillCategory>.from(json["categories"]!.map((x) => BillCategory.fromJson(x))),
        providers: json["providers"] == null ? [] : List<BillProvider>.from(json["providers"]!.map((x) => BillProvider.fromJson(x))),
        transactions: json["transactions"] == null ? [] : List<BillTransaction>.from(json["transactions"]!.map((x) => BillTransaction.fromJson(x))),
        transaction: json["transaction"] == null ? null : BillTransaction.fromJson(json["transaction"]),
        beneficiaries: json["beneficiaries"] == null ? [] : List<Beneficiary>.from(json["beneficiaries"]!.map((x) => Beneficiary.fromJson(x))),
        stats: json["stats"] == null ? null : BillPaymentStats.fromJson(json["stats"]),
      );

  Map<String, dynamic> toJson() => {
        "categories": categories == null ? [] : List<dynamic>.from(categories!.map((x) => x.toJson())),
        "providers": providers == null ? [] : List<dynamic>.from(providers!.map((x) => x.toJson())),
        "transactions": transactions == null ? [] : List<dynamic>.from(transactions!.map((x) => x.toJson())),
        "transaction": transaction?.toJson(),
        "beneficiaries": beneficiaries == null ? [] : List<dynamic>.from(beneficiaries!.map((x) => x.toJson())),
        "stats": stats?.toJson(),
      };
}

class BillCategory {
  int? id;
  String? name;
  String? code;
  String? description;
  String? icon;
  String? color;
  bool? isActive;
  int? sortOrder;
  List<BillProvider>? providers;

  BillCategory({
    this.id,
    this.name,
    this.code,
    this.description,
    this.icon,
    this.color,
    this.isActive,
    this.sortOrder,
    this.providers,
  });

  factory BillCategory.fromJson(Map<String, dynamic> json) => BillCategory(
        id: json["id"],
        name: json["name"],
        code: json["code"],
        description: json["description"],
        icon: json["icon"],
        color: json["color"],
        isActive: json["is_active"] == 1,
        sortOrder: json["sort_order"],
        providers: json["providers"] == null ? [] : List<BillProvider>.from(json["providers"]!.map((x) => BillProvider.fromJson(x))),
      );

  Map<String, dynamic> toJson() => {
        "id": id,
        "name": name,
        "code": code,
        "description": description,
        "icon": icon,
        "color": color,
        "is_active": isActive == true ? 1 : 0,
        "sort_order": sortOrder,
        "providers": providers == null ? [] : List<dynamic>.from(providers!.map((x) => x.toJson())),
      };
}

class BillProvider {
  int? id;
  int? categoryId;
  String? name;
  String? code;
  String? description;
  String? logo;
  bool? isActive;
  String? minAmount;
  String? maxAmount;
  String? fee;
  String? feeType; // 'fixed', 'percentage'
  List<BillPlan>? plans;
  List<String>? requiredFields;
  String? validationRegex;
  String? validationMessage;

  BillProvider({
    this.id,
    this.categoryId,
    this.name,
    this.code,
    this.description,
    this.logo,
    this.isActive,
    this.minAmount,
    this.maxAmount,
    this.fee,
    this.feeType,
    this.plans,
    this.requiredFields,
    this.validationRegex,
    this.validationMessage,
  });

  factory BillProvider.fromJson(Map<String, dynamic> json) => BillProvider(
        id: json["id"],
        categoryId: json["category_id"],
        name: json["name"],
        code: json["code"],
        description: json["description"],
        logo: json["logo"],
        isActive: json["is_active"] == 1,
        minAmount: json["min_amount"],
        maxAmount: json["max_amount"],
        fee: json["fee"],
        feeType: json["fee_type"],
        plans: json["plans"] == null ? [] : List<BillPlan>.from(json["plans"]!.map((x) => BillPlan.fromJson(x))),
        requiredFields: json["required_fields"] == null ? [] : List<String>.from(json["required_fields"]!.map((x) => x)),
        validationRegex: json["validation_regex"],
        validationMessage: json["validation_message"],
      );

  Map<String, dynamic> toJson() => {
        "id": id,
        "category_id": categoryId,
        "name": name,
        "code": code,
        "description": description,
        "logo": logo,
        "is_active": isActive == true ? 1 : 0,
        "min_amount": minAmount,
        "max_amount": maxAmount,
        "fee": fee,
        "fee_type": feeType,
        "plans": plans == null ? [] : List<dynamic>.from(plans!.map((x) => x.toJson())),
        "required_fields": requiredFields == null ? [] : List<dynamic>.from(requiredFields!.map((x) => x)),
        "validation_regex": validationRegex,
        "validation_message": validationMessage,
      };
}

class BillPlan {
  int? id;
  int? providerId;
  String? name;
  String? code;
  String? description;
  String? amount;
  String? validity;
  String? dataAllowance;
  String? smsAllowance;
  String? callAllowance;
  bool? isActive;

  BillPlan({
    this.id,
    this.providerId,
    this.name,
    this.code,
    this.description,
    this.amount,
    this.validity,
    this.dataAllowance,
    this.smsAllowance,
    this.callAllowance,
    this.isActive,
  });

  factory BillPlan.fromJson(Map<String, dynamic> json) => BillPlan(
        id: json["id"],
        providerId: json["provider_id"],
        name: json["name"],
        code: json["code"],
        description: json["description"],
        amount: json["amount"],
        validity: json["validity"],
        dataAllowance: json["data_allowance"],
        smsAllowance: json["sms_allowance"],
        callAllowance: json["call_allowance"],
        isActive: json["is_active"] == 1,
      );

  Map<String, dynamic> toJson() => {
        "id": id,
        "provider_id": providerId,
        "name": name,
        "code": code,
        "description": description,
        "amount": amount,
        "validity": validity,
        "data_allowance": dataAllowance,
        "sms_allowance": smsAllowance,
        "call_allowance": callAllowance,
        "is_active": isActive == true ? 1 : 0,
      };
}

class BillTransaction {
  int? id;
  int? userId;
  String? transactionId;
  String? reference;
  int? categoryId;
  int? providerId;
  int? planId;
  String? categoryName;
  String? providerName;
  String? planName;
  String? beneficiary;
  String? amount;
  String? fee;
  String? totalAmount;
  String? status; // 'pending', 'processing', 'successful', 'failed', 'cancelled'
  String? paymentMethod;
  String? providerReference;
  String? providerResponse;
  String? failureReason;
  String? createdAt;
  String? updatedAt;

  BillTransaction({
    this.id,
    this.userId,
    this.transactionId,
    this.reference,
    this.categoryId,
    this.providerId,
    this.planId,
    this.categoryName,
    this.providerName,
    this.planName,
    this.beneficiary,
    this.amount,
    this.fee,
    this.totalAmount,
    this.status,
    this.paymentMethod,
    this.providerReference,
    this.providerResponse,
    this.failureReason,
    this.createdAt,
    this.updatedAt,
  });

  factory BillTransaction.fromJson(Map<String, dynamic> json) => BillTransaction(
        id: json["id"],
        userId: json["user_id"],
        transactionId: json["transaction_id"],
        reference: json["reference"],
        categoryId: json["category_id"],
        providerId: json["provider_id"],
        planId: json["plan_id"],
        categoryName: json["category_name"],
        providerName: json["provider_name"],
        planName: json["plan_name"],
        beneficiary: json["beneficiary"],
        amount: json["amount"],
        fee: json["fee"],
        totalAmount: json["total_amount"],
        status: json["status"],
        paymentMethod: json["payment_method"],
        providerReference: json["provider_reference"],
        providerResponse: json["provider_response"],
        failureReason: json["failure_reason"],
        createdAt: json["created_at"],
        updatedAt: json["updated_at"],
      );

  Map<String, dynamic> toJson() => {
        "id": id,
        "user_id": userId,
        "transaction_id": transactionId,
        "reference": reference,
        "category_id": categoryId,
        "provider_id": providerId,
        "plan_id": planId,
        "category_name": categoryName,
        "provider_name": providerName,
        "plan_name": planName,
        "beneficiary": beneficiary,
        "amount": amount,
        "fee": fee,
        "total_amount": totalAmount,
        "status": status,
        "payment_method": paymentMethod,
        "provider_reference": providerReference,
        "provider_response": providerResponse,
        "failure_reason": failureReason,
        "created_at": createdAt,
        "updated_at": updatedAt,
      };
}

class Beneficiary {
  int? id;
  int? userId;
  String? name;
  String? identifier; // phone number, meter number, etc.
  String? categoryCode;
  String? providerCode;
  String? categoryName;
  String? providerName;
  bool? isDefault;
  String? createdAt;

  Beneficiary({
    this.id,
    this.userId,
    this.name,
    this.identifier,
    this.categoryCode,
    this.providerCode,
    this.categoryName,
    this.providerName,
    this.isDefault,
    this.createdAt,
  });

  factory Beneficiary.fromJson(Map<String, dynamic> json) => Beneficiary(
        id: json["id"],
        userId: json["user_id"],
        name: json["name"],
        identifier: json["identifier"],
        categoryCode: json["category_code"],
        providerCode: json["provider_code"],
        categoryName: json["category_name"],
        providerName: json["provider_name"],
        isDefault: json["is_default"] == 1,
        createdAt: json["created_at"],
      );

  Map<String, dynamic> toJson() => {
        "id": id,
        "user_id": userId,
        "name": name,
        "identifier": identifier,
        "category_code": categoryCode,
        "provider_code": providerCode,
        "category_name": categoryName,
        "provider_name": providerName,
        "is_default": isDefault == true ? 1 : 0,
        "created_at": createdAt,
      };
}

class BillPaymentStats {
  String? totalSpent;
  int? totalTransactions;
  String? thisMonth;
  String? lastMonth;
  List<CategorySpending>? categoryBreakdown;
  List<MonthlySpending>? monthlyBreakdown;

  BillPaymentStats({
    this.totalSpent,
    this.totalTransactions,
    this.thisMonth,
    this.lastMonth,
    this.categoryBreakdown,
    this.monthlyBreakdown,
  });

  factory BillPaymentStats.fromJson(Map<String, dynamic> json) => BillPaymentStats(
        totalSpent: json["total_spent"],
        totalTransactions: json["total_transactions"],
        thisMonth: json["this_month"],
        lastMonth: json["last_month"],
        categoryBreakdown: json["category_breakdown"] == null ? [] : List<CategorySpending>.from(json["category_breakdown"]!.map((x) => CategorySpending.fromJson(x))),
        monthlyBreakdown: json["monthly_breakdown"] == null ? [] : List<MonthlySpending>.from(json["monthly_breakdown"]!.map((x) => MonthlySpending.fromJson(x))),
      );

  Map<String, dynamic> toJson() => {
        "total_spent": totalSpent,
        "total_transactions": totalTransactions,
        "this_month": thisMonth,
        "last_month": lastMonth,
        "category_breakdown": categoryBreakdown == null ? [] : List<dynamic>.from(categoryBreakdown!.map((x) => x.toJson())),
        "monthly_breakdown": monthlyBreakdown == null ? [] : List<dynamic>.from(monthlyBreakdown!.map((x) => x.toJson())),
      };
}

class CategorySpending {
  String? categoryName;
  String? amount;
  int? count;
  String? percentage;

  CategorySpending({
    this.categoryName,
    this.amount,
    this.count,
    this.percentage,
  });

  factory CategorySpending.fromJson(Map<String, dynamic> json) => CategorySpending(
        categoryName: json["category_name"],
        amount: json["amount"],
        count: json["count"],
        percentage: json["percentage"],
      );

  Map<String, dynamic> toJson() => {
        "category_name": categoryName,
        "amount": amount,
        "count": count,
        "percentage": percentage,
      };
}

class MonthlySpending {
  String? month;
  String? amount;
  int? count;

  MonthlySpending({
    this.month,
    this.amount,
    this.count,
  });

  factory MonthlySpending.fromJson(Map<String, dynamic> json) => MonthlySpending(
        month: json["month"],
        amount: json["amount"],
        count: json["count"],
      );

  Map<String, dynamic> toJson() => {
        "month": month,
        "amount": amount,
        "count": count,
      };
}

// Request models
class BillPaymentRequest {
  int categoryId;
  int providerId;
  int? planId;
  String beneficiary;
  String amount;
  String? customerName;
  String? meterType;
  String? pin;

  BillPaymentRequest({
    required this.categoryId,
    required this.providerId,
    this.planId,
    required this.beneficiary,
    required this.amount,
    this.customerName,
    this.meterType,
    this.pin,
  });

  Map<String, dynamic> toJson() => {
        "category_id": categoryId,
        "provider_id": providerId,
        if (planId != null) "plan_id": planId,
        "beneficiary": beneficiary,
        "amount": amount,
        if (customerName != null) "customer_name": customerName,
        if (meterType != null) "meter_type": meterType,
        if (pin != null) "pin": pin,
      };
}

class ValidateBillRequest {
  int categoryId;
  int providerId;
  String beneficiary;
  String? meterType;

  ValidateBillRequest({
    required this.categoryId,
    required this.providerId,
    required this.beneficiary,
    this.meterType,
  });

  Map<String, dynamic> toJson() => {
        "category_id": categoryId,
        "provider_id": providerId,
        "beneficiary": beneficiary,
        if (meterType != null) "meter_type": meterType,
      };
}

class AddBeneficiaryRequest {
  String name;
  String identifier;
  String categoryCode;
  String providerCode;

  AddBeneficiaryRequest({
    required this.name,
    required this.identifier,
    required this.categoryCode,
    required this.providerCode,
  });

  Map<String, dynamic> toJson() => {
        "name": name,
        "identifier": identifier,
        "category_code": categoryCode,
        "provider_code": providerCode,
      };
}
