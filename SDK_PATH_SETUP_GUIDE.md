# Android SDK Path Configuration Guide

## 🔧 **How to Find and Set Your Android SDK Path**

The `local.properties` file needs to point to your specific Android SDK installation. Here's how to find and set the correct path for your system.

---

## 📍 **Method 1: Find SDK Path Through Android Studio**

### **Step 1: Open Android Studio**
1. Launch Android Studio
2. Go to **File** → **Settings** (Windows/Linux) or **Android Studio** → **Preferences** (macOS)
3. Navigate to **Appearance & Behavior** → **System Settings** → **Android SDK**
4. Look at the **Android SDK Location** field
5. Copy this path

### **Step 2: Update local.properties**
Replace the SDK path in `android/local.properties` with your copied path:

**Windows Example:**
```properties
sdk.dir=C:\\Users\\<USER>\\AppData\\Local\\Android\\sdk
flutter.sdk=D:\\flutter_sdk\\flutter
flutter.buildMode=release
flutter.versionName=1.0.0
flutter.versionCode=1
```

**macOS Example:**
```properties
sdk.dir=/Users/<USER>/Library/Android/sdk
flutter.sdk=/Users/<USER>/flutter
flutter.buildMode=release
flutter.versionName=1.0.0
flutter.versionCode=1
```

**Linux Example:**
```properties
sdk.dir=/home/<USER>/Android/Sdk
flutter.sdk=/home/<USER>/flutter
flutter.buildMode=release
flutter.versionName=1.0.0
flutter.versionCode=1
```

---

## 📍 **Method 2: Find SDK Path Using Command Line**

### **Windows:**
```cmd
# Check environment variables
echo %ANDROID_HOME%
echo %ANDROID_SDK_ROOT%

# Or check default location
dir "C:\Users\<USER>\AppData\Local\Android\sdk"
```

### **macOS/Linux:**
```bash
# Check environment variables
echo $ANDROID_HOME
echo $ANDROID_SDK_ROOT

# Or check default locations
ls ~/Library/Android/sdk  # macOS
ls ~/Android/Sdk          # Linux
```

---

## 📍 **Method 3: Common Default Locations**

### **Windows:**
- `C:\Users\<USER>\AppData\Local\Android\sdk`
- `C:\Android\sdk`
- `C:\Program Files\Android\sdk`

### **macOS:**
- `/Users/<USER>/Library/Android/sdk`
- `/Applications/Android Studio.app/Contents/sdk`

### **Linux:**
- `/home/<USER>/Android/Sdk`
- `/opt/android-sdk`
- `/usr/local/android-sdk`

---

## 🛠️ **Method 4: Auto-Generate local.properties**

### **Delete and Regenerate:**
1. Delete the existing `android/local.properties` file
2. Run the following command in your project root:
```bash
flutter clean
flutter pub get
```
3. Flutter will automatically generate a new `local.properties` file with the correct paths

---

## 🔍 **Method 5: Manual Configuration**

### **Step 1: Find Your Username**
```cmd
# Windows
echo %USERNAME%

# macOS/Linux
whoami
```

### **Step 2: Replace in local.properties**
Update `android/local.properties` with your actual username:

**Before:**
```properties
sdk.dir=C:\\Users\\<USER>\\AppData\\Local\\Android\\sdk
```

**After (replace 'YourActualUsername' with your username):**
```properties
sdk.dir=C:\\Users\\<USER>\\AppData\\Local\\Android\\sdk
```

---

## ⚡ **Quick Fix Commands**

### **Option 1: Use Flutter Doctor**
```bash
flutter doctor -v
```
This will show you the detected SDK paths and any issues.

### **Option 2: Set Environment Variables**
```bash
# Windows (Command Prompt)
set ANDROID_HOME=C:\Users\<USER>\AppData\Local\Android\sdk
set ANDROID_SDK_ROOT=%ANDROID_HOME%

# Windows (PowerShell)
$env:ANDROID_HOME="C:\Users\<USER>\AppData\Local\Android\sdk"
$env:ANDROID_SDK_ROOT=$env:ANDROID_HOME

# macOS/Linux (Bash)
export ANDROID_HOME=~/Library/Android/sdk  # macOS
export ANDROID_HOME=~/Android/Sdk          # Linux
export ANDROID_SDK_ROOT=$ANDROID_HOME
```

### **Option 3: Create New local.properties**
Create a new `android/local.properties` file with the correct paths:

```properties
# Replace these paths with your actual paths
sdk.dir=C:\\Users\\<USER>\\AppData\\Local\\Android\\sdk
flutter.sdk=C:\\flutter
flutter.buildMode=debug
flutter.versionName=1.0.0
flutter.versionCode=1
```

---

## 🚨 **Troubleshooting Common Issues**

### **Issue 1: SDK Not Found**
```
Error: Android SDK not found
```

**Solution:**
1. Install Android Studio if not already installed
2. Open Android Studio and let it download the SDK
3. Update the path in `local.properties`

### **Issue 2: Permission Denied**
```
Error: Permission denied accessing SDK
```

**Solution:**
```bash
# Windows (Run as Administrator)
icacls "C:\Users\<USER>\AppData\Local\Android\sdk" /grant %USERNAME%:F /T

# macOS/Linux
sudo chmod -R 755 ~/Library/Android/sdk  # macOS
sudo chmod -R 755 ~/Android/Sdk          # Linux
```

### **Issue 3: Path with Spaces**
If your path contains spaces, ensure proper escaping:

**Windows:**
```properties
sdk.dir=C:\\Program Files\\Android\\sdk
```

**macOS/Linux:**
```properties
sdk.dir=/Applications/Android Studio.app/Contents/sdk
```

---

## ✅ **Verification Steps**

### **Step 1: Check Flutter Doctor**
```bash
flutter doctor
```

### **Step 2: Test Build**
```bash
flutter clean
flutter pub get
flutter build apk --debug
```

### **Step 3: Run App**
```bash
flutter run
```

---

## 📝 **Example local.properties Files**

### **Windows Developer:**
```properties
sdk.dir=C:\\Users\\<USER>\\AppData\\Local\\Android\\sdk
flutter.sdk=C:\\flutter
flutter.buildMode=debug
flutter.versionName=1.0.0
flutter.versionCode=1
```

### **macOS Developer:**
```properties
sdk.dir=/Users/<USER>/Library/Android/sdk
flutter.sdk=/Users/<USER>/flutter
flutter.buildMode=debug
flutter.versionName=1.0.0
flutter.versionCode=1
```

### **Linux Developer:**
```properties
sdk.dir=/home/<USER>/Android/Sdk
flutter.sdk=/home/<USER>/flutter
flutter.buildMode=debug
flutter.versionName=1.0.0
flutter.versionCode=1
```

---

## 🎯 **Quick Action Items**

1. **Find your Android SDK path** using Method 1 (Android Studio)
2. **Update android/local.properties** with your actual path
3. **Replace YourUsername** with your actual system username
4. **Run flutter doctor** to verify everything is working
5. **Test with flutter run** to ensure the app builds correctly

**Note:** Make sure to use double backslashes (`\\`) on Windows and forward slashes (`/`) on macOS/Linux in the path.
