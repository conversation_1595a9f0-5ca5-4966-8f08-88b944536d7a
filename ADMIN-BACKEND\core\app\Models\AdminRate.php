<?php

namespace App\Models;

use App\Traits\Searchable;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Cache;

class AdminRate extends Model
{
    use Searchable;

    protected $fillable = [
        'category',
        'type',
        'name',
        'rate',
        'unit',
        'description',
        'is_percentage',
        'is_active',
        'min_value',
        'max_value'
    ];

    protected $casts = [
        'rate' => 'decimal:4',
        'is_percentage' => 'boolean',
        'is_active' => 'boolean',
        'min_value' => 'decimal:4',
        'max_value' => 'decimal:4'
    ];

    protected $appends = [
        'formatted_rate',
        'display_name',
        'cache_key'
    ];

    // Accessors
    public function getFormattedRateAttribute(): string
    {
        if ($this->is_percentage) {
            return $this->rate . '%';
        }
        
        if ($this->unit === 'naira') {
            return '₦' . number_format($this->rate, 2);
        }
        
        return $this->rate . ' ' . $this->unit;
    }

    public function getDisplayNameAttribute(): string
    {
        return ucwords(str_replace('_', ' ', $this->name));
    }

    public function getCacheKeyAttribute(): string
    {
        return "admin_rate_{$this->category}_{$this->type}_{$this->name}";
    }

    // Scopes
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    public function scopeByCategory($query, $category)
    {
        return $query->where('category', $category);
    }

    public function scopeByType($query, $type)
    {
        return $query->where('type', $type);
    }

    public function scopeByName($query, $name)
    {
        return $query->where('name', $name);
    }

    public function scopePercentage($query)
    {
        return $query->where('is_percentage', true);
    }

    public function scopeFixed($query)
    {
        return $query->where('is_percentage', false);
    }

    // Methods
    public function updateRate(float $newRate): array
    {
        if ($this->min_value && $newRate < $this->min_value) {
            return [
                'success' => false,
                'message' => "Rate cannot be less than {$this->min_value}"
            ];
        }

        if ($this->max_value && $newRate > $this->max_value) {
            return [
                'success' => false,
                'message' => "Rate cannot be greater than {$this->max_value}"
            ];
        }

        $oldRate = $this->rate;
        $this->rate = $newRate;
        $this->save();

        // Clear cache
        Cache::forget($this->cache_key);

        // Log the change
        activity()
            ->performedOn($this)
            ->withProperties([
                'old_rate' => $oldRate,
                'new_rate' => $newRate
            ])
            ->log('Rate updated');

        return [
            'success' => true,
            'message' => 'Rate updated successfully',
            'old_rate' => $oldRate,
            'new_rate' => $newRate
        ];
    }

    // Static Methods
    public static function getRate(string $category, string $type, string $name): ?float
    {
        $cacheKey = "admin_rate_{$category}_{$type}_{$name}";
        
        return Cache::remember($cacheKey, 3600, function() use ($category, $type, $name) {
            $rate = self::where('category', $category)
                       ->where('type', $type)
                       ->where('name', $name)
                       ->where('is_active', true)
                       ->first();
            
            return $rate ? $rate->rate : null;
        });
    }

    public static function getSavingsRates(): array
    {
        return [
            'fixed_deposit_30_days' => self::getRate('savings', 'fixed_deposit', '30_days') ?? 15.0,
            'fixed_deposit_60_days' => self::getRate('savings', 'fixed_deposit', '60_days') ?? 16.0,
            'fixed_deposit_90_days' => self::getRate('savings', 'fixed_deposit', '90_days') ?? 17.0,
            'fixed_deposit_180_days' => self::getRate('savings', 'fixed_deposit', '180_days') ?? 19.0,
            'fixed_deposit_365_days' => self::getRate('savings', 'fixed_deposit', '365_days') ?? 22.0,
            'target_savings' => self::getRate('savings', 'target_saving', 'interest_rate') ?? 12.0,
            'flex_savings' => self::getRate('savings', 'flex_saving', 'interest_rate') ?? 10.0,
            'group_savings' => self::getRate('savings', 'group_saving', 'interest_rate') ?? 5.0,
        ];
    }

    public static function getInvestmentRates(): array
    {
        return [
            'treasury_bills_min' => self::getRate('investment', 'treasury_bills', 'min_rate') ?? 10.0,
            'treasury_bills_max' => self::getRate('investment', 'treasury_bills', 'max_rate') ?? 15.0,
            'mutual_funds_min' => self::getRate('investment', 'mutual_funds', 'min_rate') ?? 15.0,
            'mutual_funds_max' => self::getRate('investment', 'mutual_funds', 'max_rate') ?? 25.0,
            'corporate_bonds_min' => self::getRate('investment', 'corporate_bonds', 'min_rate') ?? 12.0,
            'corporate_bonds_max' => self::getRate('investment', 'corporate_bonds', 'max_rate') ?? 18.0,
            'stocks_min' => self::getRate('investment', 'stocks', 'min_rate') ?? 20.0,
            'stocks_max' => self::getRate('investment', 'stocks', 'max_rate') ?? 40.0,
            'real_estate_min' => self::getRate('investment', 'real_estate', 'min_rate') ?? 18.0,
            'real_estate_max' => self::getRate('investment', 'real_estate', 'max_rate') ?? 30.0,
        ];
    }

    public static function getLoanRates(): array
    {
        return [
            'personal_loan' => self::getRate('loan', 'personal', 'interest_rate') ?? 18.0,
            'business_loan' => self::getRate('loan', 'business', 'interest_rate') ?? 20.0,
            'emergency_loan' => self::getRate('loan', 'emergency', 'interest_rate') ?? 25.0,
            'processing_fee_personal' => self::getRate('loan', 'personal', 'processing_fee') ?? 2.0,
            'processing_fee_business' => self::getRate('loan', 'business', 'processing_fee') ?? 3.0,
            'processing_fee_emergency' => self::getRate('loan', 'emergency', 'processing_fee') ?? 1.0,
            'late_payment_penalty' => self::getRate('loan', 'general', 'late_payment_penalty') ?? 5.0,
        ];
    }

    public static function getTransactionFees(): array
    {
        return [
            'transfer_fee_local' => self::getRate('transaction', 'transfer', 'local_fee') ?? 10.0,
            'transfer_fee_other_banks' => self::getRate('transaction', 'transfer', 'other_banks_fee') ?? 25.0,
            'withdrawal_fee_atm' => self::getRate('transaction', 'withdrawal', 'atm_fee') ?? 35.0,
            'withdrawal_fee_agent' => self::getRate('transaction', 'withdrawal', 'agent_fee') ?? 100.0,
            'bill_payment_fee' => self::getRate('transaction', 'bill_payment', 'fee') ?? 0.0,
            'airtime_fee' => self::getRate('transaction', 'airtime', 'fee') ?? 0.0,
            'data_fee' => self::getRate('transaction', 'data', 'fee') ?? 0.0,
        ];
    }

    public static function getCardFees(): array
    {
        return [
            'virtual_card_fee' => self::getRate('card', 'virtual', 'issuance_fee') ?? 0.0,
            'physical_card_fee' => self::getRate('card', 'physical', 'issuance_fee') ?? 1000.0,
            'card_maintenance_fee' => self::getRate('card', 'general', 'maintenance_fee') ?? 50.0,
            'card_replacement_fee' => self::getRate('card', 'general', 'replacement_fee') ?? 500.0,
            'international_transaction_fee' => self::getRate('card', 'general', 'international_fee') ?? 3.5,
        ];
    }

    public static function seedDefaultRates(): void
    {
        $defaultRates = [
            // Savings Rates
            ['category' => 'savings', 'type' => 'fixed_deposit', 'name' => '30_days', 'rate' => 15.0, 'description' => '30-day fixed deposit rate'],
            ['category' => 'savings', 'type' => 'fixed_deposit', 'name' => '60_days', 'rate' => 16.0, 'description' => '60-day fixed deposit rate'],
            ['category' => 'savings', 'type' => 'fixed_deposit', 'name' => '90_days', 'rate' => 17.0, 'description' => '90-day fixed deposit rate'],
            ['category' => 'savings', 'type' => 'fixed_deposit', 'name' => '180_days', 'rate' => 19.0, 'description' => '180-day fixed deposit rate'],
            ['category' => 'savings', 'type' => 'fixed_deposit', 'name' => '365_days', 'rate' => 22.0, 'description' => '365-day fixed deposit rate'],
            ['category' => 'savings', 'type' => 'target_saving', 'name' => 'interest_rate', 'rate' => 12.0, 'description' => 'Target savings interest rate'],
            ['category' => 'savings', 'type' => 'flex_saving', 'name' => 'interest_rate', 'rate' => 10.0, 'description' => 'Flex savings interest rate'],
            ['category' => 'savings', 'type' => 'group_saving', 'name' => 'interest_rate', 'rate' => 5.0, 'description' => 'Group savings interest rate'],

            // Investment Rates
            ['category' => 'investment', 'type' => 'treasury_bills', 'name' => 'min_rate', 'rate' => 10.0, 'description' => 'Treasury bills minimum rate'],
            ['category' => 'investment', 'type' => 'treasury_bills', 'name' => 'max_rate', 'rate' => 15.0, 'description' => 'Treasury bills maximum rate'],
            ['category' => 'investment', 'type' => 'mutual_funds', 'name' => 'min_rate', 'rate' => 15.0, 'description' => 'Mutual funds minimum rate'],
            ['category' => 'investment', 'type' => 'mutual_funds', 'name' => 'max_rate', 'rate' => 25.0, 'description' => 'Mutual funds maximum rate'],
            ['category' => 'investment', 'type' => 'corporate_bonds', 'name' => 'min_rate', 'rate' => 12.0, 'description' => 'Corporate bonds minimum rate'],
            ['category' => 'investment', 'type' => 'corporate_bonds', 'name' => 'max_rate', 'rate' => 18.0, 'description' => 'Corporate bonds maximum rate'],
            ['category' => 'investment', 'type' => 'stocks', 'name' => 'min_rate', 'rate' => 20.0, 'description' => 'Stocks minimum expected return'],
            ['category' => 'investment', 'type' => 'stocks', 'name' => 'max_rate', 'rate' => 40.0, 'description' => 'Stocks maximum expected return'],
            ['category' => 'investment', 'type' => 'real_estate', 'name' => 'min_rate', 'rate' => 18.0, 'description' => 'Real estate minimum rate'],
            ['category' => 'investment', 'type' => 'real_estate', 'name' => 'max_rate', 'rate' => 30.0, 'description' => 'Real estate maximum rate'],

            // Loan Rates
            ['category' => 'loan', 'type' => 'personal', 'name' => 'interest_rate', 'rate' => 18.0, 'description' => 'Personal loan interest rate'],
            ['category' => 'loan', 'type' => 'business', 'name' => 'interest_rate', 'rate' => 20.0, 'description' => 'Business loan interest rate'],
            ['category' => 'loan', 'type' => 'emergency', 'name' => 'interest_rate', 'rate' => 25.0, 'description' => 'Emergency loan interest rate'],
            ['category' => 'loan', 'type' => 'personal', 'name' => 'processing_fee', 'rate' => 2.0, 'description' => 'Personal loan processing fee'],
            ['category' => 'loan', 'type' => 'business', 'name' => 'processing_fee', 'rate' => 3.0, 'description' => 'Business loan processing fee'],
            ['category' => 'loan', 'type' => 'emergency', 'name' => 'processing_fee', 'rate' => 1.0, 'description' => 'Emergency loan processing fee'],
            ['category' => 'loan', 'type' => 'general', 'name' => 'late_payment_penalty', 'rate' => 5.0, 'description' => 'Late payment penalty rate'],

            // Transaction Fees
            ['category' => 'transaction', 'type' => 'transfer', 'name' => 'local_fee', 'rate' => 10.0, 'unit' => 'naira', 'is_percentage' => false, 'description' => 'Local transfer fee'],
            ['category' => 'transaction', 'type' => 'transfer', 'name' => 'other_banks_fee', 'rate' => 25.0, 'unit' => 'naira', 'is_percentage' => false, 'description' => 'Other banks transfer fee'],
            ['category' => 'transaction', 'type' => 'withdrawal', 'name' => 'atm_fee', 'rate' => 35.0, 'unit' => 'naira', 'is_percentage' => false, 'description' => 'ATM withdrawal fee'],
            ['category' => 'transaction', 'type' => 'withdrawal', 'name' => 'agent_fee', 'rate' => 100.0, 'unit' => 'naira', 'is_percentage' => false, 'description' => 'Agent withdrawal fee'],

            // Card Fees
            ['category' => 'card', 'type' => 'virtual', 'name' => 'issuance_fee', 'rate' => 0.0, 'unit' => 'naira', 'is_percentage' => false, 'description' => 'Virtual card issuance fee'],
            ['category' => 'card', 'type' => 'physical', 'name' => 'issuance_fee', 'rate' => 1000.0, 'unit' => 'naira', 'is_percentage' => false, 'description' => 'Physical card issuance fee'],
            ['category' => 'card', 'type' => 'general', 'name' => 'maintenance_fee', 'rate' => 50.0, 'unit' => 'naira', 'is_percentage' => false, 'description' => 'Monthly card maintenance fee'],
            ['category' => 'card', 'type' => 'general', 'name' => 'international_fee', 'rate' => 3.5, 'description' => 'International transaction fee'],
        ];

        foreach ($defaultRates as $rateData) {
            $rateData['is_percentage'] = $rateData['is_percentage'] ?? true;
            $rateData['unit'] = $rateData['unit'] ?? 'percent';
            $rateData['is_active'] = true;
            $rateData['admin_configurable_rate'] = true;

            self::updateOrCreate(
                [
                    'category' => $rateData['category'],
                    'type' => $rateData['type'],
                    'name' => $rateData['name']
                ],
                $rateData
            );
        }
    }

    public static function clearAllCache(): void
    {
        $rates = self::all();
        foreach ($rates as $rate) {
            Cache::forget($rate->cache_key);
        }
    }
}
