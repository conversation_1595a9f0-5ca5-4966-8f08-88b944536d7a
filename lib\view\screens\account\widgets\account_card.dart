import 'package:flutter/material.dart';
import 'package:viserpay/core/utils/dimensions.dart';
import 'package:viserpay/core/utils/my_color.dart';
import 'package:viserpay/core/utils/my_strings.dart';
import 'package:viserpay/core/utils/style.dart';
import 'package:viserpay/data/model/account/account_model.dart';

class AccountCard extends StatelessWidget {
  final Account account;
  final VoidCallback onTap;
  final VoidCallback onSetDefault;
  final String currencySymbol;

  const AccountCard({
    super.key,
    required this.account,
    required this.onTap,
    required this.onSetDefault,
    required this.currencySymbol,
  });

  @override
  Widget build(BuildContext context) {
    final accountTypeColor = _getAccountTypeColor(account.accountType?.code);
    final accountTypeIcon = _getAccountTypeIcon(account.accountType?.code);

    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.all(Dimensions.space20),
        decoration: BoxDecoration(
          color: MyColor.colorWhite,
          borderRadius: BorderRadius.circular(Dimensions.mediumRadius),
          border: account.isDefault == true
              ? Border.all(color: MyColor.primaryColor, width: 2)
              : null,
          boxShadow: [
            BoxShadow(
              color: MyColor.colorGrey.withOpacity(0.1),
              blurRadius: 10,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header Row
            Row(
              children: [
                // Account Type Icon
                Container(
                  padding: const EdgeInsets.all(Dimensions.space10),
                  decoration: BoxDecoration(
                    color: accountTypeColor.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(Dimensions.smallRadius),
                  ),
                  child: Icon(
                    accountTypeIcon,
                    color: accountTypeColor,
                    size: 24,
                  ),
                ),
                const SizedBox(width: Dimensions.space15),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        children: [
                          Expanded(
                            child: Text(
                              account.accountName ?? 'Account',
                              style: semiBoldLarge.copyWith(
                                color: MyColor.colorBlack,
                              ),
                              maxLines: 1,
                              overflow: TextOverflow.ellipsis,
                            ),
                          ),
                          if (account.isDefault == true)
                            Container(
                              padding: const EdgeInsets.symmetric(
                                horizontal: Dimensions.space8,
                                vertical: Dimensions.space4,
                              ),
                              decoration: BoxDecoration(
                                color: MyColor.primaryColor,
                                borderRadius: BorderRadius.circular(Dimensions.smallRadius),
                              ),
                              child: Text(
                                'Default',
                                style: regularSmall.copyWith(
                                  color: MyColor.colorWhite,
                                  fontSize: 10,
                                ),
                              ),
                            ),
                        ],
                      ),
                      const SizedBox(height: Dimensions.space5),
                      Text(
                        account.accountType?.name ?? 'Account Type',
                        style: regularDefault.copyWith(
                          color: MyColor.colorGrey,
                        ),
                      ),
                    ],
                  ),
                ),
                // More Options
                PopupMenuButton<String>(
                  icon: const Icon(
                    Icons.more_vert,
                    color: MyColor.colorGrey,
                  ),
                  onSelected: (value) {
                    switch (value) {
                      case 'set_default':
                        if (account.isDefault != true) {
                          onSetDefault();
                        }
                        break;
                      case 'view_details':
                        onTap();
                        break;
                    }
                  },
                  itemBuilder: (context) => [
                    if (account.isDefault != true)
                      PopupMenuItem<String>(
                        value: 'set_default',
                        child: Row(
                          children: [
                            const Icon(Icons.star_outline, size: 18),
                            const SizedBox(width: Dimensions.space8),
                            Text(MyStrings.setAsDefault),
                          ],
                        ),
                      ),
                    PopupMenuItem<String>(
                      value: 'view_details',
                      child: Row(
                        children: [
                          const Icon(Icons.visibility_outlined, size: 18),
                          const SizedBox(width: Dimensions.space8),
                          Text(MyStrings.accountDetails),
                        ],
                      ),
                    ),
                  ],
                ),
              ],
            ),
            
            const SizedBox(height: Dimensions.space15),
            
            // Account Number
            Row(
              children: [
                Text(
                  MyStrings.accountNumber,
                  style: regularDefault.copyWith(
                    color: MyColor.colorGrey,
                  ),
                ),
                const SizedBox(width: Dimensions.space10),
                Expanded(
                  child: Text(
                    account.accountNumber ?? 'N/A',
                    style: semiBoldDefault.copyWith(
                      color: MyColor.colorBlack,
                    ),
                  ),
                ),
              ],
            ),
            
            const SizedBox(height: Dimensions.space10),
            
            // Balance
            Row(
              children: [
                Text(
                  MyStrings.accountBalance,
                  style: regularDefault.copyWith(
                    color: MyColor.colorGrey,
                  ),
                ),
                const Spacer(),
                Text(
                  '$currencySymbol${_formatBalance(account.balance)}',
                  style: boldLarge.copyWith(
                    color: MyColor.primaryColor,
                  ),
                ),
              ],
            ),
            
            // Interest Rate (if applicable)
            if (account.interestRate != null && account.interestRate != '0')
              Padding(
                padding: const EdgeInsets.only(top: Dimensions.space10),
                child: Row(
                  children: [
                    Text(
                      MyStrings.interestRate,
                      style: regularDefault.copyWith(
                        color: MyColor.colorGrey,
                      ),
                    ),
                    const Spacer(),
                    Text(
                      '${account.interestRate}% p.a.',
                      style: semiBoldDefault.copyWith(
                        color: MyColor.colorGreen,
                      ),
                    ),
                  ],
                ),
              ),
            
            const SizedBox(height: Dimensions.space15),
            
            // Status Indicator
            Row(
              children: [
                Container(
                  width: 8,
                  height: 8,
                  decoration: BoxDecoration(
                    color: _getStatusColor(account.status),
                    shape: BoxShape.circle,
                  ),
                ),
                const SizedBox(width: Dimensions.space8),
                Text(
                  _getStatusText(account.status),
                  style: regularSmall.copyWith(
                    color: _getStatusColor(account.status),
                  ),
                ),
                const Spacer(),
                Text(
                  'Tap to view details',
                  style: regularSmall.copyWith(
                    color: MyColor.colorGrey,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Color _getAccountTypeColor(String? code) {
    switch (code?.toUpperCase()) {
      case 'SAVINGS':
        return MyColor.colorGreen;
      case 'CURRENT':
        return MyColor.primaryColor;
      case 'FIXED_DEPOSIT':
        return MyColor.colorOrange;
      case 'BUSINESS':
        return MyColor.colorPurple;
      case 'STUDENT':
        return MyColor.colorTeal;
      default:
        return MyColor.colorGrey;
    }
  }

  IconData _getAccountTypeIcon(String? code) {
    switch (code?.toUpperCase()) {
      case 'SAVINGS':
        return Icons.savings;
      case 'CURRENT':
        return Icons.account_balance_wallet;
      case 'FIXED_DEPOSIT':
        return Icons.lock_clock;
      case 'BUSINESS':
        return Icons.business;
      case 'STUDENT':
        return Icons.school;
      default:
        return Icons.account_balance;
    }
  }

  Color _getStatusColor(String? status) {
    switch (status?.toLowerCase()) {
      case 'active':
        return MyColor.colorGreen;
      case 'frozen':
        return MyColor.colorOrange;
      case 'closed':
        return MyColor.colorRed;
      default:
        return MyColor.colorGrey;
    }
  }

  String _getStatusText(String? status) {
    switch (status?.toLowerCase()) {
      case 'active':
        return 'Active';
      case 'frozen':
        return 'Frozen';
      case 'closed':
        return 'Closed';
      default:
        return 'Unknown';
    }
  }

  String _formatBalance(String? balance) {
    if (balance == null || balance.isEmpty) return '0.00';
    try {
      double amount = double.parse(balance);
      return amount.toStringAsFixed(2);
    } catch (e) {
      return balance;
    }
  }
}
