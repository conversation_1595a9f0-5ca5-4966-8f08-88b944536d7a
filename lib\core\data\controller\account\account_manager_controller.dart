import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:viserpay/core/utils/app_theme.dart';
import 'package:viserpay/data/controller/theme/theme_controller.dart';

enum AccountType { personal, business, kids }

class AccountInfo {
  final String id;
  final String name;
  final AccountType type;
  final String? parentAccountId; // For kids accounts
  final bool isActive;
  final DateTime createdAt;

  AccountInfo({
    required this.id,
    required this.name,
    required this.type,
    this.parentAccountId,
    this.isActive = true,
    required this.createdAt,
  });

  Map<String, dynamic> toJson() => {
    'id': id,
    'name': name,
    'type': type.toString(),
    'parentAccountId': parentAccountId,
    'isActive': isActive,
    'createdAt': createdAt.toIso8601String(),
  };

  factory AccountInfo.fromJson(Map<String, dynamic> json) => AccountInfo(
    id: json['id'],
    name: json['name'],
    type: AccountType.values.firstWhere(
      (e) => e.toString() == json['type'],
      orElse: () => AccountType.personal,
    ),
    parentAccountId: json['parentAccountId'],
    isActive: json['isActive'] ?? true,
    createdAt: DateTime.parse(json['createdAt']),
  );
}

class AccountManagerController extends GetxController {
  static const String _currentAccountKey = 'current_account';
  static const String _accountsKey = 'user_accounts';
  
  final SharedPreferences _prefs = Get.find();
  final ThemeController _themeController = Get.find();
  
  // Observable properties
  final Rx<AccountInfo?> currentAccount = Rx<AccountInfo?>(null);
  final RxList<AccountInfo> userAccounts = <AccountInfo>[].obs;
  final RxBool isLoading = false.obs;

  @override
  void onInit() {
    super.onInit();
    _loadAccounts();
  }

  /// Load accounts from storage
  void _loadAccounts() {
    try {
      // Load current account
      final currentAccountJson = _prefs.getString(_currentAccountKey);
      if (currentAccountJson != null) {
        final accountData = Map<String, dynamic>.from(
          // Note: In real implementation, use proper JSON parsing
          {} // Placeholder for JSON.decode(currentAccountJson)
        );
        currentAccount.value = AccountInfo.fromJson(accountData);
      }

      // Load all accounts
      final accountsJson = _prefs.getStringList(_accountsKey) ?? [];
      userAccounts.value = accountsJson.map((json) {
        final accountData = Map<String, dynamic>.from(
          {} // Placeholder for JSON.decode(json)
        );
        return AccountInfo.fromJson(accountData);
      }).toList();

      // If no current account but accounts exist, set first as current
      if (currentAccount.value == null && userAccounts.isNotEmpty) {
        switchAccount(userAccounts.first);
      }
    } catch (e) {
      print('Error loading accounts: $e');
      _createDefaultPersonalAccount();
    }
  }

  /// Save accounts to storage
  Future<void> _saveAccounts() async {
    try {
      // Save current account
      if (currentAccount.value != null) {
        // Note: In real implementation, use JSON.encode
        await _prefs.setString(_currentAccountKey, '{}'); // Placeholder
      }

      // Save all accounts
      final accountsJson = userAccounts.map((account) {
        // Note: In real implementation, use JSON.encode
        return '{}'; // Placeholder
      }).toList();
      await _prefs.setStringList(_accountsKey, accountsJson);
    } catch (e) {
      print('Error saving accounts: $e');
    }
  }

  /// Create default personal account
  void _createDefaultPersonalAccount() {
    final defaultAccount = AccountInfo(
      id: 'personal_${DateTime.now().millisecondsSinceEpoch}',
      name: 'Personal Account',
      type: AccountType.personal,
      createdAt: DateTime.now(),
    );
    
    userAccounts.add(defaultAccount);
    currentAccount.value = defaultAccount;
    _saveAccounts();
  }

  /// Switch to a different account
  Future<void> switchAccount(AccountInfo account) async {
    if (account.id == currentAccount.value?.id) return;

    isLoading.value = true;
    
    try {
      // Update current account
      currentAccount.value = account;
      
      // Switch theme based on account type
      AppThemeType themeType;
      switch (account.type) {
        case AccountType.personal:
          themeType = AppThemeType.personal;
          break;
        case AccountType.business:
          themeType = AppThemeType.business;
          break;
        case AccountType.kids:
          themeType = AppThemeType.kids;
          break;
      }
      
      await _themeController.changeTheme(themeType);
      await _saveAccounts();
      
      Get.snackbar(
        'Account Switched',
        'Now using ${account.name}',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Get.theme.primaryColor.withOpacity(0.1),
        colorText: Get.theme.primaryColor,
      );
    } catch (e) {
      print('Error switching account: $e');
      Get.snackbar(
        'Error',
        'Failed to switch account',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Get.theme.colorScheme.error.withOpacity(0.1),
        colorText: Get.theme.colorScheme.error,
      );
    } finally {
      isLoading.value = false;
    }
  }

  /// Create a new business account
  Future<bool> createBusinessAccount(String businessName) async {
    try {
      // Check if user already has a business account
      final existingBusiness = userAccounts.firstWhereOrNull(
        (account) => account.type == AccountType.business,
      );
      
      if (existingBusiness != null) {
        Get.snackbar(
          'Business Account Exists',
          'You already have a business account',
          snackPosition: SnackPosition.BOTTOM,
        );
        return false;
      }

      final businessAccount = AccountInfo(
        id: 'business_${DateTime.now().millisecondsSinceEpoch}',
        name: businessName,
        type: AccountType.business,
        createdAt: DateTime.now(),
      );
      
      userAccounts.add(businessAccount);
      await _saveAccounts();
      
      Get.snackbar(
        'Business Account Created',
        'Successfully created $businessName',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Get.theme.primaryColor.withOpacity(0.1),
        colorText: Get.theme.primaryColor,
      );
      
      return true;
    } catch (e) {
      print('Error creating business account: $e');
      return false;
    }
  }

  /// Create a new kids account (only for personal account holders)
  Future<bool> createKidsAccount(String childName, int childAge) async {
    try {
      // Only personal account holders can create kids accounts
      if (currentAccount.value?.type != AccountType.personal) {
        Get.snackbar(
          'Access Denied',
          'Only personal account holders can create kids accounts',
          snackPosition: SnackPosition.BOTTOM,
        );
        return false;
      }

      final kidsAccount = AccountInfo(
        id: 'kids_${DateTime.now().millisecondsSinceEpoch}',
        name: "$childName's Account",
        type: AccountType.kids,
        parentAccountId: currentAccount.value!.id,
        createdAt: DateTime.now(),
      );
      
      userAccounts.add(kidsAccount);
      await _saveAccounts();
      
      Get.snackbar(
        'Kids Account Created',
        'Successfully created account for $childName',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Get.theme.primaryColor.withOpacity(0.1),
        colorText: Get.theme.primaryColor,
      );
      
      return true;
    } catch (e) {
      print('Error creating kids account: $e');
      return false;
    }
  }

  /// Get accounts by type
  List<AccountInfo> getAccountsByType(AccountType type) {
    return userAccounts.where((account) => account.type == type).toList();
  }

  /// Get kids accounts for current user
  List<AccountInfo> getKidsAccounts() {
    if (currentAccount.value?.type != AccountType.personal) return [];
    
    return userAccounts.where((account) => 
      account.type == AccountType.kids && 
      account.parentAccountId == currentAccount.value!.id
    ).toList();
  }

  /// Check if user can create business account
  bool canCreateBusinessAccount() {
    return currentAccount.value?.type == AccountType.personal &&
           !userAccounts.any((account) => account.type == AccountType.business);
  }

  /// Check if user can create kids account
  bool canCreateKidsAccount() {
    return currentAccount.value?.type == AccountType.personal;
  }

  /// Get account type display name
  String getAccountTypeName(AccountType type) {
    switch (type) {
      case AccountType.personal:
        return 'Personal';
      case AccountType.business:
        return 'Business';
      case AccountType.kids:
        return 'Kids';
    }
  }

  /// Get account type icon
  IconData getAccountTypeIcon(AccountType type) {
    switch (type) {
      case AccountType.personal:
        return Icons.person;
      case AccountType.business:
        return Icons.business;
      case AccountType.kids:
        return Icons.child_care;
    }
  }

  /// Get account type color
  Color getAccountTypeColor(AccountType type) {
    switch (type) {
      case AccountType.personal:
        return const Color(0xff1231b8); // Primary blue
      case AccountType.business:
        return const Color(0xff000000); // Black
      case AccountType.kids:
        return const Color(0xffFF6B6B); // Coral
    }
  }
}
