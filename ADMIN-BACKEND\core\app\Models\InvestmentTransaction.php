<?php

namespace App\Models;

use App\Traits\Searchable;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Carbon\Carbon;

class InvestmentTransaction extends Model
{
    use Searchable;

    protected $fillable = [
        'user_id',
        'investment_id',
        'product_id',
        'type',
        'amount',
        'units',
        'unit_price',
        'fees',
        'description',
        'trx'
    ];

    protected $casts = [
        'amount' => 'decimal:8',
        'units' => 'decimal:8',
        'unit_price' => 'decimal:8',
        'fees' => 'decimal:8'
    ];

    protected $appends = [
        'formatted_amount',
        'type_badge_class',
        'net_amount',
        'is_profitable'
    ];

    // Relationships
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    public function investment(): BelongsTo
    {
        return $this->belongsTo(UserInvestment::class, 'investment_id');
    }

    public function product(): BelongsTo
    {
        return $this->belongsTo(InvestmentProduct::class, 'product_id');
    }

    // Accessors
    public function getFormattedAmountAttribute(): string
    {
        return '₦' . number_format($this->amount, 2);
    }

    public function getTypeBadgeClassAttribute(): string
    {
        return match($this->type) {
            'buy' => 'badge-primary',
            'sell' => 'badge-warning',
            'dividend' => 'badge-success',
            'interest' => 'badge-info',
            'fee' => 'badge-danger',
            default => 'badge-secondary'
        };
    }

    public function getNetAmountAttribute(): float
    {
        return $this->amount - $this->fees;
    }

    public function getIsProfitableAttribute(): bool
    {
        return in_array($this->type, ['sell', 'dividend', 'interest']) && $this->amount > 0;
    }

    // Scopes
    public function scopeForUser($query, $userId)
    {
        return $query->where('user_id', $userId);
    }

    public function scopeByType($query, $type)
    {
        return $query->where('type', $type);
    }

    public function scopeByProduct($query, $productId)
    {
        return $query->where('product_id', $productId);
    }

    public function scopeByInvestment($query, $investmentId)
    {
        return $query->where('investment_id', $investmentId);
    }

    public function scopePurchases($query)
    {
        return $query->where('type', 'buy');
    }

    public function scopeSales($query)
    {
        return $query->where('type', 'sell');
    }

    public function scopeDividends($query)
    {
        return $query->where('type', 'dividend');
    }

    public function scopeInterest($query)
    {
        return $query->where('type', 'interest');
    }

    public function scopeFees($query)
    {
        return $query->where('type', 'fee');
    }

    public function scopeRecent($query, $days = 30)
    {
        return $query->where('created_at', '>=', Carbon::now()->subDays($days));
    }

    public function scopeThisMonth($query)
    {
        return $query->whereMonth('created_at', Carbon::now()->month)
                    ->whereYear('created_at', Carbon::now()->year);
    }

    public function scopeThisYear($query)
    {
        return $query->whereYear('created_at', Carbon::now()->year);
    }

    // Static Methods
    public static function getTotalInvestmentForUser($userId): float
    {
        return self::where('user_id', $userId)
                  ->where('type', 'buy')
                  ->sum('amount');
    }

    public static function getTotalReturnsForUser($userId): float
    {
        return self::where('user_id', $userId)
                  ->whereIn('type', ['sell', 'dividend', 'interest'])
                  ->sum('amount');
    }

    public static function getTotalFeesForUser($userId): float
    {
        return self::where('user_id', $userId)
                  ->sum('fees');
    }

    public static function getInvestmentAnalytics($userId, $period = 30): array
    {
        $startDate = Carbon::now()->subDays($period);
        
        $transactions = self::where('user_id', $userId)
                           ->where('created_at', '>=', $startDate)
                           ->get();

        $purchases = $transactions->where('type', 'buy');
        $sales = $transactions->where('type', 'sell');
        $dividends = $transactions->where('type', 'dividend');
        $interest = $transactions->where('type', 'interest');
        $fees = $transactions->sum('fees');

        return [
            'total_invested' => $purchases->sum('amount'),
            'total_liquidated' => $sales->sum('amount'),
            'total_dividends' => $dividends->sum('amount'),
            'total_interest' => $interest->sum('amount'),
            'total_fees' => $fees,
            'net_investment' => $purchases->sum('amount') - $sales->sum('amount'),
            'total_returns' => $dividends->sum('amount') + $interest->sum('amount'),
            'transaction_count' => $transactions->count(),
            'by_product_type' => [
                'treasury_bills' => $transactions->whereHas('product', function($q) {
                    $q->where('type', 'treasury_bills');
                })->sum('amount'),
                'mutual_funds' => $transactions->whereHas('product', function($q) {
                    $q->where('type', 'mutual_funds');
                })->sum('amount'),
                'corporate_bonds' => $transactions->whereHas('product', function($q) {
                    $q->where('type', 'corporate_bonds');
                })->sum('amount'),
                'stocks' => $transactions->whereHas('product', function($q) {
                    $q->where('type', 'stocks');
                })->sum('amount'),
                'real_estate' => $transactions->whereHas('product', function($q) {
                    $q->where('type', 'real_estate');
                })->sum('amount')
            ]
        ];
    }

    public static function getMonthlyInvestmentTrend($userId, $months = 12): array
    {
        $trend = [];
        
        for ($i = $months - 1; $i >= 0; $i--) {
            $date = Carbon::now()->subMonths($i);
            $monthStart = $date->copy()->startOfMonth();
            $monthEnd = $date->copy()->endOfMonth();
            
            $transactions = self::where('user_id', $userId)
                              ->whereBetween('created_at', [$monthStart, $monthEnd])
                              ->get();
            
            $purchases = $transactions->where('type', 'buy')->sum('amount');
            $sales = $transactions->where('type', 'sell')->sum('amount');
            $returns = $transactions->whereIn('type', ['dividend', 'interest'])->sum('amount');
            
            $trend[] = [
                'month' => $date->format('M Y'),
                'invested' => $purchases,
                'liquidated' => $sales,
                'returns' => $returns,
                'net_flow' => $purchases - $sales + $returns
            ];
        }
        
        return $trend;
    }

    public static function getProductPerformance($userId): array
    {
        $transactions = self::where('user_id', $userId)
                           ->with('product')
                           ->get()
                           ->groupBy('product_id');
        
        $performance = [];
        
        foreach ($transactions as $productId => $productTransactions) {
            $product = $productTransactions->first()->product;
            
            $invested = $productTransactions->where('type', 'buy')->sum('amount');
            $liquidated = $productTransactions->where('type', 'sell')->sum('amount');
            $returns = $productTransactions->whereIn('type', ['dividend', 'interest'])->sum('amount');
            $fees = $productTransactions->sum('fees');
            
            $netInvested = $invested - $liquidated;
            $totalReturns = $returns + $liquidated - $invested;
            $returnPercentage = $invested > 0 ? ($totalReturns / $invested) * 100 : 0;
            
            $performance[] = [
                'product' => $product,
                'total_invested' => $invested,
                'total_liquidated' => $liquidated,
                'net_invested' => $netInvested,
                'total_returns' => $returns,
                'total_fees' => $fees,
                'net_profit_loss' => $totalReturns,
                'return_percentage' => round($returnPercentage, 2),
                'transaction_count' => $productTransactions->count()
            ];
        }
        
        // Sort by return percentage descending
        usort($performance, function($a, $b) {
            return $b['return_percentage'] <=> $a['return_percentage'];
        });
        
        return $performance;
    }

    public static function getDailyTransactionVolume($days = 30): array
    {
        $volume = [];
        
        for ($i = $days - 1; $i >= 0; $i--) {
            $date = Carbon::now()->subDays($i);
            
            $dayTransactions = self::whereDate('created_at', $date)->get();
            
            $volume[] = [
                'date' => $date->toDateString(),
                'total_volume' => $dayTransactions->sum('amount'),
                'purchase_volume' => $dayTransactions->where('type', 'buy')->sum('amount'),
                'sale_volume' => $dayTransactions->where('type', 'sell')->sum('amount'),
                'transaction_count' => $dayTransactions->count()
            ];
        }
        
        return $volume;
    }

    public static function getTopInvestors($limit = 10): array
    {
        return self::selectRaw('user_id, SUM(CASE WHEN type = "buy" THEN amount ELSE 0 END) as total_invested')
                  ->selectRaw('SUM(CASE WHEN type IN ("dividend", "interest") THEN amount ELSE 0 END) as total_returns')
                  ->selectRaw('COUNT(*) as transaction_count')
                  ->with('user')
                  ->groupBy('user_id')
                  ->orderBy('total_invested', 'desc')
                  ->limit($limit)
                  ->get()
                  ->map(function($item) {
                      $returnPercentage = $item->total_invested > 0 ? 
                                        ($item->total_returns / $item->total_invested) * 100 : 0;
                      
                      return [
                          'user' => $item->user,
                          'total_invested' => $item->total_invested,
                          'total_returns' => $item->total_returns,
                          'return_percentage' => round($returnPercentage, 2),
                          'transaction_count' => $item->transaction_count
                      ];
                  })
                  ->toArray();
    }
}
