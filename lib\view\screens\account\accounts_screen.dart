import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:viserpay/core/route/route.dart';
import 'package:viserpay/core/utils/dimensions.dart';
import 'package:viserpay/core/utils/my_color.dart';
import 'package:viserpay/core/utils/my_strings.dart';
import 'package:viserpay/core/utils/style.dart';
import 'package:viserpay/data/controller/account/account_controller.dart';
import 'package:viserpay/data/repo/account/account_repo.dart';
import 'package:viserpay/data/services/api_service.dart';
import 'package:viserpay/view/components/app-bar/custom_appbar.dart';
import 'package:viserpay/view/components/buttons/gradient_rounded_button.dart';
import 'package:viserpay/view/components/custom_loader/custom_loader.dart';
import 'package:viserpay/view/components/no_data.dart';
import 'package:viserpay/view/screens/account/widgets/account_card.dart';

class AccountsScreen extends StatefulWidget {
  const AccountsScreen({super.key});

  @override
  State<AccountsScreen> createState() => _AccountsScreenState();
}

class _AccountsScreenState extends State<AccountsScreen> {
  @override
  void initState() {
    Get.put(ApiClient(sharedPreferences: Get.find()));
    Get.put(AccountRepo(apiClient: Get.find()));
    Get.put(AccountController(accountRepo: Get.find()));
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: MyColor.screenBgColor,
      appBar: CustomAppBar(
        title: MyStrings.myAccounts,
        isShowBackBtn: true,
      ),
      body: GetBuilder<AccountController>(
        builder: (controller) {
          return RefreshIndicator(
            color: MyColor.primaryColor,
            backgroundColor: MyColor.colorWhite,
            onRefresh: () async {
              await controller.loadAccountData();
            },
            child: controller.isLoading
                ? const CustomLoader()
                : controller.userAccounts.isEmpty
                    ? const NoDataWidget()
                    : SingleChildScrollView(
                        physics: const AlwaysScrollableScrollPhysics(),
                        padding: const EdgeInsets.symmetric(
                          vertical: Dimensions.space15,
                          horizontal: Dimensions.space15,
                        ),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            // Total Balance Card
                            Container(
                              width: double.infinity,
                              padding: const EdgeInsets.all(Dimensions.space20),
                              decoration: BoxDecoration(
                                gradient: LinearGradient(
                                  colors: [
                                    MyColor.primaryColor,
                                    MyColor.primaryColor.withOpacity(0.8),
                                  ],
                                  begin: Alignment.topLeft,
                                  end: Alignment.bottomRight,
                                ),
                                borderRadius: BorderRadius.circular(Dimensions.mediumRadius),
                                boxShadow: [
                                  BoxShadow(
                                    color: MyColor.primaryColor.withOpacity(0.3),
                                    blurRadius: 10,
                                    offset: const Offset(0, 5),
                                  ),
                                ],
                              ),
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Text(
                                    MyStrings.availableBalance,
                                    style: regularDefault.copyWith(
                                      color: MyColor.colorWhite.withOpacity(0.8),
                                    ),
                                  ),
                                  const SizedBox(height: Dimensions.space5),
                                  Text(
                                    '${controller.curSymbol}${controller.formatBalance(controller.currentBalance)}',
                                    style: boldExtraLarge.copyWith(
                                      color: MyColor.colorWhite,
                                      fontSize: 28,
                                    ),
                                  ),
                                  const SizedBox(height: Dimensions.space10),
                                  Text(
                                    '${controller.userAccounts.length} ${MyStrings.accounts}',
                                    style: regularDefault.copyWith(
                                      color: MyColor.colorWhite.withOpacity(0.8),
                                    ),
                                  ),
                                ],
                              ),
                            ),
                            
                            const SizedBox(height: Dimensions.space25),
                            
                            // Section Header
                            Row(
                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                              children: [
                                Text(
                                  MyStrings.myAccounts,
                                  style: semiBoldLarge.copyWith(
                                    color: MyColor.colorBlack,
                                  ),
                                ),
                                TextButton.icon(
                                  onPressed: () {
                                    Get.toNamed(RouteHelper.createAccountScreen);
                                  },
                                  icon: const Icon(
                                    Icons.add,
                                    color: MyColor.primaryColor,
                                    size: 18,
                                  ),
                                  label: Text(
                                    MyStrings.createAccount,
                                    style: semiBoldDefault.copyWith(
                                      color: MyColor.primaryColor,
                                    ),
                                  ),
                                ),
                              ],
                            ),
                            
                            const SizedBox(height: Dimensions.space15),
                            
                            // Accounts List
                            ListView.separated(
                              shrinkWrap: true,
                              physics: const NeverScrollableScrollPhysics(),
                              itemCount: controller.userAccounts.length,
                              separatorBuilder: (context, index) => const SizedBox(height: Dimensions.space15),
                              itemBuilder: (context, index) {
                                final account = controller.userAccounts[index];
                                return AccountCard(
                                  account: account,
                                  onTap: () {
                                    controller.selectAccount(account);
                                    Get.toNamed(RouteHelper.accountDetailsScreen);
                                  },
                                  onSetDefault: () {
                                    controller.setDefaultAccount(account);
                                  },
                                  currencySymbol: controller.curSymbol,
                                );
                              },
                            ),
                            
                            const SizedBox(height: Dimensions.space30),
                            
                            // Quick Actions
                            Container(
                              width: double.infinity,
                              padding: const EdgeInsets.all(Dimensions.space20),
                              decoration: BoxDecoration(
                                color: MyColor.colorWhite,
                                borderRadius: BorderRadius.circular(Dimensions.mediumRadius),
                                boxShadow: [
                                  BoxShadow(
                                    color: MyColor.colorGrey.withOpacity(0.1),
                                    blurRadius: 10,
                                    offset: const Offset(0, 2),
                                  ),
                                ],
                              ),
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Text(
                                    'Quick Actions',
                                    style: semiBoldLarge.copyWith(
                                      color: MyColor.colorBlack,
                                    ),
                                  ),
                                  const SizedBox(height: Dimensions.space15),
                                  Row(
                                    children: [
                                      Expanded(
                                        child: GradientRoundedButton(
                                          text: MyStrings.transferBetweenAccounts,
                                          press: () {
                                            if (controller.userAccounts.length >= 2) {
                                              Get.toNamed(RouteHelper.accountTransferScreen);
                                            } else {
                                              Get.snackbar(
                                                'Info',
                                                'You need at least 2 accounts to transfer',
                                                backgroundColor: MyColor.colorOrange,
                                                colorText: MyColor.colorWhite,
                                              );
                                            }
                                          },
                                          isLoading: false,
                                        ),
                                      ),
                                    ],
                                  ),
                                  const SizedBox(height: Dimensions.space10),
                                  Row(
                                    children: [
                                      Expanded(
                                        child: GradientRoundedButton(
                                          text: MyStrings.createAccount,
                                          press: () {
                                            Get.toNamed(RouteHelper.createAccountScreen);
                                          },
                                          isLoading: false,
                                          color: MyColor.secondaryColor,
                                        ),
                                      ),
                                    ],
                                  ),
                                ],
                              ),
                            ),
                          ],
                        ),
                      ),
          );
        },
      ),
    );
  }
}
