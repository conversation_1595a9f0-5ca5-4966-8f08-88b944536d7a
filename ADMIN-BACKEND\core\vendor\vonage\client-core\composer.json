{"name": "vonage/client-core", "type": "library", "description": "PHP Client for using Vonage's API.", "homepage": "https://developer.nexmo.com", "license": "Apache-2.0", "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "PHP Developer Advocate"}], "require": {"php": ">=7.2", "ext-json": "*", "ext-mbstring": "*", "laminas/laminas-diactoros": "^2.4", "lcobucci/jwt": "^3.4|^4.0", "composer/package-versions-deprecated": "^1.11", "psr/container": "^1.0", "psr/http-client-implementation": "^1.0", "vonage/nexmo-bridge": "^0.1.0", "psr/log": "^1.1"}, "require-dev": {"php": ">=7.4", "guzzlehttp/guzzle": ">=6", "helmich/phpunit-json-assert": "^3.3", "php-http/mock-client": "^1.4", "phpstan/phpstan": "^0.12", "phpunit/phpunit": "^8.5|^9.4", "roave/security-advisories": "dev-latest", "squizlabs/php_codesniffer": "^3.5", "softcreatr/jsonpath": "^0.6.4", "phpspec/prophecy-phpunit": "^2.0"}, "config": {"optimize-autoloader": true, "preferred-install": "dist"}, "autoload": {"psr-4": {"Vonage\\": "src/"}}, "autoload-dev": {"psr-4": {"VonageTest\\": "test/"}}, "minimum-stability": "stable", "scripts": {"cs-check": "phpcs", "cs-fix": "phpcbf", "test": "phpunit"}, "support": {"email": "<EMAIL>", "issues": "https://github.com/Vonage/vonage-php-sdk-core/issues", "source": "https://github.com/Vonage/vonage-php-sdk-core", "docs": "https://developer.vonage.com"}}