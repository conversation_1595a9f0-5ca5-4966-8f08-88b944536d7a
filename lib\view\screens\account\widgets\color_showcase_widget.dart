import 'package:flutter/material.dart';
import 'package:viserpay/core/utils/dimensions.dart';
import 'package:viserpay/core/utils/my_color.dart';
import 'package:viserpay/core/utils/style.dart';

/// Widget to showcase the new color scheme with #00d6d6 cyan and dark gradients
class ColorShowcaseWidget extends StatelessWidget {
  const ColorShowcaseWidget({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: MyColor.screenBgColor,
      appBar: AppBar(
        title: const Text('New Color Scheme'),
        backgroundColor: MyColor.primaryColor,
        foregroundColor: MyColor.colorWhite,
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(Dimensions.space20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header
            Text(
              'Updated Account Type Colors',
              style: boldExtraLarge.copyWith(
                color: MyColor.colorBlack,
              ),
            ),
            const SizedBox(height: Dimensions.space20),

            // Account Type Color Cards
            _buildAccountTypeCard(
              'Business Account',
              MyColor.colorCyan,
              Icons.business,
              'New Cyan Color (#00d6d6)',
              'Modern, professional look for business accounts',
            ),
            
            const SizedBox(height: Dimensions.space15),
            
            _buildAccountTypeCard(
              'Student Account',
              MyColor.colorDarkGrey,
              Icons.school,
              'Dark Grey Color (#2c3e50)',
              'Sophisticated dark theme for student accounts',
            ),

            const SizedBox(height: Dimensions.space30),

            // Gradient Showcase
            Text(
              'New Gradient Combinations',
              style: boldExtraLarge.copyWith(
                color: MyColor.colorBlack,
              ),
            ),
            const SizedBox(height: Dimensions.space20),

            // Cyan Gradient Card
            Container(
              width: double.infinity,
              height: 120,
              decoration: BoxDecoration(
                gradient: MyColor.cyanGradient,
                borderRadius: BorderRadius.circular(Dimensions.mediumRadius),
                boxShadow: [
                  BoxShadow(
                    color: MyColor.colorCyan.withOpacity(0.3),
                    blurRadius: 15,
                    offset: const Offset(0, 5),
                  ),
                ],
              ),
              child: Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    const Icon(
                      Icons.gradient,
                      color: MyColor.colorWhite,
                      size: 32,
                    ),
                    const SizedBox(height: Dimensions.space8),
                    Text(
                      'Cyan Gradient',
                      style: boldLarge.copyWith(
                        color: MyColor.colorWhite,
                      ),
                    ),
                    Text(
                      'Perfect for business features',
                      style: regularDefault.copyWith(
                        color: MyColor.colorWhite.withOpacity(0.9),
                      ),
                    ),
                  ],
                ),
              ),
            ),

            const SizedBox(height: Dimensions.space15),

            // Dark Gradient Card
            Container(
              width: double.infinity,
              height: 120,
              decoration: BoxDecoration(
                gradient: MyColor.darkGradient,
                borderRadius: BorderRadius.circular(Dimensions.mediumRadius),
                boxShadow: [
                  BoxShadow(
                    color: MyColor.colorDarkGrey.withOpacity(0.3),
                    blurRadius: 15,
                    offset: const Offset(0, 5),
                  ),
                ],
              ),
              child: Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    const Icon(
                      Icons.dark_mode,
                      color: MyColor.colorWhite,
                      size: 32,
                    ),
                    const SizedBox(height: Dimensions.space8),
                    Text(
                      'Dark Gradient',
                      style: boldLarge.copyWith(
                        color: MyColor.colorWhite,
                      ),
                    ),
                    Text(
                      'Elegant dark theme option',
                      style: regularDefault.copyWith(
                        color: MyColor.colorWhite.withOpacity(0.9),
                      ),
                    ),
                  ],
                ),
              ),
            ),

            const SizedBox(height: Dimensions.space15),

            // Cyan to Dark Gradient Card
            Container(
              width: double.infinity,
              height: 120,
              decoration: BoxDecoration(
                gradient: MyColor.cyanToDarkGradient,
                borderRadius: BorderRadius.circular(Dimensions.mediumRadius),
                boxShadow: [
                  BoxShadow(
                    color: MyColor.colorCyan.withOpacity(0.2),
                    blurRadius: 15,
                    offset: const Offset(0, 5),
                  ),
                ],
              ),
              child: Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    const Icon(
                      Icons.auto_awesome,
                      color: MyColor.colorWhite,
                      size: 32,
                    ),
                    const SizedBox(height: Dimensions.space8),
                    Text(
                      'Cyan to Dark Gradient',
                      style: boldLarge.copyWith(
                        color: MyColor.colorWhite,
                      ),
                    ),
                    Text(
                      'Dynamic transition effect',
                      style: regularDefault.copyWith(
                        color: MyColor.colorWhite.withOpacity(0.9),
                      ),
                    ),
                  ],
                ),
              ),
            ),

            const SizedBox(height: Dimensions.space30),

            // Color Comparison
            Text(
              'Before vs After Comparison',
              style: boldExtraLarge.copyWith(
                color: MyColor.colorBlack,
              ),
            ),
            const SizedBox(height: Dimensions.space20),

            Row(
              children: [
                Expanded(
                  child: Column(
                    children: [
                      Text(
                        'OLD COLORS',
                        style: semiBoldDefault.copyWith(
                          color: MyColor.colorGrey,
                        ),
                      ),
                      const SizedBox(height: Dimensions.space10),
                      Container(
                        height: 60,
                        decoration: BoxDecoration(
                          color: const Color(0xff7367F0), // Old purple
                          borderRadius: BorderRadius.circular(Dimensions.smallRadius),
                        ),
                        child: Center(
                          child: Text(
                            'Purple',
                            style: semiBoldDefault.copyWith(
                              color: MyColor.colorWhite,
                            ),
                          ),
                        ),
                      ),
                      const SizedBox(height: Dimensions.space10),
                      Container(
                        height: 60,
                        decoration: BoxDecoration(
                          color: const Color(0xff17a2b8), // Old teal
                          borderRadius: BorderRadius.circular(Dimensions.smallRadius),
                        ),
                        child: Center(
                          child: Text(
                            'Teal',
                            style: semiBoldDefault.copyWith(
                              color: MyColor.colorWhite,
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
                const SizedBox(width: Dimensions.space20),
                Expanded(
                  child: Column(
                    children: [
                      Text(
                        'NEW COLORS',
                        style: semiBoldDefault.copyWith(
                          color: MyColor.primaryColor,
                        ),
                      ),
                      const SizedBox(height: Dimensions.space10),
                      Container(
                        height: 60,
                        decoration: BoxDecoration(
                          color: MyColor.colorCyan,
                          borderRadius: BorderRadius.circular(Dimensions.smallRadius),
                        ),
                        child: Center(
                          child: Text(
                            'Cyan (#00d6d6)',
                            style: semiBoldDefault.copyWith(
                              color: MyColor.colorWhite,
                            ),
                          ),
                        ),
                      ),
                      const SizedBox(height: Dimensions.space10),
                      Container(
                        height: 60,
                        decoration: BoxDecoration(
                          color: MyColor.colorDarkGrey,
                          borderRadius: BorderRadius.circular(Dimensions.smallRadius),
                        ),
                        child: Center(
                          child: Text(
                            'Dark Grey',
                            style: semiBoldDefault.copyWith(
                              color: MyColor.colorWhite,
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildAccountTypeCard(
    String title,
    Color color,
    IconData icon,
    String colorName,
    String description,
  ) {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(Dimensions.space20),
      decoration: BoxDecoration(
        color: MyColor.colorWhite,
        borderRadius: BorderRadius.circular(Dimensions.mediumRadius),
        border: Border.all(color: color, width: 2),
        boxShadow: [
          BoxShadow(
            color: color.withOpacity(0.1),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(Dimensions.space15),
            decoration: BoxDecoration(
              color: color.withOpacity(0.1),
              borderRadius: BorderRadius.circular(Dimensions.smallRadius),
            ),
            child: Icon(
              icon,
              color: color,
              size: 32,
            ),
          ),
          const SizedBox(width: Dimensions.space15),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: semiBoldLarge.copyWith(
                    color: color,
                  ),
                ),
                const SizedBox(height: Dimensions.space5),
                Text(
                  colorName,
                  style: semiBoldDefault.copyWith(
                    color: MyColor.colorBlack,
                  ),
                ),
                const SizedBox(height: Dimensions.space5),
                Text(
                  description,
                  style: regularDefault.copyWith(
                    color: MyColor.colorGrey,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
