import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';
import 'package:viserpay/core/utils/dimensions.dart';
import 'package:viserpay/core/utils/my_color.dart';
import 'package:viserpay/core/utils/style.dart';
import 'package:viserpay/view/components/text-form-field/enhanced_text_field.dart';
import 'package:viserpay/view/screens/auth/signup/signup_controller.dart';

class SecurityStep extends StatelessWidget {
  final SignupController controller;

  const SecurityStep({
    super.key,
    required this.controller,
  });

  @override
  Widget build(BuildContext context) {
    return Form(
      key: controller.securityFormKey,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // PIN Section
          _buildSectionHeader(
            'Transaction PIN',
            'Create a 4-digit PIN for secure transactions',
          ),
          
          const SizedBox(height: Dimensions.space15),
          
          EnhancedTextField(
            label: 'Transaction PIN',
            hint: 'Enter 4-digit PIN',
            controller: controller.pinController,
            keyboardType: TextInputType.number,
            textInputAction: TextInputAction.next,
            obscureText: true,
            maxLength: 4,
            inputFormatters: [
              FilteringTextInputFormatter.digitsOnly,
              LengthLimitingTextInputFormatter(4),
            ],
            validator: (value) {
              if (value == null || value.trim().isEmpty) {
                return 'PIN is required';
              }
              if (value.trim().length != 4) {
                return 'PIN must be exactly 4 digits';
              }
              if (!RegExp(r'^[0-9]+$').hasMatch(value.trim())) {
                return 'PIN must contain only numbers';
              }
              // Check for weak PINs
              if (_isWeakPin(value.trim())) {
                return 'Please choose a stronger PIN';
              }
              return null;
            },
            prefixIcon: const Icon(Icons.pin_outlined),
          ),
          
          const SizedBox(height: Dimensions.space30),
          
          // Password Section
          _buildSectionHeader(
            'Account Password',
            'Create a strong password for your account',
          ),
          
          const SizedBox(height: Dimensions.space15),
          
          Obx(() => EnhancedTextField(
            label: 'Password',
            hint: 'Enter your password',
            controller: controller.passwordController,
            keyboardType: TextInputType.visiblePassword,
            textInputAction: TextInputAction.next,
            obscureText: !controller.showPassword.value,
            validator: (value) {
              if (value == null || value.trim().isEmpty) {
                return 'Password is required';
              }
              if (value.trim().length < 8) {
                return 'Password must be at least 8 characters';
              }
              if (!_isStrongPassword(value.trim())) {
                return 'Password must contain uppercase, lowercase, number, and special character';
              }
              return null;
            },
            prefixIcon: const Icon(Icons.lock_outline),
            suffixIcon: IconButton(
              onPressed: controller.togglePasswordVisibility,
              icon: Icon(
                controller.showPassword.value
                    ? Icons.visibility_off_outlined
                    : Icons.visibility_outlined,
                color: MyColor.contentTextColor,
              ),
            ),
          )),
          
          const SizedBox(height: Dimensions.space20),
          
          Obx(() => EnhancedTextField(
            label: 'Confirm Password',
            hint: 'Re-enter your password',
            controller: controller.confirmPasswordController,
            keyboardType: TextInputType.visiblePassword,
            textInputAction: TextInputAction.done,
            obscureText: !controller.showConfirmPassword.value,
            validator: (value) {
              if (value == null || value.trim().isEmpty) {
                return 'Please confirm your password';
              }
              if (value.trim() != controller.passwordController.text.trim()) {
                return 'Passwords do not match';
              }
              return null;
            },
            prefixIcon: const Icon(Icons.lock_outline),
            suffixIcon: IconButton(
              onPressed: controller.toggleConfirmPasswordVisibility,
              icon: Icon(
                controller.showConfirmPassword.value
                    ? Icons.visibility_off_outlined
                    : Icons.visibility_outlined,
                color: MyColor.contentTextColor,
              ),
            ),
          )),
          
          const SizedBox(height: Dimensions.space20),
          
          // Password Requirements
          _buildPasswordRequirements(),
          
          const SizedBox(height: Dimensions.space30),
          
          // Security Notice
          Container(
            padding: const EdgeInsets.all(Dimensions.space15),
            decoration: BoxDecoration(
              color: MyColor.colorGreen.withOpacity(0.05),
              borderRadius: BorderRadius.circular(Dimensions.mediumRadius),
              border: Border.all(
                color: MyColor.colorGreen.withOpacity(0.2),
                width: 1,
              ),
            ),
            child: Row(
              children: [
                Icon(
                  Icons.security_outlined,
                  color: MyColor.colorGreen,
                  size: 20,
                ),
                const SizedBox(width: Dimensions.space10),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Security Notice',
                        style: mediumDefault.copyWith(
                          color: MyColor.colorGreen,
                        ),
                      ),
                      const SizedBox(height: Dimensions.space5),
                      Text(
                        'Your PIN and password are encrypted and stored securely. Never share them with anyone.',
                        style: regularSmall.copyWith(
                          color: MyColor.contentTextColor,
                          height: 1.4,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSectionHeader(String title, String description) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          title,
          style: semiBoldLarge.copyWith(
            color: MyColor.primaryTextColor,
          ),
        ),
        const SizedBox(height: Dimensions.space5),
        Text(
          description,
          style: regularDefault.copyWith(
            color: MyColor.contentTextColor,
          ),
        ),
      ],
    );
  }

  Widget _buildPasswordRequirements() {
    return Obx(() {
      final password = controller.passwordController.text;
      
      return Container(
        padding: const EdgeInsets.all(Dimensions.space15),
        decoration: BoxDecoration(
          color: MyColor.borderColor.withOpacity(0.3),
          borderRadius: BorderRadius.circular(Dimensions.mediumRadius),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Password Requirements:',
              style: mediumDefault.copyWith(
                color: MyColor.primaryTextColor,
              ),
            ),
            const SizedBox(height: Dimensions.space10),
            _buildRequirementItem(
              'At least 8 characters',
              password.length >= 8,
            ),
            _buildRequirementItem(
              'Contains uppercase letter (A-Z)',
              password.contains(RegExp(r'[A-Z]')),
            ),
            _buildRequirementItem(
              'Contains lowercase letter (a-z)',
              password.contains(RegExp(r'[a-z]')),
            ),
            _buildRequirementItem(
              'Contains number (0-9)',
              password.contains(RegExp(r'[0-9]')),
            ),
            _buildRequirementItem(
              'Contains special character (!@#\$%^&*)',
              password.contains(RegExp(r'[!@#$%^&*(),.?":{}|<>]')),
            ),
          ],
        ),
      );
    });
  }

  Widget _buildRequirementItem(String text, bool isMet) {
    return Padding(
      padding: const EdgeInsets.only(bottom: Dimensions.space5),
      child: Row(
        children: [
          Icon(
            isMet ? Icons.check_circle : Icons.radio_button_unchecked,
            color: isMet ? MyColor.colorGreen : MyColor.contentTextColor,
            size: 16,
          ),
          const SizedBox(width: Dimensions.space8),
          Expanded(
            child: Text(
              text,
              style: regularSmall.copyWith(
                color: isMet ? MyColor.colorGreen : MyColor.contentTextColor,
              ),
            ),
          ),
        ],
      ),
    );
  }

  bool _isWeakPin(String pin) {
    // Check for common weak PINs
    final weakPins = ['0000', '1111', '2222', '3333', '4444', '5555', '6666', '7777', '8888', '9999', '1234', '4321', '0123'];
    return weakPins.contains(pin);
  }

  bool _isStrongPassword(String password) {
    final hasUppercase = password.contains(RegExp(r'[A-Z]'));
    final hasLowercase = password.contains(RegExp(r'[a-z]'));
    final hasDigits = password.contains(RegExp(r'[0-9]'));
    final hasSpecialCharacters = password.contains(RegExp(r'[!@#$%^&*(),.?":{}|<>]'));

    return hasUppercase && hasLowercase && hasDigits && hasSpecialCharacters;
  }
}
