<?php

use Illuminate\Support\Facades\Route;

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for your application. These
| routes are loaded by the RouteServiceProvider within a group which
| is assigned the "api" middleware group. Enjoy building your API!
|
*/ 

Route::namespace('Api')->name('api.')->group(function(){

    Route::controller('AppController')->group(function(){
        Route::get('general-setting', 'generalSetting')->name('general.setting');
        Route::get('module-setting', 'moduleSetting')->name('module.setting');
        Route::get('get-countries', 'getCountries')->name('get.countries');
        Route::get('language/{code?}', 'language')->name('language');
        Route::get('policy-pages', 'policyPages')->name('policy.pages');
    });

    Route::controller('AppController')->group(function(){
        Route::get('general-setting', 'generalSetting')->name('general.setting');
        Route::get('module-setting', 'moduleSetting')->name('module.setting');
        Route::get('get-countries', 'getCountries')->name('get.countries');
        Route::get('language/{code?}', 'language')->name('language');
        Route::get('policy-pages', 'policyPages')->name('policy.pages');
        Route::get('faq', 'faq')->name('faq');
    });

	Route::namespace('Auth')->group(function(){
		Route::post('login', 'LoginController@login');
		Route::post('register', 'RegisterController@register');

        Route::controller('ForgotPasswordController')->group(function(){
            Route::post('password/email', 'sendResetCodeEmail')->name('password.email');
            Route::post('password/verify-code', 'verifyCode')->name('password.verify.code');
            Route::post('password/reset', 'reset')->name('password.update');
        });
	});

    Route::middleware(['auth:sanctum', 'ability:user'])->group(function () {

        //authorization
        Route::controller('AuthorizationController')->group(function(){
            Route::get('authorization', 'authorization')->name('authorization');
            Route::get('resend-verify/{type}', 'sendVerifyCode')->name('send.verify.code');
            Route::post('verify-email', 'emailVerification')->name('verify.email');
            Route::post('verify-mobile', 'mobileVerification')->name('verify.mobile');
            Route::post('verify-g2fa', 'g2faVerification')->name('go2fa.verify');
        });

        Route::middleware(['check.status'])->group(function () {
            Route::post('user-data-submit', 'UserController@userDataSubmit')->name('data.submit');

            Route::middleware('registration.complete')->group(function(){

                Route::controller('UserController')->group(function(){

                    Route::post('/get/device/token', 'getDeviceToken')->name('get.device.token');

                    Route::get('dashboard','dashboard')->name('dashboard');
                    Route::get('user-info','userInfo')->name('user.info');

                    //KYC
                    Route::get('kyc-form','kycForm')->name('kyc.form');
                    Route::post('kyc-submit','kycSubmit')->name('kyc.submit');

                    //Report
                    Route::any('deposit/history', 'depositHistory')->name('deposit.history');
                    Route::get('transactions','transactions')->name('transactions');

                    Route::post('/qr-code/scan', 'qrCodeScan')->name('qr.code.scan');
                    Route::get('/qr-code', 'qrCode')->name('qr.code');  
                    Route::post('/qr-code/download', 'qrCodeDownload')->name('qr.code.download');
                    Route::post('/qr-code/remove', 'qrCodeRemove')->name('qr.code.remove');

                    Route::post('account/delete', 'accountDelete');
                    
                    Route::get('limit-charge', 'trxLimit');

                    Route::get('notification/settings', 'notificationSettings')->name('notification.settings');
                    Route::post('notification/settings', 'notificationSettingsUpdate')->name('notification.settings.update');
                    Route::post('remove/promotional/notification/image', 'removePromotionalNotificationImage')->name('remove.promotional.notification.image');

                    Route::get('twofactor', 'show2faData');
                    Route::post('twofactor/enable', 'create2fa');
                    Route::post('twofactor/disable', 'disable2fa');

                    Route::post('pin/validate', 'validatePin');
                });

                //Profile setting
                Route::controller('UserController')->group(function(){
                    Route::post('profile-setting', 'submitProfile');
                    Route::post('change-password', 'submitPassword');
                });

                Route::controller('OtpController')->group(function () {
                    Route::post('otp-verify', 'otpVerify')->name('verify.otp.submit');
                    Route::post('otp-resend', 'otpResend')->name('verify.otp.resend');
                });

                //Cash out 
                Route::controller('CashOutController')->middleware(['module:cash_out', 'kyc:web,cash_out'])->group(function(){
                    Route::post('/agent/exist', 'checkUser')->name('agent.check.exist');
                    Route::get('/cash-out', 'cashOut')->name('cash.out');
                    Route::post('/cash-out', 'cashOutConfirm');
                    Route::post('/cash-out-done/{actionId?}', 'cashOutDone')->name('cash.out.done');
                    Route::get('/cash-out/history', 'cashOutHistory')->name('cash.out.history');
                });

                //Make payment 
                Route::controller('MakePaymentController')->middleware(['module:make_payment', 'kyc:web,make_payment'])->group(function(){
                    Route::post('/merchant/exist', 'checkUser')->name('merchant.check.exist');
                    Route::get('/make-payment', 'makePayment')->name('make.payment');
                    Route::post('/make-payment', 'makePaymentConfirm');
                    Route::post('/make-payment-done/{actionId?}', 'makePaymentDone')->name('make.payment.done');
                    Route::get('/make-payment/history', 'makePaymentHistory')->name('make.payment.history');
                });

                //Send money
                Route::controller('SendMoneyController')->middleware(['module:send_money', 'kyc:web,send_money'])->group(function(){
                    Route::get('/send/money', 'sendMoney')->name('send.money');
                    Route::post('/send/money', 'sendMoneyConfirm');
                    Route::post('/send/money-done/{actionId?}', 'sendMoneyDone')->name('send.money.done');
                    Route::post('/user/exist', 'checkUser')->name('check.exist');
                    Route::get('/send/money/history', 'sendMoneyHistory')->name('send.money.history');
                });
                 
                //Pay bill
                Route::controller('PayBillController')->middleware(['module:utility_bill', 'kyc:web,pay_bill'])->group(function(){
                    Route::get('/pay/bill', 'payBill')->name('pay.bill');
                    Route::post('/pay/bill', 'payBillConfirm')->name('pay.bill.confirm');
                    Route::post('/pay/bill-done/{actionId?}', 'payBillDone')->name('pay.bill.done');
                    Route::get('/pay/bill/history', 'payBillHistory')->name('pay.bill.history');
                    Route::get('/pay/bill/download/{id}', 'payBillDownload')->name('pay.bill.download');
                });
                
                //Mobile recharge
                Route::controller('MobileRechargeController')->middleware(['module:mobile_recharge', 'kyc:web,mobile_recharge'])->group(function(){
                    Route::get('/mobile/recharge', 'mobileRecharge')->name('mobile.recharge');
                    Route::post('/mobile/recharge', 'mobileRechargeConfirm');
                    Route::post('/mobile/recharge-done/{actionId?}', 'mobileRechargeDone')->name('mobile.recharge.done');
                    Route::get('/mobile/recharge/history', 'mobileRechargeHistory')->name('mobile.recharge.history');
                });
                 
                //Donation
                Route::controller('DonationController')->middleware(['module:donation', 'kyc:web,donation'])->group(function(){
                    Route::get('/donation', 'donation')->name('donation');
                    Route::post('/donation', 'donationConfirm');
                    Route::post('/donation-done/{actionId?}', 'donationDone')->name('donation.done');
                    Route::get('/donation/history', 'donationHistory')->name('donation.history');
                });

                //Bank Transfer
                Route::controller('BankTransferController')->middleware(['module:bank_transfer', 'kyc:web,bank_transfer'])->group(function(){
                    Route::post('/add/bank', 'addBank')->name('add.bank');
                    Route::get('/bank/details/{id}', 'bankDetails')->name('bank.details');
                    Route::post('/update/bank', 'updateBank')->name('update.bank');
                    Route::post('/delete/bank', 'deleteBank')->name('delete.bank');

                    Route::get('/bank/transfer', 'bankTransfer')->name('bank.transfer');
                    Route::post('/bank/transfer', 'bankTransferConfirm');
                    Route::post('/bank/transfer-done/{actionId?}', 'bankTransferDone')->name('bank.transfer.done');

                    Route::get('/bank/transfer/history', 'bankTransferHistory')->name('bank.transfer.history');
                });
 
                // Payment
                Route::controller('PaymentController')->middleware(['module:add_money'])->group(function(){
                    Route::get('deposit/methods', 'methods')->name('deposit');
                    Route::post('deposit/insert', 'depositInsert')->name('deposit.insert');
                    Route::get('deposit/confirm', 'depositConfirm')->name('deposit.confirm');
                    Route::get('deposit/manual', 'manualDepositConfirm')->name('deposit.manual.confirm');
                    Route::post('deposit/manual', 'manualDepositUpdate')->name('deposit.manual.update');
                });

            });
        });

        Route::get('logout', 'Auth\LoginController@logout');
    });

    // KojaPay Enhanced API Routes
    Route::group(['prefix' => 'kojapay', 'middleware' => ['auth:sanctum', 'ability:user', 'check.status', 'registration.complete']], function () {

        // Savings routes
        Route::group(['prefix' => 'savings'], function () {
            Route::get('overview', 'SavingsController@overview');
            Route::get('products', 'SavingsController@products');
            Route::get('user-savings', 'SavingsController@getUserSavings');
            Route::post('calculate-interest', 'SavingsController@calculateInterest');

            // Fixed deposits
            Route::post('fixed-deposit/create', 'SavingsController@createFixedDeposit');
            Route::post('fixed-deposit/break/{id}', 'FixedDepositController@breakDeposit');
            Route::post('fixed-deposit/renew/{id}', 'FixedDepositController@renewDeposit');

            // Target savings
            Route::post('target-saving/create', 'SavingsController@createTargetSaving');
            Route::post('target-saving/contribute/{id}', 'TargetSavingsController@contribute');
            Route::post('target-saving/withdraw/{id}', 'TargetSavingsController@withdraw');
            Route::post('target-saving/pause/{id}', 'TargetSavingsController@pause');
            Route::post('target-saving/resume/{id}', 'TargetSavingsController@resume');

            // Flex savings
            Route::post('flex/deposit', 'FlexSavingsController@deposit');
            Route::post('flex/withdraw', 'FlexSavingsController@withdraw');
            Route::get('flex/balance', 'FlexSavingsController@getBalance');
            Route::get('flex/analytics', 'FlexSavingsController@getAnalytics');

            // Group savings
            Route::post('group/create', 'GroupSavingsController@create');
            Route::post('group/join/{id}', 'GroupSavingsController@join');
            Route::post('group/leave/{id}', 'GroupSavingsController@leave');
            Route::post('group/contribute/{id}', 'GroupSavingsController@contribute');
            Route::get('group/user', 'GroupSavingsController@getUserGroups');
            Route::get('group/available', 'GroupSavingsController@getAvailableGroups');
        });

        // Investment routes
        Route::group(['prefix' => 'investments'], function () {
            Route::get('products', 'InvestmentController@products');
            Route::get('portfolio', 'InvestmentController@portfolio');
            Route::post('buy', 'InvestmentController@buyInvestment');
            Route::post('sell', 'InvestmentController@sellInvestment');
            Route::get('performance', 'InvestmentController@performance');
            Route::get('market-data', 'InvestmentController@marketData');
            Route::get('analytics', 'InvestmentController@analytics');
        });

        // Card management routes
        Route::group(['prefix' => 'cards'], function () {
            Route::get('user', 'CardController@getUserCards');
            Route::post('request', 'CardController@requestCard');
            Route::get('analytics', 'CardController@getCardAnalytics');

            // PIN management
            Route::post('pin/create', 'CardController@createPin');
            Route::post('pin/change', 'CardController@changePin');
            Route::post('pin/reset', 'CardController@resetPin');
            Route::post('pin/unlock', 'CardController@unlockPin');

            // Card controls
            Route::post('freeze', 'CardController@freezeCard');
            Route::post('unfreeze', 'CardController@unfreezeCard');
            Route::post('update-limits', 'CardController@updateLimits');

            // Card transactions
            Route::get('transactions/{cardId}', 'CardTransactionController@getTransactions');
            Route::get('transactions/{cardId}/analytics', 'CardTransactionController@getAnalytics');
        });

        // Loan routes
        Route::group(['prefix' => 'loans'], function () {
            Route::get('products', 'LoanController@getProducts');
            Route::post('eligibility-check', 'LoanController@checkEligibility');
            Route::post('apply', 'LoanController@applyForLoan');
            Route::get('user', 'LoanController@getUserLoans');
            Route::get('schedule/{loanId}', 'LoanController@getRepaymentSchedule');
            Route::post('repay/{loanId}', 'LoanRepaymentController@makePayment');
            Route::post('auto-debit/setup', 'LoanRepaymentController@setupAutoDebit');
            Route::post('auto-debit/toggle/{loanId}', 'LoanRepaymentController@toggleAutoDebit');
        });

        // Insights and analytics routes
        Route::group(['prefix' => 'insights'], function () {
            Route::get('overview', 'InsightsController@overview');
            Route::get('spending', 'InsightsController@spendingAnalytics');
            Route::get('savings', 'InsightsController@savingsAnalytics');
            Route::get('income', 'InsightsController@incomeAnalytics');
            Route::get('goals', 'InsightsController@goalsAnalytics');
            Route::get('financial-health', 'InsightsController@financialHealthScore');
        });

        // Referral routes
        Route::group(['prefix' => 'referrals'], function () {
            Route::get('code', 'ReferralController@getReferralCode');
            Route::post('use-code', 'ReferralController@useReferralCode');
            Route::get('stats', 'ReferralController@getReferralStats');
            Route::get('leaderboard', 'ReferralController@getLeaderboard');
        });

        // Account tier routes
        Route::group(['prefix' => 'tiers'], function () {
            Route::get('current', 'TierController@getCurrentTier');
            Route::get('available', 'TierController@getAvailableTiers');
            Route::get('upgrade-requirements', 'TierController@getUpgradeRequirements');
            Route::post('request-upgrade', 'TierController@requestUpgrade');
        });
    });

    // Public KojaPay routes (no authentication required)
    Route::group(['prefix' => 'kojapay/public'], function () {
        Route::get('rates', 'PublicController@getRates');
        Route::get('investment-products', 'PublicController@getInvestmentProducts');
        Route::get('loan-products', 'PublicController@getLoanProducts');
        Route::get('account-tiers', 'PublicController@getAccountTiers');
        Route::get('app-settings', 'PublicController@getAppSettings');
    });

});
