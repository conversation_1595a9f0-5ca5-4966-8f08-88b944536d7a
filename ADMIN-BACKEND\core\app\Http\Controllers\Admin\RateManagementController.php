<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\AdminRate;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Cache;

class RateManagementController extends Controller
{
    public function __construct()
    {
        $this->middleware('admin');
    }

    /**
     * Display rate management dashboard
     */
    public function index()
    {
        $pageTitle = 'Rate Management';
        
        $rates = AdminRate::orderBy('category')
                         ->orderBy('type')
                         ->orderBy('name')
                         ->get()
                         ->groupBy('category');

        $categories = [
            'savings' => 'Savings Products',
            'investment' => 'Investment Products', 
            'loan' => 'Loan Products',
            'transaction' => 'Transaction Fees',
            'card' => 'Card Fees'
        ];

        return view('admin.rate_management.index', compact('pageTitle', 'rates', 'categories'));
    }

    /**
     * Show savings rates management
     */
    public function savingsRates()
    {
        $pageTitle = 'Savings Rates Management';
        
        $rates = AdminRate::byCategory('savings')->get()->groupBy('type');
        $currentRates = AdminRate::getSavingsRates();

        return view('admin.rate_management.savings', compact('pageTitle', 'rates', 'currentRates'));
    }

    /**
     * Show investment rates management
     */
    public function investmentRates()
    {
        $pageTitle = 'Investment Rates Management';
        
        $rates = AdminRate::byCategory('investment')->get()->groupBy('type');
        $currentRates = AdminRate::getInvestmentRates();

        return view('admin.rate_management.investment', compact('pageTitle', 'rates', 'currentRates'));
    }

    /**
     * Show loan rates management
     */
    public function loanRates()
    {
        $pageTitle = 'Loan Rates Management';
        
        $rates = AdminRate::byCategory('loan')->get()->groupBy('type');
        $currentRates = AdminRate::getLoanRates();

        return view('admin.rate_management.loan', compact('pageTitle', 'rates', 'currentRates'));
    }

    /**
     * Show transaction fees management
     */
    public function transactionFees()
    {
        $pageTitle = 'Transaction Fees Management';
        
        $rates = AdminRate::byCategory('transaction')->get()->groupBy('type');
        $currentRates = AdminRate::getTransactionFees();

        return view('admin.rate_management.transaction', compact('pageTitle', 'rates', 'currentRates'));
    }

    /**
     * Show card fees management
     */
    public function cardFees()
    {
        $pageTitle = 'Card Fees Management';
        
        $rates = AdminRate::byCategory('card')->get()->groupBy('type');
        $currentRates = AdminRate::getCardFees();

        return view('admin.rate_management.card', compact('pageTitle', 'rates', 'currentRates'));
    }

    /**
     * Update a specific rate
     */
    public function updateRate(Request $request, $id)
    {
        $request->validate([
            'rate' => 'required|numeric|min:0'
        ]);

        $adminRate = AdminRate::findOrFail($id);
        
        $result = $adminRate->updateRate($request->rate);

        if ($result['success']) {
            $notify[] = ['success', $result['message']];
        } else {
            $notify[] = ['error', $result['message']];
        }

        return back()->withNotify($notify);
    }

    /**
     * Bulk update rates
     */
    public function bulkUpdateRates(Request $request)
    {
        $request->validate([
            'rates' => 'required|array',
            'rates.*' => 'required|numeric|min:0'
        ]);

        $updatedCount = 0;
        $errors = [];

        foreach ($request->rates as $rateId => $newRate) {
            try {
                $adminRate = AdminRate::findOrFail($rateId);
                $result = $adminRate->updateRate($newRate);
                
                if ($result['success']) {
                    $updatedCount++;
                } else {
                    $errors[] = "Failed to update {$adminRate->display_name}: {$result['message']}";
                }
            } catch (\Exception $e) {
                $errors[] = "Error updating rate ID {$rateId}: " . $e->getMessage();
            }
        }

        if ($updatedCount > 0) {
            $notify[] = ['success', "Successfully updated {$updatedCount} rates"];
        }

        if (!empty($errors)) {
            foreach ($errors as $error) {
                $notify[] = ['error', $error];
            }
        }

        return back()->withNotify($notify ?? []);
    }

    /**
     * Create new rate
     */
    public function createRate(Request $request)
    {
        $request->validate([
            'category' => 'required|string|max:255',
            'type' => 'required|string|max:255',
            'name' => 'required|string|max:255',
            'rate' => 'required|numeric|min:0',
            'unit' => 'required|string|max:50',
            'description' => 'nullable|string',
            'is_percentage' => 'boolean',
            'min_value' => 'nullable|numeric|min:0',
            'max_value' => 'nullable|numeric|min:0'
        ]);

        // Check if rate already exists
        $existingRate = AdminRate::where('category', $request->category)
                                ->where('type', $request->type)
                                ->where('name', $request->name)
                                ->first();

        if ($existingRate) {
            $notify[] = ['error', 'Rate with this category, type, and name already exists'];
            return back()->withNotify($notify);
        }

        AdminRate::create([
            'category' => $request->category,
            'type' => $request->type,
            'name' => $request->name,
            'rate' => $request->rate,
            'unit' => $request->unit,
            'description' => $request->description,
            'is_percentage' => $request->boolean('is_percentage', true),
            'is_active' => true,
            'admin_configurable_rate' => true,
            'min_value' => $request->min_value,
            'max_value' => $request->max_value
        ]);

        $notify[] = ['success', 'Rate created successfully'];
        return back()->withNotify($notify);
    }

    /**
     * Toggle rate active status
     */
    public function toggleRate(Request $request, $id)
    {
        $adminRate = AdminRate::findOrFail($id);
        
        $adminRate->is_active = !$adminRate->is_active;
        $adminRate->save();

        // Clear cache
        Cache::forget($adminRate->cache_key);

        $status = $adminRate->is_active ? 'activated' : 'deactivated';
        $notify[] = ['success', "Rate {$status} successfully"];

        return back()->withNotify($notify);
    }

    /**
     * Delete rate
     */
    public function deleteRate($id)
    {
        $adminRate = AdminRate::findOrFail($id);
        
        // Clear cache before deletion
        Cache::forget($adminRate->cache_key);
        
        $adminRate->delete();

        $notify[] = ['success', 'Rate deleted successfully'];
        return back()->withNotify($notify);
    }

    /**
     * Reset rates to default
     */
    public function resetToDefault(Request $request)
    {
        $category = $request->get('category');

        if ($category) {
            // Reset specific category
            AdminRate::where('category', $category)->delete();
        } else {
            // Reset all rates
            AdminRate::truncate();
        }

        // Seed default rates
        AdminRate::seedDefaultRates();

        // Clear all cache
        AdminRate::clearAllCache();

        $message = $category ? "Reset {$category} rates to default" : "Reset all rates to default";
        $notify[] = ['success', $message];

        return back()->withNotify($notify);
    }

    /**
     * Export rates configuration
     */
    public function exportRates()
    {
        $rates = AdminRate::all();
        
        $filename = 'kojapay_rates_' . date('Y-m-d_H-i-s') . '.json';
        
        $headers = [
            'Content-Type' => 'application/json',
            'Content-Disposition' => 'attachment; filename="' . $filename . '"'
        ];

        return response()->json($rates->toArray(), 200, $headers);
    }

    /**
     * Import rates configuration
     */
    public function importRates(Request $request)
    {
        $request->validate([
            'rates_file' => 'required|file|mimes:json'
        ]);

        try {
            $file = $request->file('rates_file');
            $content = file_get_contents($file->getPathname());
            $rates = json_decode($content, true);

            if (!is_array($rates)) {
                throw new \Exception('Invalid JSON format');
            }

            $importedCount = 0;
            $errors = [];

            foreach ($rates as $rateData) {
                try {
                    AdminRate::updateOrCreate(
                        [
                            'category' => $rateData['category'],
                            'type' => $rateData['type'],
                            'name' => $rateData['name']
                        ],
                        $rateData
                    );
                    $importedCount++;
                } catch (\Exception $e) {
                    $errors[] = "Failed to import rate: " . $e->getMessage();
                }
            }

            // Clear cache after import
            AdminRate::clearAllCache();

            $notify[] = ['success', "Successfully imported {$importedCount} rates"];
            
            if (!empty($errors)) {
                foreach (array_slice($errors, 0, 5) as $error) { // Show max 5 errors
                    $notify[] = ['warning', $error];
                }
            }

        } catch (\Exception $e) {
            $notify[] = ['error', 'Import failed: ' . $e->getMessage()];
        }

        return back()->withNotify($notify ?? []);
    }

    /**
     * Get rate history/audit log
     */
    public function rateHistory($id)
    {
        $adminRate = AdminRate::findOrFail($id);
        $pageTitle = "Rate History - {$adminRate->display_name}";
        
        // Get activity log for this rate
        $activities = activity()
            ->forSubject($adminRate)
            ->orderBy('created_at', 'desc')
            ->paginate(20);

        return view('admin.rate_management.history', compact('pageTitle', 'adminRate', 'activities'));
    }
}
