import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

enum LoanType { personal, business, emergency }
enum LoanStatus { pending, approved, rejected, disbursed, repaying, completed }

class LoanController extends GetxController {
  bool isLoading = false;
  bool submitLoading = false;

  // Form controllers
  final TextEditingController loanAmountController = TextEditingController();
  final TextEditingController loanPurposeController = TextEditingController();
  final TextEditingController monthlyIncomeController = TextEditingController();
  final TextEditingController employerNameController = TextEditingController();
  final TextEditingController workAddressController = TextEditingController();
  final TextEditingController guarantorNameController = TextEditingController();
  final TextEditingController guarantorPhoneController = TextEditingController();

  // Focus nodes
  final FocusNode loanAmountFocusNode = FocusNode();
  final FocusNode loanPurposeFocusNode = FocusNode();
  final FocusNode monthlyIncomeFocusNode = FocusNode();
  final FocusNode employerNameFocusNode = FocusNode();
  final FocusNode workAddressFocusNode = FocusNode();
  final FocusNode guarantorNameFocusNode = FocusNode();
  final FocusNode guarantorPhoneFocusNode = FocusNode();

  // Selected values
  LoanType selectedLoanType = LoanType.personal;
  int selectedDurationMonths = 6;
  String selectedRepaymentFrequency = 'monthly';

  // Data
  String currentBalance = '0';
  String currency = '₦';
  double eligibleAmount = 0.0;
  double interestRate = 0.0;
  double monthlyRepayment = 0.0;
  double totalRepayment = 0.0;
  int creditScore = 0;
  bool isEligible = false;

  List<Map<String, dynamic>> userLoans = [];
  List<Map<String, dynamic>> loanHistory = [];
  List<int> availableDurations = [3, 6, 9, 12, 18, 24];
  List<String> repaymentFrequencies = ['weekly', 'monthly'];

  @override
  void onInit() {
    super.onInit();
    loadLoanData();
    checkLoanEligibility();
  }

  void selectLoanType(LoanType type) {
    selectedLoanType = type;
    calculateLoanTerms();
    update();
  }

  void selectDuration(int months) {
    selectedDurationMonths = months;
    calculateLoanTerms();
    update();
  }

  void selectRepaymentFrequency(String frequency) {
    selectedRepaymentFrequency = frequency;
    calculateLoanTerms();
    update();
  }

  Future<void> loadLoanData() async {
    isLoading = true;
    update();

    try {
      // Simulate API call
      await Future.delayed(const Duration(seconds: 1));
      
      // Mock data
      currentBalance = '125000';
      userLoans = [
        {
          'id': '1',
          'amount': 50000,
          'status': 'repaying',
          'remaining_balance': 25000,
          'next_payment_date': '2024-02-15',
          'monthly_payment': 5000,
        }
      ];
      
      loanHistory = [
        {
          'id': '2',
          'amount': 30000,
          'status': 'completed',
          'disbursed_date': '2023-08-01',
          'completed_date': '2024-01-01',
        }
      ];
    } catch (e) {
      print('Error loading loan data: $e');
    }

    isLoading = false;
    update();
  }

  Future<void> checkLoanEligibility() async {
    try {
      // Simulate eligibility check
      await Future.delayed(const Duration(milliseconds: 500));
      
      // Mock eligibility calculation based on:
      // - Account age
      // - Transaction history
      // - Current balance
      // - Previous loan performance
      
      double balance = double.tryParse(currentBalance) ?? 0;
      
      if (balance >= 10000) {
        isEligible = true;
        creditScore = 750; // Good credit score
        eligibleAmount = balance * 2; // 2x current balance
        interestRate = 15.0; // 15% annual interest
      } else {
        isEligible = false;
        creditScore = 500; // Poor credit score
        eligibleAmount = 0;
        interestRate = 0;
      }
      
      calculateLoanTerms();
    } catch (e) {
      print('Error checking eligibility: $e');
    }
  }

  void calculateLoanTerms() {
    double requestedAmount = double.tryParse(loanAmountController.text) ?? 0;
    
    if (requestedAmount > 0 && requestedAmount <= eligibleAmount) {
      // Calculate monthly payment using simple interest
      double monthlyInterestRate = interestRate / 100 / 12;
      double principal = requestedAmount;
      
      // Simple interest calculation
      double totalInterest = principal * (interestRate / 100) * (selectedDurationMonths / 12);
      totalRepayment = principal + totalInterest;
      
      if (selectedRepaymentFrequency == 'monthly') {
        monthlyRepayment = totalRepayment / selectedDurationMonths;
      } else {
        // Weekly payments
        monthlyRepayment = totalRepayment / (selectedDurationMonths * 4);
      }
    } else {
      monthlyRepayment = 0;
      totalRepayment = 0;
    }
    
    update();
  }

  Future<void> applyForLoan() async {
    if (!validateLoanForm()) return;

    submitLoading = true;
    update();

    try {
      // Simulate loan application
      await Future.delayed(const Duration(seconds: 2));
      
      // Mock successful application
      Get.snackbar(
        'Loan Application Submitted',
        'Your loan application has been submitted successfully. You will receive a response within 24 hours.',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.green.withOpacity(0.1),
        colorText: Colors.green,
        duration: const Duration(seconds: 3),
      );
      
      clearForm();
      Get.back();
      await loadLoanData();
      
    } catch (e) {
      Get.snackbar(
        'Application Failed',
        'Failed to submit loan application. Please try again.',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red.withOpacity(0.1),
        colorText: Colors.red,
      );
    }

    submitLoading = false;
    update();
  }

  bool validateLoanForm() {
    double requestedAmount = double.tryParse(loanAmountController.text) ?? 0;
    
    if (!isEligible) {
      Get.snackbar(
        'Not Eligible',
        'You are not eligible for a loan at this time. Please improve your account activity.',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.orange.withOpacity(0.1),
        colorText: Colors.orange,
      );
      return false;
    }
    
    if (requestedAmount <= 0) {
      Get.snackbar(
        'Invalid Amount',
        'Please enter a valid loan amount.',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red.withOpacity(0.1),
        colorText: Colors.red,
      );
      return false;
    }
    
    if (requestedAmount > eligibleAmount) {
      Get.snackbar(
        'Amount Too High',
        'Requested amount exceeds your eligible limit of ₦${eligibleAmount.toStringAsFixed(0)}',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red.withOpacity(0.1),
        colorText: Colors.red,
      );
      return false;
    }
    
    if (loanPurposeController.text.trim().isEmpty) {
      Get.snackbar(
        'Purpose Required',
        'Please specify the purpose of the loan.',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red.withOpacity(0.1),
        colorText: Colors.red,
      );
      return false;
    }
    
    if (monthlyIncomeController.text.trim().isEmpty) {
      Get.snackbar(
        'Income Required',
        'Please enter your monthly income.',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red.withOpacity(0.1),
        colorText: Colors.red,
      );
      return false;
    }
    
    return true;
  }

  void clearForm() {
    loanAmountController.clear();
    loanPurposeController.clear();
    monthlyIncomeController.clear();
    employerNameController.clear();
    workAddressController.clear();
    guarantorNameController.clear();
    guarantorPhoneController.clear();
    selectedLoanType = LoanType.personal;
    selectedDurationMonths = 6;
    selectedRepaymentFrequency = 'monthly';
  }

  String getLoanTypeText(LoanType type) {
    switch (type) {
      case LoanType.personal:
        return 'Personal Loan';
      case LoanType.business:
        return 'Business Loan';
      case LoanType.emergency:
        return 'Emergency Loan';
    }
  }

  String getLoanStatusText(String status) {
    switch (status.toLowerCase()) {
      case 'pending':
        return 'Pending Review';
      case 'approved':
        return 'Approved';
      case 'rejected':
        return 'Rejected';
      case 'disbursed':
        return 'Disbursed';
      case 'repaying':
        return 'Repaying';
      case 'completed':
        return 'Completed';
      default:
        return 'Unknown';
    }
  }

  Color getLoanStatusColor(String status) {
    switch (status.toLowerCase()) {
      case 'pending':
        return Colors.orange;
      case 'approved':
        return Colors.green;
      case 'rejected':
        return Colors.red;
      case 'disbursed':
        return Colors.blue;
      case 'repaying':
        return Colors.purple;
      case 'completed':
        return Colors.green;
      default:
        return Colors.grey;
    }
  }

  String getCreditScoreText() {
    if (creditScore >= 750) return 'Excellent';
    if (creditScore >= 700) return 'Good';
    if (creditScore >= 650) return 'Fair';
    if (creditScore >= 600) return 'Poor';
    return 'Very Poor';
  }

  Color getCreditScoreColor() {
    if (creditScore >= 750) return Colors.green;
    if (creditScore >= 700) return Colors.lightGreen;
    if (creditScore >= 650) return Colors.orange;
    if (creditScore >= 600) return Colors.deepOrange;
    return Colors.red;
  }
}
