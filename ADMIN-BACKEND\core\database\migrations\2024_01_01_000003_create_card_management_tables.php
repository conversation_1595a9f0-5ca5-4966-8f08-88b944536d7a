<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // User Cards Table
        Schema::create('user_cards', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('user_id');
            $table->enum('card_type', ['physical', 'virtual']);
            $table->enum('account_type', ['personal', 'business'])->default('personal');
            $table->string('card_name');
            $table->string('card_number', 19)->nullable();
            $table->string('masked_card_number', 19)->nullable();
            $table->string('expiry_date', 7)->nullable(); // MM/YYYY format
            $table->string('cvv', 4)->nullable();
            $table->string('pin_hash')->nullable();
            $table->boolean('pin_set')->default(false);
            $table->boolean('pin_locked')->default(false);
            $table->integer('failed_attempts')->default(0);
            $table->decimal('daily_limit', 28, 8)->default(100000);
            $table->decimal('monthly_limit', 28, 8)->default(500000);
            $table->decimal('single_transaction_limit', 28, 8)->default(50000);
            $table->enum('status', ['pending', 'active', 'blocked', 'expired', 'cancelled'])->default('pending');
            $table->text('delivery_address')->nullable();
            $table->enum('delivery_status', ['pending', 'processing', 'shipped', 'delivered'])->nullable();
            $table->string('tracking_number')->nullable();
            $table->decimal('card_fee', 28, 8)->default(0);
            $table->boolean('is_frozen')->default(false);
            $table->timestamp('frozen_at')->nullable();
            $table->timestamps();
            
            $table->foreign('user_id')->references('id')->on('users')->onDelete('cascade');
            $table->index(['user_id', 'status']);
            $table->index(['card_type', 'status']);
        });

        // Card Requests Table
        Schema::create('card_requests', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('user_id');
            $table->enum('card_type', ['physical', 'virtual']);
            $table->enum('account_type', ['personal', 'business'])->default('personal');
            $table->enum('card_category', ['debit', 'prepaid'])->default('debit');
            $table->string('card_name');
            $table->text('delivery_address')->nullable();
            $table->string('delivery_state')->nullable();
            $table->string('delivery_city')->nullable();
            $table->string('phone_number', 20)->nullable();
            $table->decimal('spending_limit', 28, 8)->nullable();
            $table->enum('status', ['pending', 'approved', 'rejected', 'processing', 'delivered'])->default('pending');
            $table->text('rejection_reason')->nullable();
            $table->text('admin_notes')->nullable();
            $table->unsignedBigInteger('processed_by')->nullable(); // Admin ID
            $table->timestamp('processed_at')->nullable();
            $table->timestamps();
            
            $table->foreign('user_id')->references('id')->on('users')->onDelete('cascade');
            $table->foreign('processed_by')->references('id')->on('admins')->onDelete('set null');
            $table->index(['user_id', 'status']);
        });

        // Card Security Settings Table
        Schema::create('card_security_settings', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('card_id');
            $table->boolean('online_transactions_enabled')->default(true);
            $table->boolean('international_transactions_enabled')->default(false);
            $table->boolean('atm_withdrawals_enabled')->default(true);
            $table->boolean('contactless_enabled')->default(true);
            $table->boolean('pos_transactions_enabled')->default(true);
            $table->boolean('web_transactions_enabled')->default(true);
            $table->decimal('single_transaction_limit', 28, 8)->nullable();
            $table->integer('daily_transaction_count')->nullable();
            $table->json('blocked_merchant_categories')->nullable();
            $table->json('allowed_countries')->nullable();
            $table->timestamps();
            
            $table->foreign('card_id')->references('id')->on('user_cards')->onDelete('cascade');
            $table->unique('card_id');
        });

        // Card Transactions Table
        Schema::create('card_transactions', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('card_id');
            $table->unsignedBigInteger('user_id');
            $table->enum('type', ['purchase', 'withdrawal', 'refund', 'fee', 'reversal']);
            $table->decimal('amount', 28, 8);
            $table->string('currency', 3)->default('NGN');
            $table->decimal('exchange_rate', 10, 6)->default(1);
            $table->string('merchant_name')->nullable();
            $table->string('merchant_category')->nullable();
            $table->string('location')->nullable();
            $table->string('country', 2)->nullable();
            $table->enum('channel', ['pos', 'atm', 'online', 'contactless']);
            $table->string('reference')->unique();
            $table->string('authorization_code')->nullable();
            $table->enum('status', ['pending', 'successful', 'failed', 'reversed'])->default('pending');
            $table->string('failure_reason')->nullable();
            $table->decimal('balance_before', 28, 8);
            $table->decimal('balance_after', 28, 8);
            $table->string('trx', 40)->unique();
            $table->timestamps();
            
            $table->foreign('card_id')->references('id')->on('user_cards')->onDelete('cascade');
            $table->foreign('user_id')->references('id')->on('users')->onDelete('cascade');
            $table->index(['card_id', 'status']);
            $table->index(['user_id', 'type']);
            $table->index('trx');
        });

        // Card PIN History Table (for security tracking)
        Schema::create('card_pin_history', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('card_id');
            $table->unsignedBigInteger('user_id');
            $table->enum('action', ['created', 'changed', 'reset', 'locked', 'unlocked']);
            $table->string('ip_address')->nullable();
            $table->string('user_agent')->nullable();
            $table->text('reason')->nullable();
            $table->timestamps();
            
            $table->foreign('card_id')->references('id')->on('user_cards')->onDelete('cascade');
            $table->foreign('user_id')->references('id')->on('users')->onDelete('cascade');
            $table->index(['card_id', 'action']);
        });

        // Card Delivery Tracking Table
        Schema::create('card_delivery_tracking', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('card_id');
            $table->string('tracking_number')->unique();
            $table->enum('status', ['pending', 'picked_up', 'in_transit', 'out_for_delivery', 'delivered', 'failed']);
            $table->string('location')->nullable();
            $table->text('notes')->nullable();
            $table->timestamp('status_updated_at');
            $table->timestamps();
            
            $table->foreign('card_id')->references('id')->on('user_cards')->onDelete('cascade');
            $table->index(['card_id', 'status']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('card_delivery_tracking');
        Schema::dropIfExists('card_pin_history');
        Schema::dropIfExists('card_transactions');
        Schema::dropIfExists('card_security_settings');
        Schema::dropIfExists('card_requests');
        Schema::dropIfExists('user_cards');
    }
};
