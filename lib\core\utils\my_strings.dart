import 'package:viserpay/data/model/language/language_model.dart';

class MyStrings {
  static const String appName = "Viserpay";
  static const String confirm = "Confirm";
  static const String emptyAddMoneyTitle = "No Add Money to Show";
  static const String expectedAgent = "agent";
  static const String or = "or";
  static const String pinCodeMsg = "Pin code must be 4 characters";
  static const String expectedMerchant = "merchant";
  static const String expectedUser = "user";

  static const String qrPermissonTitle = "Please Enable your Camera Permisson";
  static const String qrPermissonSubTitle = "To Make a transaction using a QR code, please grant camera permission.";

  static const String appSettings = "App Settings";
  static const String contactPermissonTitle = "Please Allow contact permisson From your ";
  static const String contactPermissonSubText = "to get your contact list here";
  static const String permantlyLockedPleaselockyourPhoneandTryagain = "Permantly Locked Please lock your Phone and Try again";
  static const String daily = "Daily";
  static const String alreadyUsed = "Used";
  static const String monthly = "Monthly";
  static const String appAutoUPdate = "App auto update";
  static const String seconds = "Seconds";
  static const String fingerPrint = "Fingerprint";
  static const String resetpin = "Reset or Change PIN";
  static const String resetpinSub = "Update your PIN code here";
  static const String enterYouraccountNumber = "Enter your account number";
  static const String enteraccountHolderName = "Enter Account holder name";
  static const String enterYourNewPassCode = "To secure your account enter a passcode";
  static const String confirmNewPassCode = "Please confirm your passcode";
  static const String resetInput = "Reset input";
  static const String personalInformation = "Personal Information";
  static const String myqrCode = "My QR Code";
  static const String securityInformation = "Security Information";
  static const String privacySetting = "Privacy Setting";
  static const String appPreference = "App Preference";
  static const String notificationSettings = "Notification Settings";
  static const String receivePushNotification = "Receive Push Notification";
  static const String receivePushNotificationSubtitle = "Get push notifications in-app to find out whats going on & to get latest information";
  static const String emailNotification = "Email Notifications";
  static const String emailNotificationSubtitle = "Receive email notifications for transaction updates and stay updated on our latest offers.";
  static const String promotionalOffer = "Promotional Offer";
  static const String promotionalOfferSubtitle = "Stay updated with our latest offers through push notifications";
  static const String accountDelete = "Delete Account";
  static const String enterYourPINCode = "Please enter your PIN code";
  static const String inputLockerFor = "Input locked for";
  static const String userNotFound = "User not found";
  static const String continue_ = "Continue";
  static const String to = "To";
  static const String recent = "Recent";
  static const String allContacts = "All Contacts";
  static const String availableBalance = "Available Balance";
  static const String enterUserNameOrNumber = "Enter username or number";
  static const String selectAvailableNumberPlease = "Select a valid number please";
  static const String pleaseSelectOtp = "Please Select otp Field";
  static const String selectOptType = "Select Otp Type";
  static const String savedAccount = "Save Accounts";
  static const String enterYourPIN = "Enter Your PIN";
  static const String enterYourFourDigitPIN = "Enter Your Four Digit PIN";
  static const String newBalance = "New Balance";
  static const String reference = "Reference";
  static const String referenceID = "Reference ID";
  static const String allBank = "All Banks";
  static const String enterBankName = "Enter bank name";
  static const String bankTranfer = "Bank Transfer";
  static const String addBankAccount = "Add New Bank Account";
  static const String addAccount = "ADD ACCOUNT";
  static const String merchantNumber = "Merchant Number";
  static const String merchantName = "Merchant Name";
  static const String enterMerchantNumber = "Enter Merchant Number";
  static const String enterBankHolderName = "Enter Account Holder Name";
  static const String accountHolderName = "Enter Account Holder Name";
  static const String paybill = "Pay Bill";
  static const String payBillHistory = "Pay Bill History";
  static const String billingHistory = "Billing History";
  static const String seeall = "See All";

  static const String billCompletedSuccessFully = "Bill Completed Successfully";
  static const String mobileRecharge = "Mobile Recharge";
  static const String mobileRechargeHistory = "Mobile Recharge History";
  static const String recharge = "Recharge";
  static const String pleaseSelectOperator = "Please select a operator";
  static const String rechargeSuccessMessage = "Recharge Completed Successfully";
  static const String rechargeSuccessSubtitle = "After admin approve your mobile recharge request, it will be completed";
  static const String cashOut = "Cash Out";
  static const String cashOutHistory = "Cash Out History";
  static const String agentNumber = "Agent Number";
  static const String agentName = "Agent Name";
  static const String enterAgentNameOrNumber = "Enter agent name or number";
  static const String tapToScanQrCode = "Tap To Scan QR Code";
  static const String cashOutSuccessMessage = "Successfully complete the cash out process";
  static const String makePaymentSuccess = "Successfully complete the Payment process";
  static const String bankTransfer = "Bank Transfer";
  static const String addNewBank = "Add New Bank";
  static const String bankTransferHistory = "Bank Transfer History";
  static const String payBill = "Pay Bill";
  static const String utility = "Utility";
  static const String utilityBill = "Utility Bill";
  static const String billPaymentTitle = "Bill Payment is ";
  static const String billPaymentSubTitle = "After admin approve your request bill payment will be completed";

  static const String donate = "Donate";
  static const String donated = "Donated";
  static const String donation = "Donations";
  static const String donationHistory = "Donations History";
  static const String enterName = "Enter Name";
  static const String enterYourName = "Enter your name";
  static const String donotWantToDiscloseMyIdentity = "Don’t want to disclose my identity";
  static const String selectAOrganization = "Select your organization";
  static const String updatePin = "UPDATE PIN";
  static const String mobileOperator = "Mobile Operator";
  static const String selectNetwork = "Select recipient’s current mobile network operator";
  static const String receivedMoney = "received_money";
  static const String sendMoney = "Send Money";
  static const String sendMoneyHistory = "Send Money History";
  static const String yourBalanceIsLow = "Insufficient Balance";
  static const String suggestedMerchant = "Suggested Merchant";
  static const String merchant = "Merchant";
  static const String transactionsHistory = "Transactions History";
  static const String allType = "All Type";
  static const String allRemarks = "All Remark";
  static const String shareThisQrCode = "Share This QR Code";
  static const String downloading = "Downloading";
  static const String fileDownloadedSuccess = "File downloaded successfully";
  static const String bankWarningSubtitle = "Are you sure you want to delete this saved account?";
  static const String yes = "Yes";
  static const String no = "No";
  static const String sec = 'Sec';
  static const String myQrCode = "My QR Code";
  static const String qrScan = "QR Scan";
  static const String share = "Share";
  static const String next = "Next";
  static const String beforeCharge = "Before Charge";
  static const String remainingBalance = "Remaining Balance";
  static const String remaining = "Remaining";
  static const String enterTransactionNo = "Enter transaction no.";
  static const String username = "Username";
  static const String pin = "Pin";
  static const String typeYourPassword = "Type your PIN";
  static const String forgotPassword = "Forgot PIN?";
  static const String signIn = "Sign In";
  static const String createAnAccount = "Create an Account";
  static const String firstName = "First Name";
  static const String firstNameIsRequired = "First name is required";
  static const String lastNameIsRequired = "Last name is required";
  static const String lastName = "Last Name";
  static const String last = "Last";
  static const String country = "Country";
  static const String mobileNumber = "Mobile Number";
  static const String confirmPin = "Confirm new PIN";
  static const String signUp = "Sign Up";
  static const String alreadyAccount = "Already have an account?";
  static const String signUpNow = "Sign Up Now";
  static const String email = "Email";
  static const String address = "Address";
  static const String copy = "Copy";
  static const String view = "View";
  static const String kyc = 'KYC';
  static const String kycData = "KYC Data";
  static const String isRequired = 'is required';
  static const String chooseFile = 'Choose File';
  static const String attachment = "Attachment";

  static const String kycUnderReviewMsg = 'Your KYC data is under review';
  static const String kycAlreadyVerifiedMsg = 'You are already verified';
  static const String verify = "Verify";
  static const String tapForBalance = "Tap for Balance";
  static const String addMoney = "Add Money";
  static const String addMoneyInfo = 'Add Money Info';
  static const String rejectReason = 'Reject Reason';
  static const String makePayment = "Make Payment";
  static const String willGet = "You Will Get";
  static const String yourBalanceWillBe = "Your Balance will be";
  static const String home = "Home";
  static const String activity = "Activity";
  static const String kycVerificationRequired = "KYC Verification Required";
  static const String kycVerificationMsg = "Please submit the required KYC information to verify yourself. Otherwise, you couldn't make any withdrawal request.";
  static const String kycPendingMsg = "Thank you for submitting your KYC documents. Our team is currently reviewing the information.";
  static const String transaction = "Transaction";
  static const String privacySettings = "Privacy Settings";
  static const String fingerPrintMsg = "Use your fingerprint for faster access to your $appName account";
  static const String userGuideAndTutorials = "User guide and tutorials";
  static const String yourInformation = "Your Information";
  static const String allOperations = "All Operations";
  static const String transactionLimit = "Transaction Limit";
  static const String transactionId = "Transaction ID";
  static const String addMoneyHistory = "Add Money History";
  static const String agentUsernameHint = "Enter agent username / email";
  static const String amount = "Amount";
  static const String finalAmount = "Final Amount";
  static const String pleaseEnterNumberWithCountrycode = "Please enter number with country code or username";
  static const String amountHint = "Enter amount";
  static const String date = "Date";
  static const String gateWay = "Gateway";
  static const String charge = "Charge";
  static const String status = "Status";
  static const String selectGateway = "Select Gateway";
  static const String payable = "Payable";
  static const String conversionRate = "Conversion Rate";
  static const String in_ = "In";
  static const String proceed = "Proceed";
  static const String merchantUsernameEmailHint = "Enter merchant username / email";
  static const String used = "Used";
  static const String enterYourOTPTitle = "Enter 6 digit verification code";
  static const String sendToYour = "sent to your";
  static const String codeResendIn = "Resend code in";
  static const String codeResend = "Resend code";
  static const String resending = "Resending";
  static const String download = "Download";
  static const String limit = "Limit";
  static const String selectMethod = "Select Method";
  static const String newPin = "New PIN";
  static const String changePin = "Change PIN";
  static const String currentPin = "Current PIN";
  static const String payment_ = "Payment";
  static const String selectOtp = "Select OTP";
  static const String submit = "Submit";
  static const String name = "Name";
  static const String phone = "Phone";
  static const String zipCode = "Zip Code";
  static const String state = "State";
  static const String city = "City";
  static const String editProfile = "Edit Profile";
  static const String updateProfile = "Update Profile";
  static const String selectALanguage = 'Select Language';
  static const String twoFactorAuth = 'Two Factor Authentication';
  static const String twoFactorMsg = 'Enter 6-digit code from your two factor authenticator APP.';
  static const String exitTitle = "Are you sure you want to exit the app?";
  static const String hasUpperLetter = "Has uppercase letter";
  static const String hasLowerLetter = "Has lowercase letter";
  static const String hasDigit = "Has digit";
  static const String hasSpecialChar = "Has special character";
  static const String minSixChar = "Min of 6 characters";
  static const String recoverAccount = "Recover Account";
  static const String receiverUser = "Receiver User";
  static const String fieldErrorMsg = "Please fill out this field";
  static const String resetPassContent = "To Protect your account please provide a strong pin.";
  static const String forgetPasswordSubText = "Enter your phone number below to receive a PIN reset verification code";
  static const String verifyPasswordMobileSubText = "A 6 digits verification code sent to your phone number";
  static const String emailVerification = "Email Verification";
  static const String viaEmailVerify = "We've sent you six-digit verification code via email for email verification";
  static const String didNotReceiveCode = "Didn't receive code?";
  static const String resend = "Resend Code";
  static const String resendCode = "Resend Code";
  static const String smsVerification = "Sms Verification";
  static const String profileComplete = "Profile Complete";
  static const String enterYour = "Enter your";
  static const String history = "History";
  static const String historyFrom = "History From";
  static const String any = 'Any';

  static const String transactionHistory = 'Transaction History';
  static const String trx = 'TRX';
  static const String backToHome = 'Back Home';
  static const String deleteAccount = 'Delete Account';
  static const String deleteYourAccount = 'Delete your Account';
  static const String deleteBottomSheetSubtitle = "You will lose all of your data by deleting your account. This action cannot be undone.";
  static const String areYouSureWantToDeleteAccount = "Are you sure you want to ";
  static const String afterDeleteYouCanBack = "After Yes Your can't  undo yourself";
  static const String successfully = "Successful";
  static const String is_ = "is";
  static const String your = "Your";
  static const String confirmTo = "Confirm to";
  static const String swipeRightToConfirm = "Swipe right to confirm";
  static const String details = 'Details';
  static const String referralLink = "Referral Link";
  static const String total = "Total";
  static const String week = "week";
  static const String type = "Type";
  static const String remark = "Remark";
  static const String profile = "Profile";
  static const String theme = "Theme";
  static const String language = "Language";
  static const String faq = "FAQ";
  static const String light = "Light";
  static const String dark = "Dark";
  static const String transactionNo = "Transaction No.";
  static const String privacyPolicy = "Privacy & Policy";
  static const String resetYourPin = "Reset Your PIN";
  static const String forgetPassword = "Forgot PIN";
  static const String for_ = "for";
  static const String doNotHaveAccount = "Don't have an account?";
  static const String policies = 'Policies';
  static const String verificationFailed = 'Verification Failed';
  static const String emailVerificationFailed = 'Email Verification Failed';
  static const String emailVerificationSuccess = 'Email Verification Success';
  static const String login_ = 'Log in';
  static const String enterYourPassword_ = 'Enter your PIN';
  static const String noDataFound = 'Sorry! there are no data to show';
  static const String noTrnxHistory = 'You have no transaction history.';
  static const String iAgreeWith = "I agree with the";
  static const String createNewPin = "Create new PIN";
  static const String createPinSubText = "Please provide a strong PIN to secure your account";
  static const String enterAmount = "Enter Amount";
  static const String enterReferences = "Enter References";
  static const String enterValidAmount = "Enter Valid Amount";
  static const String enterValidNumber = "Enter Valid Number";
  static const String enteryourAmount = "Enter Your Amount";
  static const String enterAmountMsg = "Please enter an amount";
  static const String hint = "0.0";
  static const String searchByTrxId = "Search by trx id";
  static const String trxId = "Trx Id";
  static const String complete = "Completed";
  static const String cancel = "Cancel";
  static const String time = "Time";
  static const String bankName = "Bank Name";
  static const String accountNumber = "Account Number";
  static const String enteraccountNumber = "Enter Account Number";
  static const String otpVerification = "OTP Verification";
  static const String showMore = "Show More";
  static const String success = 'success';
  static const String logoutSuccessMsg = 'Sign Out Successfully';
  static const String accountDeletedSuccessfully = 'Account deleted successfully';
  static const String invalidEmailMsg = "Enter valid email";
  static const String enterCurrentPass = "Enter your current PIN";
  static const String enterNewPass = "Enter your new PIN";
  static const String kMatchPassError = "PIN doesn't match";
  static const String kFirstNameNullError = "Enter first name";
  static const String kLastNameNullError = "Enter last name";
  static const String kShortUserNameError = "Username must be 6 character";
  static const String phoneNumber = "Phone Number";
  static const String phonenumber = "Phone number";
  static const String passVerification = 'PIN Verification';
  static const String successfullyCodeResend = 'Resend the code successfully';
  static const String resendCodeFail = 'Failed to resend code';
  static const String somethingWentWrong = 'Something went wrong';
  static const String billDownloadingFailed = 'Bill downloading failed';
  static const String verificationSuccess = 'Verification Success';
  static const String verificationCodeResendMsg = 'Verification code resend successfully';
  static const String enterYourUsername = 'Enter your name';
  static const String enterYourname = 'Enter your username';
  static const String successfullyCopied = 'Successfully Copied';

  static const String enterYourEmail = 'Enter your email';
  static const String enterYourPhoneNumber = "Enter your phone number";
  static const String confirmYourPassword = 'Confirm Your PIN';
  static const String smsVerificationMsg = "We've sent you an access code to your phone number for SMS verification";
  static const String loginMsg = "to your ViserPay account";
  static const String selectACountry = "Select a country";
  static const String selectyourCountry = "Select Your country";
  static const String requestFail = "Request Failed";
  static const String invalidUserType = "Invalid user type";
  static const String requestSuccess = "Request Success";
  static const String loginFailedTryAgain = 'Login failed,please try again';
  static const String selectOne = "Select One";
  static const String select = "Select";
  static const String sms = 'SMS';
  static const String noInternet = 'No internet connection';
  static const String retry = "Retry";
  static const String otpFieldEmptyMsg = "Otp field can't be empty";
  static const String secondAgo = 'second ago';
  static const String minutesAgo = 'minutes ago';
  static const String hourAgo = 'hour ago';
  static const String daysAgo = 'days ago';
  static const String justNow = 'just now';
  static const String logout = "Logout";
  static const String menu = "Menu";
  static const String badResponseMsg = 'Bad Response Format!';
  static const String serverError = 'Server Error';
  static const String unAuthorized = 'Unauthorized';
  static const String yourEmail = 'your email';
  static const String passResetMailSendTo = "Pin reset email sent to ";
  static const String depositLimit = "Deposit Limit";
  static const String selectAWallet = "Select a wallet";
  static const String error = 'Error';
  static const String trxType = 'Trx Type';
  static const String approved = "Approved";
  static const String succeed = "Succeed";
  static const String pending = "Pending";
  static const String rejected = "Rejected";
  static const String transactionType = "Transaction Type";
  static const String validAgentMsg = "Valid agent for money out";
  static const String invalidAgentMsg = "Agent not found";
  static const String validMerchantMsg = "Valid merchant for make payment";
  static const String invalidMerchantMsg = "Merchant not found";
  static const String agreePolicyMessage = "You must agree with our privacy & policies";
  static const String newBankSuccessMessage = "New bank added successfully";
  static const String bankTransferSuccessMessage = "Bank Transfer Successfully";
  static const String pinLengthErrorMessage = "Enter your 4 digit pin code";
  static const String pinErrorMessage = "Please Enter pin code";
  static const String notNow = "Not now";
  static const String nosavedAccountFound = "No saved account";
  static const String andresetYourPin = "And Reset your PIN";
  static const String searchCountry = "Search Country";
  static const String qrCodeWrong = "You Entered a Wrong Qr Code";
  static const String scanMerchantQrCode = "Please scan the right merchant QR code for a seamless transaction";
  static const String scanAgentQrCode = "Please scan the right agent QR code for a seamless transaction";
  static const String scanUserQrCode = "Please scan the right user QR code for a seamless transaction";
  static const String tryAgain = "Try Again";
  static const String makePaymentSuccessSubtitle = "After admin approve your payment request, it will be completed.";
  static const String selectAbank = "Select a bank";
  static const String bankTransferSuccessSubtitle = "After the admin approves your bank transfer request, it will be completed.";
  static const String selectAUtility = "Select a Utility";
  static const String paymentOn = "Payment on";
  static const String downloadPermissionMsg = "Please provide download permission";
  static const String noPermissionFound = "No permission found";
  static const String fileNotFound = "File Not Found";
  static const String donationSearchHintText = "Search organizations for donation";
  static const String resetPreference = "Reset preference";
  static const String faqTitle = "Frequently asked questions (FAQ)";
  static const String inc = "Inc";
  static const String privacyPolicyAndUsage = "Privacy Policy and Usage";
  static const String listthetypesofdatatheappcollects = "List the types of data the app collects";
  static const String helpSupport = "Help & Support";
  static const String selectAValidNumber = "Please select a Valid Number";
  static const String sorryThereAreNodataToShow = "Sorry there are no data to show";

  static const String copiedToClipBoard = "Copied to your clipboard!";
  static const String setupKey = "Setup Key";
  static const String addYourAccount = "Add Your Account";
  static const String enable2Fa = "Enable 2FA Security";
  static const String disable2Fa = "Disable 2FA Security";
  static const String useQRCODETips = "Use the QR code or setup key on your Google Authenticator app to add your account.";
  static const String useQRCODETips2 = "Google Authenticator is a multifactor app for mobile devices. It generates timed codes used during the 2-step verification process. To use Google Authenticator, install the Google Authenticator application on your mobile device.";
  static const String twoFaIconMsg = "Manage your 2FA security";
  static const String hasBeen = "has been";

  static const String setupYourFingerPrint = "Setup your FingerPrint";
  static const String auAuthenticatewithYourFingerprin = "Update your fingerprint authentication status";
  static const String pleaseTryagainAfter = "Please try again after ";
  static const String later = "Later";
  static const String unavalableBioMsg = "Make sure your phone support Biometric";
  static const String fingerPrintSubtitleMsg = "Setup your fingerprint & make your account more secure and faster and easy access to your account";
  static const String enablefingerPrint = "Enable Fingerprint";
  static const String disablefingerPrint = "Disable Fingerprint";
  static const String noContactFound = "No contacts found";

  static List<MyLanguageModel> myLanguages = [
    MyLanguageModel(languageName: 'English', countryCode: 'US', languageCode: 'en'),
    MyLanguageModel(languageName: 'Arabic', countryCode: 'SA', languageCode: 'ar'),
  ];

  static RegExp emailValidatorRegExp = RegExp(r"^[a-zA-Z0-9.]+@[a-zA-Z0-9]+\.[a-zA-Z]+");
}
