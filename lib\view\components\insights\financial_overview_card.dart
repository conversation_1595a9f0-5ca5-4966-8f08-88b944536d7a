import 'package:flutter/material.dart';
import 'package:viserpay/core/utils/dimensions.dart';
import 'package:viserpay/core/utils/my_color.dart';
import 'package:viserpay/core/utils/style.dart';
import 'package:viserpay/core/utils/nigerian_currency_utils.dart';
import 'package:viserpay/data/model/insights/insights_model.dart';

class FinancialOverviewCard extends StatefulWidget {
  final FinancialOverview? financialOverview;

  const FinancialOverviewCard({
    super.key,
    this.financialOverview,
  });

  @override
  State<FinancialOverviewCard> createState() => _FinancialOverviewCardState();
}

class _FinancialOverviewCardState extends State<FinancialOverviewCard>
    with TickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _scaleAnimation;
  late Animation<double> _fadeAnimation;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );

    _scaleAnimation = Tween<double>(
      begin: 0.8,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.elasticOut,
    ));

    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeOut,
    ));

    _animationController.forward();
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: _animationController,
      builder: (context, child) {
        return Transform.scale(
          scale: _scaleAnimation.value,
          child: FadeTransition(
            opacity: _fadeAnimation,
            child: Container(
              padding: const EdgeInsets.all(Dimensions.space20),
              decoration: BoxDecoration(
                gradient: MyColor.primaryGradient,
                borderRadius: BorderRadius.circular(Dimensions.cardRadius),
                boxShadow: [
                  BoxShadow(
                    color: MyColor.primaryColor.withOpacity(0.3),
                    blurRadius: 20,
                    offset: const Offset(0, 10),
                  ),
                ],
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Header
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(
                        'Financial Overview',
                        style: semiBoldLarge.copyWith(
                          color: MyColor.colorWhite,
                          fontSize: 18,
                        ),
                      ),
                      Container(
                        padding: const EdgeInsets.all(Dimensions.space8),
                        decoration: BoxDecoration(
                          color: MyColor.colorWhite.withOpacity(0.2),
                          borderRadius: BorderRadius.circular(Dimensions.smallRadius),
                        ),
                        child: const Icon(
                          Icons.analytics,
                          color: MyColor.colorWhite,
                          size: 20,
                        ),
                      ),
                    ],
                  ),

                  const SizedBox(height: Dimensions.space25),

                  // Net Worth
                  _buildMainMetric(
                    label: 'Net Worth',
                    value: widget.financialOverview?.netWorth ?? '0',
                    change: widget.financialOverview?.monthlyChange ?? '0',
                    changePercentage: widget.financialOverview?.changePercentage ?? '0',
                  ),

                  const SizedBox(height: Dimensions.space20),

                  // Metrics Grid
                  Row(
                    children: [
                      Expanded(
                        child: _buildMetricItem(
                          label: 'Total Income',
                          value: widget.financialOverview?.totalIncome ?? '0',
                          icon: Icons.trending_up,
                          color: MyColor.colorGreen,
                        ),
                      ),
                      const SizedBox(width: Dimensions.space15),
                      Expanded(
                        child: _buildMetricItem(
                          label: 'Total Expenses',
                          value: widget.financialOverview?.totalExpenses ?? '0',
                          icon: Icons.trending_down,
                          color: MyColor.colorRed,
                        ),
                      ),
                    ],
                  ),

                  const SizedBox(height: Dimensions.space15),

                  Row(
                    children: [
                      Expanded(
                        child: _buildMetricItem(
                          label: 'Cash Flow',
                          value: widget.financialOverview?.cashFlow ?? '0',
                          icon: Icons.account_balance_wallet,
                          color: MyColor.colorCyan,
                        ),
                      ),
                      const SizedBox(width: Dimensions.space15),
                      Expanded(
                        child: _buildMetricItem(
                          label: 'Savings Rate',
                          value: '${widget.financialOverview?.savingsRate ?? '0'}%',
                          icon: Icons.savings,
                          color: MyColor.colorOrange,
                          isPercentage: true,
                        ),
                      ),
                    ],
                  ),

                  const SizedBox(height: Dimensions.space20),

                  // Financial Health Indicators
                  _buildHealthIndicators(),
                ],
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildMainMetric({
    required String label,
    required String value,
    required String change,
    required String changePercentage,
  }) {
    bool isPositive = !change.startsWith('-');
    
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: regularDefault.copyWith(
            color: MyColor.colorWhite.withOpacity(0.8),
            fontSize: 14,
          ),
        ),
        const SizedBox(height: Dimensions.space8),
        Text(
          NigerianCurrencyUtils.formatNaira(value, useShortForm: true),
          style: boldExtraLarge.copyWith(
            color: MyColor.colorWhite,
            fontSize: 28,
          ),
        ),
        const SizedBox(height: Dimensions.space8),
        Row(
          children: [
            Icon(
              isPositive ? Icons.trending_up : Icons.trending_down,
              color: isPositive ? MyColor.colorGreen : MyColor.colorRed,
              size: 16,
            ),
            const SizedBox(width: Dimensions.space5),
            Text(
              '${NigerianCurrencyUtils.formatNaira(change, useShortForm: true)} ($changePercentage%)',
              style: semiBoldDefault.copyWith(
                color: MyColor.colorWhite.withOpacity(0.9),
                fontSize: 12,
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildMetricItem({
    required String label,
    required String value,
    required IconData icon,
    required Color color,
    bool isPercentage = false,
  }) {
    return Container(
      padding: const EdgeInsets.all(Dimensions.space12),
      decoration: BoxDecoration(
        color: MyColor.colorWhite.withOpacity(0.15),
        borderRadius: BorderRadius.circular(Dimensions.smallRadius),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                icon,
                color: color,
                size: 16,
              ),
              const Spacer(),
            ],
          ),
          const SizedBox(height: Dimensions.space8),
          Text(
            label,
            style: regularSmall.copyWith(
              color: MyColor.colorWhite.withOpacity(0.8),
              fontSize: 10,
            ),
          ),
          const SizedBox(height: Dimensions.space5),
          Text(
            isPercentage 
                ? value 
                : NigerianCurrencyUtils.formatNaira(value, useShortForm: true),
            style: semiBoldDefault.copyWith(
              color: MyColor.colorWhite,
              fontSize: 14,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildHealthIndicators() {
    double savingsRate = double.tryParse(widget.financialOverview?.savingsRate ?? '0') ?? 0;
    double debtRatio = double.tryParse(widget.financialOverview?.debtToIncomeRatio ?? '0') ?? 0;

    return Container(
      padding: const EdgeInsets.all(Dimensions.space15),
      decoration: BoxDecoration(
        color: MyColor.colorWhite.withOpacity(0.1),
        borderRadius: BorderRadius.circular(Dimensions.smallRadius),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Financial Health Indicators',
            style: semiBoldDefault.copyWith(
              color: MyColor.colorWhite,
              fontSize: 14,
            ),
          ),
          const SizedBox(height: Dimensions.space15),
          
          // Savings Rate Indicator
          _buildIndicator(
            label: 'Savings Rate',
            value: savingsRate,
            target: 20.0,
            unit: '%',
          ),
          
          const SizedBox(height: Dimensions.space10),
          
          // Debt to Income Ratio Indicator
          _buildIndicator(
            label: 'Debt to Income Ratio',
            value: debtRatio,
            target: 30.0,
            unit: '%',
            isReverse: true,
          ),
        ],
      ),
    );
  }

  Widget _buildIndicator({
    required String label,
    required double value,
    required double target,
    required String unit,
    bool isReverse = false,
  }) {
    double progress = (value / target).clamp(0.0, 1.0);
    Color progressColor;
    
    if (isReverse) {
      // For debt ratio, lower is better
      if (value <= target * 0.5) {
        progressColor = MyColor.colorGreen;
      } else if (value <= target) {
        progressColor = MyColor.colorOrange;
      } else {
        progressColor = MyColor.colorRed;
      }
    } else {
      // For savings rate, higher is better
      if (value >= target) {
        progressColor = MyColor.colorGreen;
      } else if (value >= target * 0.5) {
        progressColor = MyColor.colorOrange;
      } else {
        progressColor = MyColor.colorRed;
      }
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              label,
              style: regularSmall.copyWith(
                color: MyColor.colorWhite.withOpacity(0.8),
                fontSize: 12,
              ),
            ),
            Text(
              '$value$unit',
              style: semiBoldDefault.copyWith(
                color: MyColor.colorWhite,
                fontSize: 12,
              ),
            ),
          ],
        ),
        const SizedBox(height: Dimensions.space5),
        LinearProgressIndicator(
          value: progress,
          backgroundColor: MyColor.colorWhite.withOpacity(0.2),
          valueColor: AlwaysStoppedAnimation<Color>(progressColor),
          minHeight: 4,
        ),
      ],
    );
  }
}
