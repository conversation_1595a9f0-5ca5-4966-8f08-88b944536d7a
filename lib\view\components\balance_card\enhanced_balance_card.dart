import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';
import 'package:viserpay/core/utils/dimensions.dart';
import 'package:viserpay/core/utils/my_color.dart';
import 'package:viserpay/core/utils/my_strings.dart';
import 'package:viserpay/core/utils/style.dart';

class EnhancedBalanceCard extends StatefulWidget {
  final String balance;
  final String accountNumber;
  final String accountName;
  final bool isBalanceHidden;
  final VoidCallback onToggleBalance;
  final VoidCallback? onAddMoney;
  final VoidCallback? onSendMoney;
  final VoidCallback? onWithdraw;
  final VoidCallback? onMoreOptions;
  final String? accountType;
  final bool isBusinessAccount;

  const EnhancedBalanceCard({
    super.key,
    required this.balance,
    required this.accountNumber,
    required this.accountName,
    required this.isBalanceHidden,
    required this.onToggleBalance,
    this.onAddMoney,
    this.onSendMoney,
    this.onWithdraw,
    this.onMoreOptions,
    this.accountType,
    this.isBusinessAccount = false,
  });

  @override
  State<EnhancedBalanceCard> createState() => _EnhancedBalanceCardState();
}

class _EnhancedBalanceCardState extends State<EnhancedBalanceCard>
    with TickerProviderStateMixin {
  late AnimationController _balanceAnimationController;
  late AnimationController _cardAnimationController;
  late Animation<double> _balanceAnimation;
  late Animation<double> _cardAnimation;

  @override
  void initState() {
    super.initState();
    _balanceAnimationController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );
    _cardAnimationController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );

    _balanceAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _balanceAnimationController,
      curve: Curves.easeInOut,
    ));

    _cardAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _cardAnimationController,
      curve: Curves.elasticOut,
    ));

    _cardAnimationController.forward();
  }

  @override
  void dispose() {
    _balanceAnimationController.dispose();
    _cardAnimationController.dispose();
    super.dispose();
  }

  void _toggleBalance() {
    if (widget.isBalanceHidden) {
      _balanceAnimationController.forward();
    } else {
      _balanceAnimationController.reverse();
    }
    widget.onToggleBalance();
  }

  void _copyAccountNumber() {
    Clipboard.setData(ClipboardData(text: widget.accountNumber));
    Get.snackbar(
      'Copied',
      'Account number copied to clipboard',
      backgroundColor: MyColor.colorGreen,
      colorText: MyColor.colorWhite,
      duration: const Duration(seconds: 2),
      snackPosition: SnackPosition.TOP,
    );
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: _cardAnimation,
      builder: (context, child) {
        return Transform.scale(
          scale: _cardAnimation.value,
          child: Container(
            margin: const EdgeInsets.all(Dimensions.space15),
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(Dimensions.cardRadius),
              boxShadow: [
                BoxShadow(
                  color: widget.isBusinessAccount 
                      ? MyColor.colorCyan.withOpacity(0.3)
                      : MyColor.primaryColor.withOpacity(0.3),
                  blurRadius: 20,
                  offset: const Offset(0, 10),
                ),
              ],
            ),
            child: Container(
              decoration: BoxDecoration(
                gradient: widget.isBusinessAccount
                    ? MyColor.cyanToDarkGradient
                    : LinearGradient(
                        colors: [
                          MyColor.primaryColor,
                          MyColor.primaryColor.withOpacity(0.8),
                        ],
                        begin: Alignment.topLeft,
                        end: Alignment.bottomRight,
                      ),
                borderRadius: BorderRadius.circular(Dimensions.cardRadius),
              ),
              child: Stack(
                children: [
                  // Background Pattern
                  Positioned(
                    top: -50,
                    right: -50,
                    child: Container(
                      width: 150,
                      height: 150,
                      decoration: BoxDecoration(
                        shape: BoxShape.circle,
                        color: MyColor.colorWhite.withOpacity(0.1),
                      ),
                    ),
                  ),
                  Positioned(
                    bottom: -30,
                    left: -30,
                    child: Container(
                      width: 100,
                      height: 100,
                      decoration: BoxDecoration(
                        shape: BoxShape.circle,
                        color: MyColor.colorWhite.withOpacity(0.05),
                      ),
                    ),
                  ),
                  
                  // Main Content
                  Padding(
                    padding: const EdgeInsets.all(Dimensions.space20),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        // Header Row
                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                  widget.isBusinessAccount ? 'Business Account' : 'Personal Account',
                                  style: regularDefault.copyWith(
                                    color: MyColor.colorWhite.withOpacity(0.8),
                                    fontSize: 12,
                                  ),
                                ),
                                const SizedBox(height: Dimensions.space5),
                                Text(
                                  widget.accountName,
                                  style: semiBoldLarge.copyWith(
                                    color: MyColor.colorWhite,
                                    fontSize: 16,
                                  ),
                                ),
                              ],
                            ),
                            Row(
                              children: [
                                GestureDetector(
                                  onTap: _toggleBalance,
                                  child: Container(
                                    padding: const EdgeInsets.all(Dimensions.space8),
                                    decoration: BoxDecoration(
                                      color: MyColor.colorWhite.withOpacity(0.2),
                                      borderRadius: BorderRadius.circular(Dimensions.smallRadius),
                                    ),
                                    child: Icon(
                                      widget.isBalanceHidden 
                                          ? Icons.visibility_off 
                                          : Icons.visibility,
                                      color: MyColor.colorWhite,
                                      size: 18,
                                    ),
                                  ),
                                ),
                                const SizedBox(width: Dimensions.space10),
                                if (widget.onMoreOptions != null)
                                  GestureDetector(
                                    onTap: widget.onMoreOptions,
                                    child: Container(
                                      padding: const EdgeInsets.all(Dimensions.space8),
                                      decoration: BoxDecoration(
                                        color: MyColor.colorWhite.withOpacity(0.2),
                                        borderRadius: BorderRadius.circular(Dimensions.smallRadius),
                                      ),
                                      child: const Icon(
                                        Icons.more_vert,
                                        color: MyColor.colorWhite,
                                        size: 18,
                                      ),
                                    ),
                                  ),
                              ],
                            ),
                          ],
                        ),

                        const SizedBox(height: Dimensions.space25),

                        // Balance Section
                        Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              'Available Balance',
                              style: regularDefault.copyWith(
                                color: MyColor.colorWhite.withOpacity(0.8),
                                fontSize: 12,
                              ),
                            ),
                            const SizedBox(height: Dimensions.space8),
                            AnimatedBuilder(
                              animation: _balanceAnimation,
                              builder: (context, child) {
                                return widget.isBalanceHidden
                                    ? Row(
                                        children: [
                                          Text(
                                            '₦',
                                            style: boldExtraLarge.copyWith(
                                              color: MyColor.colorWhite,
                                              fontSize: 32,
                                            ),
                                          ),
                                          const SizedBox(width: Dimensions.space5),
                                          ...List.generate(
                                            6,
                                            (index) => Container(
                                              margin: const EdgeInsets.only(right: 4),
                                              width: 8,
                                              height: 8,
                                              decoration: const BoxDecoration(
                                                color: MyColor.colorWhite,
                                                shape: BoxShape.circle,
                                              ),
                                            ),
                                          ),
                                        ],
                                      )
                                    : Text(
                                        '₦${_formatBalance(widget.balance)}',
                                        style: boldExtraLarge.copyWith(
                                          color: MyColor.colorWhite,
                                          fontSize: 32,
                                          fontWeight: FontWeight.w700,
                                        ),
                                      );
                              },
                            ),
                          ],
                        ),

                        const SizedBox(height: Dimensions.space20),

                        // Account Number Section
                        GestureDetector(
                          onTap: _copyAccountNumber,
                          child: Container(
                            padding: const EdgeInsets.symmetric(
                              horizontal: Dimensions.space12,
                              vertical: Dimensions.space8,
                            ),
                            decoration: BoxDecoration(
                              color: MyColor.colorWhite.withOpacity(0.15),
                              borderRadius: BorderRadius.circular(Dimensions.smallRadius),
                            ),
                            child: Row(
                              mainAxisSize: MainAxisSize.min,
                              children: [
                                Text(
                                  widget.accountNumber,
                                  style: semiBoldDefault.copyWith(
                                    color: MyColor.colorWhite,
                                    letterSpacing: 1.2,
                                  ),
                                ),
                                const SizedBox(width: Dimensions.space8),
                                const Icon(
                                  Icons.copy,
                                  color: MyColor.colorWhite,
                                  size: 16,
                                ),
                              ],
                            ),
                          ),
                        ),

                        const SizedBox(height: Dimensions.space25),

                        // Quick Actions
                        Row(
                          children: [
                            if (widget.onAddMoney != null)
                              Expanded(
                                child: _buildQuickAction(
                                  icon: Icons.add,
                                  label: 'Add Money',
                                  onTap: widget.onAddMoney!,
                                ),
                              ),
                            if (widget.onAddMoney != null && widget.onSendMoney != null)
                              const SizedBox(width: Dimensions.space10),
                            if (widget.onSendMoney != null)
                              Expanded(
                                child: _buildQuickAction(
                                  icon: Icons.send,
                                  label: 'Send',
                                  onTap: widget.onSendMoney!,
                                ),
                              ),
                            if (widget.onSendMoney != null && widget.onWithdraw != null)
                              const SizedBox(width: Dimensions.space10),
                            if (widget.onWithdraw != null)
                              Expanded(
                                child: _buildQuickAction(
                                  icon: Icons.account_balance,
                                  label: 'Withdraw',
                                  onTap: widget.onWithdraw!,
                                ),
                              ),
                          ],
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildQuickAction({
    required IconData icon,
    required String label,
    required VoidCallback onTap,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.symmetric(
          horizontal: Dimensions.space12,
          vertical: Dimensions.space10,
        ),
        decoration: BoxDecoration(
          color: MyColor.colorWhite.withOpacity(0.2),
          borderRadius: BorderRadius.circular(Dimensions.smallRadius),
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              icon,
              color: MyColor.colorWhite,
              size: 18,
            ),
            const SizedBox(width: Dimensions.space5),
            Text(
              label,
              style: semiBoldDefault.copyWith(
                color: MyColor.colorWhite,
                fontSize: 12,
              ),
            ),
          ],
        ),
      ),
    );
  }

  String _formatBalance(String balance) {
    try {
      double amount = double.parse(balance);
      if (amount >= 1000000) {
        return '${(amount / 1000000).toStringAsFixed(1)}M';
      } else if (amount >= 1000) {
        return '${(amount / 1000).toStringAsFixed(1)}K';
      } else {
        return amount.toStringAsFixed(2);
      }
    } catch (e) {
      return balance;
    }
  }
}
