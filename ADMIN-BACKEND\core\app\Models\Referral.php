<?php

namespace App\Models;

use App\Traits\Searchable;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Carbon\Carbon;

class Referral extends Model
{
    use Searchable;

    protected $fillable = [
        'referrer_id',
        'referee_id',
        'referral_code',
        'status',
        'qualification_date',
        'reward_paid_date'
    ];

    protected $casts = [
        'qualification_date' => 'date',
        'reward_paid_date' => 'date'
    ];

    protected $appends = [
        'is_qualified',
        'days_since_signup',
        'qualification_progress'
    ];

    // Relationships
    public function referrer(): BelongsTo
    {
        return $this->belongsTo(User::class, 'referrer_id');
    }

    public function referee(): BelongsTo
    {
        return $this->belongsTo(User::class, 'referee_id');
    }

    public function rewards(): HasMany
    {
        return $this->hasMany(ReferralReward::class);
    }

    // Accessors
    public function getIsQualifiedAttribute(): bool
    {
        return $this->status === 'qualified' || $this->status === 'rewarded';
    }

    public function getDaysSinceSignupAttribute(): int
    {
        return $this->created_at->diffInDays(now());
    }

    public function getQualificationProgressAttribute(): array
    {
        $referee = $this->referee;
        
        return [
            'kyc_completed' => $referee->kv == 1,
            'first_deposit_made' => $this->hasMinimumDeposit(),
            'account_active_30_days' => $this->days_since_signup >= 30,
            'overall_progress' => $this->calculateOverallProgress()
        ];
    }

    // Scopes
    public function scopePending($query)
    {
        return $query->where('status', 'pending');
    }

    public function scopeQualified($query)
    {
        return $query->where('status', 'qualified');
    }

    public function scopeRewarded($query)
    {
        return $query->where('status', 'rewarded');
    }

    public function scopeExpired($query)
    {
        return $query->where('status', 'expired');
    }

    public function scopeForReferrer($query, $referrerId)
    {
        return $query->where('referrer_id', $referrerId);
    }

    public function scopeForReferee($query, $refereeId)
    {
        return $query->where('referee_id', $refereeId);
    }

    // Methods
    public function checkQualification(): array
    {
        $referee = $this->referee;
        
        $requirements = [
            'kyc_completed' => $referee->kv == 1,
            'first_deposit_made' => $this->hasMinimumDeposit(),
            'account_active_30_days' => $this->days_since_signup >= 30
        ];

        $allMet = array_reduce($requirements, function($carry, $item) {
            return $carry && $item;
        }, true);

        if ($allMet && $this->status === 'pending') {
            $this->qualify();
        }

        return [
            'qualified' => $allMet,
            'requirements' => $requirements,
            'status' => $this->status
        ];
    }

    public function qualify(): bool
    {
        if ($this->status !== 'pending') {
            return false;
        }

        $this->status = 'qualified';
        $this->qualification_date = Carbon::now()->toDateString();
        $this->save();

        // Create rewards for both referrer and referee
        $this->createReferralRewards();

        return true;
    }

    public function expire(): bool
    {
        if ($this->status !== 'pending') {
            return false;
        }

        $this->status = 'expired';
        $this->save();

        return true;
    }

    public function markAsRewarded(): bool
    {
        if ($this->status !== 'qualified') {
            return false;
        }

        $this->status = 'rewarded';
        $this->reward_paid_date = Carbon::now()->toDateString();
        $this->save();

        return true;
    }

    private function hasMinimumDeposit(): bool
    {
        $minimumAmount = 5000; // ₦5,000 minimum deposit
        
        $totalDeposits = \App\Models\SavingsTransaction::where('user_id', $this->referee_id)
                                                      ->where('type', 'deposit')
                                                      ->sum('amount');

        return $totalDeposits >= $minimumAmount;
    }

    private function calculateOverallProgress(): float
    {
        $progress = $this->qualification_progress;
        $completed = 0;
        $total = 3; // Total requirements

        if ($progress['kyc_completed']) $completed++;
        if ($progress['first_deposit_made']) $completed++;
        if ($progress['account_active_30_days']) $completed++;

        return ($completed / $total) * 100;
    }

    private function createReferralRewards(): void
    {
        $referrerReward = 500; // ₦500 for referrer
        $refereeReward = 1000; // ₦1,000 for referee

        // Get referrer's tier multiplier
        $referrerTier = $this->getReferrerTier();
        $referrerReward *= $referrerTier['bonus_multiplier'];

        // Create referrer reward
        ReferralReward::create([
            'user_id' => $this->referrer_id,
            'referral_id' => $this->id,
            'type' => 'referrer_bonus',
            'amount' => $referrerReward,
            'status' => 'pending'
        ]);

        // Create referee reward
        ReferralReward::create([
            'user_id' => $this->referee_id,
            'referral_id' => $this->id,
            'type' => 'referee_bonus',
            'amount' => $refereeReward,
            'status' => 'pending'
        ]);
    }

    private function getReferrerTier(): array
    {
        $referralCount = self::where('referrer_id', $this->referrer_id)
                            ->where('status', 'qualified')
                            ->count();

        if ($referralCount >= 100) {
            return ['name' => 'Diamond', 'level' => 5, 'bonus_multiplier' => 2.0];
        } elseif ($referralCount >= 50) {
            return ['name' => 'Platinum', 'level' => 4, 'bonus_multiplier' => 1.5];
        } elseif ($referralCount >= 25) {
            return ['name' => 'Gold', 'level' => 3, 'bonus_multiplier' => 1.3];
        } elseif ($referralCount >= 10) {
            return ['name' => 'Silver', 'level' => 2, 'bonus_multiplier' => 1.2];
        } else {
            return ['name' => 'Bronze', 'level' => 1, 'bonus_multiplier' => 1.0];
        }
    }

    // Static methods
    public static function processQualificationChecks(): int
    {
        $pendingReferrals = self::pending()
                              ->where('created_at', '<=', Carbon::now()->subDays(30))
                              ->get();

        $qualified = 0;
        $expired = 0;

        foreach ($pendingReferrals as $referral) {
            $check = $referral->checkQualification();
            
            if ($check['qualified']) {
                $qualified++;
            } elseif ($referral->days_since_signup > 60) {
                // Expire after 60 days if not qualified
                $referral->expire();
                $expired++;
            }
        }

        return $qualified;
    }

    public static function getTopReferrers($limit = 10): array
    {
        return self::selectRaw('referrer_id, COUNT(*) as referral_count')
                  ->where('status', 'qualified')
                  ->groupBy('referrer_id')
                  ->orderByDesc('referral_count')
                  ->limit($limit)
                  ->with('referrer:id,firstname,lastname,image')
                  ->get()
                  ->toArray();
    }

    public static function getReferralStats(): array
    {
        return [
            'total_referrals' => self::count(),
            'pending_referrals' => self::where('status', 'pending')->count(),
            'qualified_referrals' => self::where('status', 'qualified')->count(),
            'rewarded_referrals' => self::where('status', 'rewarded')->count(),
            'expired_referrals' => self::where('status', 'expired')->count(),
            'qualification_rate' => self::getQualificationRate(),
            'average_qualification_time' => self::getAverageQualificationTime()
        ];
    }

    private static function getQualificationRate(): float
    {
        $total = self::whereIn('status', ['qualified', 'rewarded', 'expired'])->count();
        $qualified = self::whereIn('status', ['qualified', 'rewarded'])->count();
        
        return $total > 0 ? ($qualified / $total) * 100 : 0;
    }

    private static function getAverageQualificationTime(): float
    {
        $qualifiedReferrals = self::whereNotNull('qualification_date')->get();
        
        if ($qualifiedReferrals->isEmpty()) {
            return 0;
        }

        $totalDays = $qualifiedReferrals->sum(function ($referral) {
            return Carbon::parse($referral->created_at)->diffInDays($referral->qualification_date);
        });

        return $totalDays / $qualifiedReferrals->count();
    }
}
