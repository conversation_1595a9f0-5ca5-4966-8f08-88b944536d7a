<?php

namespace App\Models;

use App\Traits\Searchable;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class SavingsTransaction extends Model
{
    use Searchable;

    protected $fillable = [
        'user_id',
        'savings_type',
        'savings_id',
        'type',
        'amount',
        'balance_before',
        'balance_after',
        'description',
        'trx'
    ];

    protected $casts = [
        'amount' => 'decimal:8',
        'balance_before' => 'decimal:8',
        'balance_after' => 'decimal:8'
    ];

    protected $appends = [
        'formatted_amount',
        'type_badge_class'
    ];

    // Relationships
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    public function savingsRecord()
    {
        switch ($this->savings_type) {
            case 'fixed_deposit':
                return $this->belongsTo(FixedDeposit::class, 'savings_id');
            case 'target_saving':
                return $this->belongsTo(TargetSaving::class, 'savings_id');
            case 'flex_saving':
                return $this->belongsTo(FlexSaving::class, 'savings_id');
            case 'group_saving':
                return $this->belongsTo(GroupSaving::class, 'savings_id');
            default:
                return null;
        }
    }

    // Accessors
    public function getFormattedAmountAttribute(): string
    {
        return '₦' . number_format($this->amount, 2);
    }

    public function getTypeBadgeClassAttribute(): string
    {
        return match($this->type) {
            'deposit' => 'badge-success',
            'withdrawal' => 'badge-warning',
            'interest' => 'badge-info',
            'penalty' => 'badge-danger',
            default => 'badge-secondary'
        };
    }

    // Scopes
    public function scopeForUser($query, $userId)
    {
        return $query->where('user_id', $userId);
    }

    public function scopeByType($query, $type)
    {
        return $query->where('type', $type);
    }

    public function scopeBySavingsType($query, $savingsType)
    {
        return $query->where('savings_type', $savingsType);
    }

    public function scopeDeposits($query)
    {
        return $query->where('type', 'deposit');
    }

    public function scopeWithdrawals($query)
    {
        return $query->where('type', 'withdrawal');
    }

    public function scopeInterest($query)
    {
        return $query->where('type', 'interest');
    }

    public function scopePenalties($query)
    {
        return $query->where('type', 'penalty');
    }

    public function scopeRecent($query, $days = 30)
    {
        return $query->where('created_at', '>=', now()->subDays($days));
    }

    // Static methods
    public static function getTotalSavingsForUser($userId): float
    {
        return self::where('user_id', $userId)
                  ->where('type', 'deposit')
                  ->sum('amount');
    }

    public static function getTotalWithdrawalsForUser($userId): float
    {
        return self::where('user_id', $userId)
                  ->where('type', 'withdrawal')
                  ->sum('amount');
    }

    public static function getTotalInterestEarnedForUser($userId): float
    {
        return self::where('user_id', $userId)
                  ->where('type', 'interest')
                  ->sum('amount');
    }

    public static function getTransactionsByType($userId, $type): \Illuminate\Database\Eloquent\Collection
    {
        return self::where('user_id', $userId)
                  ->where('type', $type)
                  ->orderBy('created_at', 'desc')
                  ->get();
    }

    public static function getTransactionsBySavingsType($userId, $savingsType): \Illuminate\Database\Eloquent\Collection
    {
        return self::where('user_id', $userId)
                  ->where('savings_type', $savingsType)
                  ->orderBy('created_at', 'desc')
                  ->get();
    }

    public static function getSavingsAnalytics($userId, $period = 30): array
    {
        $startDate = now()->subDays($period);
        
        $transactions = self::where('user_id', $userId)
                           ->where('created_at', '>=', $startDate)
                           ->get();

        return [
            'total_deposits' => $transactions->where('type', 'deposit')->sum('amount'),
            'total_withdrawals' => $transactions->where('type', 'withdrawal')->sum('amount'),
            'total_interest' => $transactions->where('type', 'interest')->sum('amount'),
            'total_penalties' => $transactions->where('type', 'penalty')->sum('amount'),
            'net_savings' => $transactions->where('type', 'deposit')->sum('amount') - 
                           $transactions->where('type', 'withdrawal')->sum('amount'),
            'transaction_count' => $transactions->count(),
            'by_savings_type' => [
                'fixed_deposit' => $transactions->where('savings_type', 'fixed_deposit')->sum('amount'),
                'target_saving' => $transactions->where('savings_type', 'target_saving')->sum('amount'),
                'flex_saving' => $transactions->where('savings_type', 'flex_saving')->sum('amount'),
                'group_saving' => $transactions->where('savings_type', 'group_saving')->sum('amount')
            ]
        ];
    }
}
