<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('users', function (Blueprint $table) {
            // Add PIN field if not exists
            if (!Schema::hasColumn('users', 'pin')) {
                $table->string('pin', 255)->nullable()->after('password');
            }
            
            // Add savings balance
            $table->decimal('savings_balance', 28, 8)->default(0)->after('balance');
            
            // Add investment balance
            $table->decimal('investment_balance', 28, 8)->default(0)->after('savings_balance');
            
            // Add loan balance
            $table->decimal('loan_balance', 28, 8)->default(0)->after('investment_balance');
            
            // Add account tier
            $table->unsignedBigInteger('account_tier_id')->nullable()->after('loan_balance');
            
            // Add referral code
            $table->string('referral_code', 20)->unique()->nullable()->after('account_tier_id');
            
            // Add referred by
            $table->unsignedBigInteger('referred_by')->nullable()->after('referral_code');
            
            // Add credit score
            $table->integer('credit_score')->default(500)->after('referred_by');
            
            // Add risk profile
            $table->enum('risk_profile', ['conservative', 'moderate', 'aggressive'])->default('moderate')->after('credit_score');
            
            // Add preferred currency (for future multi-currency support)
            $table->string('preferred_currency', 3)->default('NGN')->after('risk_profile');
            
            // Add timezone
            $table->string('timezone')->default('Africa/Lagos')->after('preferred_currency');
            
            // Add last active date
            $table->timestamp('last_active_at')->nullable()->after('timezone');
            
            // Add BVN for Nigerian KYC
            $table->string('bvn', 11)->nullable()->after('last_active_at');
            
            // Add NIN for Nigerian KYC
            $table->string('nin', 11)->nullable()->after('bvn');
            
            // Add date of birth
            $table->date('date_of_birth')->nullable()->after('nin');
            
            // Add gender
            $table->enum('gender', ['male', 'female', 'other'])->nullable()->after('date_of_birth');
            
            // Add occupation
            $table->string('occupation')->nullable()->after('gender');
            
            // Add monthly income
            $table->decimal('monthly_income', 28, 8)->nullable()->after('occupation');
            
            // Add account opening date
            $table->date('account_opened_at')->nullable()->after('monthly_income');
            
            // Add biometric enabled
            $table->boolean('biometric_enabled')->default(false)->after('account_opened_at');
            
            // Add notification preferences
            $table->boolean('email_notifications')->default(true)->after('biometric_enabled');
            $table->boolean('sms_notifications')->default(true)->after('email_notifications');
            $table->boolean('push_notifications')->default(true)->after('sms_notifications');
            
            // Add foreign key constraints
            $table->foreign('account_tier_id')->references('id')->on('account_tiers')->onDelete('set null');
            $table->foreign('referred_by')->references('id')->on('users')->onDelete('set null');
            
            // Add indexes
            $table->index('referral_code');
            $table->index('credit_score');
            $table->index('account_tier_id');
            $table->index('last_active_at');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('users', function (Blueprint $table) {
            // Drop foreign keys first
            $table->dropForeign(['account_tier_id']);
            $table->dropForeign(['referred_by']);
            
            // Drop indexes
            $table->dropIndex(['referral_code']);
            $table->dropIndex(['credit_score']);
            $table->dropIndex(['account_tier_id']);
            $table->dropIndex(['last_active_at']);
            
            // Drop columns
            $table->dropColumn([
                'savings_balance',
                'investment_balance', 
                'loan_balance',
                'account_tier_id',
                'referral_code',
                'referred_by',
                'credit_score',
                'risk_profile',
                'preferred_currency',
                'timezone',
                'last_active_at',
                'bvn',
                'nin',
                'date_of_birth',
                'gender',
                'occupation',
                'monthly_income',
                'account_opened_at',
                'biometric_enabled',
                'email_notifications',
                'sms_notifications',
                'push_notifications'
            ]);
        });
    }
};
